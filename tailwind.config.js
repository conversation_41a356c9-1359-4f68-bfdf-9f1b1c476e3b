/**
 * 🔄 JobbLogg – Change Log
 * ------------------------
 * 📄 File: tailwind.config.js
 * 📆 Date: 2025-06-26
 * 👤 Agent: AI Assistant
 *
 * ✅ Summary:
 * - Initial Tailwind CSS configuration with daisyUI integration
 * - Configured light and dark themes for daisyUI
 *
 * 🧱 Components/Functions Added:
 * - Tailwind CSS configuration with content paths
 * - daisyUI plugin integration
 * - Theme configuration (light/dark)
 *
 * 🧪 Notes or TODOs:
 * - Configuration ready for production use
 * - May need custom theme extensions later
 */

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [
    require('daisyui'),
  ],
  daisyui: {
    themes: ["light", "dark"],
  },
}

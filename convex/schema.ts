import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  projects: defineTable({
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    sharedId: v.string(),
    createdAt: v.number()
  })
    .index("by_user", ["userId"])
    .index("by_shared_id", ["sharedId"]),

  logEntries: defineTable({
    projectId: v.id("projects"),
    userId: v.string(),
    description: v.string(),
    imageId: v.optional(v.id("_storage")),
    createdAt: v.number()
  })
    .index("by_project", ["projectId"])
    .index("by_user", ["userId"])
    .index("by_project_and_user", ["projectId", "userId"])
});

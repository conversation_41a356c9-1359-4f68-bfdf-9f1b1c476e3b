import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { nanoid } from 'nanoid';

export const create = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    userId: v.string() // Use placeholder "user-1" for now, will integrate Clerk later
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("projects", {
      name: args.name,
      description: args.description,
      userId: args.userId,
      sharedId: nanoid(10),
      createdAt: Date.now()
    });
  }
});

export const getByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();
  }
});

export const getById = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.projectId);
  }
});

/**
 * 🔄 JobbLogg – Change Log
 * ------------------------
 * 📄 File: postcss.config.js
 * 📆 Date: 2025-06-26
 * 👤 Agent: AI Assistant
 *
 * ✅ Summary:
 * - Fixed PostCSS configuration for Tailwind CSS v4 compatibility
 * - Updated to use @tailwindcss/postcss plugin instead of direct tailwindcss
 *
 * 🧱 Components/Functions Added:
 * - PostCSS configuration with @tailwindcss/postcss plugin
 * - Autoprefixer integration
 *
 * 🧪 Notes or TODOs:
 * - Configuration now compatible with Tailwind CSS v4
 * - Resolves PostCSS plugin errors
 */

export default {
  plugins: {
    '@tailwindcss/postcss': {},
    autoprefixer: {},
  },
}

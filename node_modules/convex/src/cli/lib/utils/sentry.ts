import "@sentry/tracing";
import { productionProvisionHost, provisionHost } from "../config.js";
import stripAnsi from "strip-ansi";
import * as Sentry from "@sentry/node";
import { version } from "../../../index.js";

export const SENTRY_DSN =
  "https://<EMAIL>/6390839";

export function initSentry() {
  if (
    (!process.env.CI || process.env.VERCEL === "1") &&
    provisionHost === productionProvisionHost
  ) {
    Sentry.init({
      dsn: SENTRY_DSN,
      release: "cli@" + version,
      tracesSampleRate: 0.2,
      beforeBreadcrumb: (breadcrumb) => {
        // Strip ANSI color codes from log lines that are sent as breadcrumbs.
        if (breadcrumb.message) {
          breadcrumb.message = stripAnsi(breadcrumb.message);
        }
        return breadcrumb;
      },
    });
  }
}

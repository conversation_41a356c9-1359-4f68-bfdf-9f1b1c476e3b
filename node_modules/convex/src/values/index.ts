/**
 * Utilities for working with values stored in Convex.
 *
 * You can see the full set of supported types at
 * [Types](https://docs.convex.dev/using/types).
 * @module
 */

export { convexToJson, jsonToConvex } from "./value.js";
export type {
  Id as GenericId,
  JSONValue,
  Value,
  NumericValue,
} from "./value.js";
export { v, asObjectValidator } from "./validator.js";
export type {
  AsObjectValidator,
  GenericValidator,
  ObjectType,
  PropertyValidators,
} from "./validator.js";
export type {
  ValidatorJSON,
  RecordKeyValidatorJSON,
  RecordValueValidatorJSON,
  ObjectFieldType,
  Validator,
  OptionalProperty,
  VId,
  VFloat64,
  VInt64,
  VBoolean,
  VBytes,
  VString,
  VNull,
  VAny,
  VObject,
  VLiteral,
  VArray,
  VRecord,
  VUnion,
  VOptional,
} from "./validators.js";
import * as Base64 from "./base64.js";
export { Base64 };
export type { Infer } from "./validator.js";
export * from "./errors.js";
export { compareValues } from "./compare.js";

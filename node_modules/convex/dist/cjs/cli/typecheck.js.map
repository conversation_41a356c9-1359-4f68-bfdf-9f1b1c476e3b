{"version": 3, "sources": ["../../../src/cli/typecheck.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { functionsDir, ensureHasConvexDependency } from \"./lib/utils/utils.js\";\nimport { Command } from \"@commander-js/extra-typings\";\nimport { readConfig } from \"./lib/config.js\";\nimport { typeCheckFunctions } from \"./lib/typecheck.js\";\nimport {\n  logFinishedStep,\n  logMessage,\n  oneoffContext,\n} from \"../bundler/context.js\";\n\n// Experimental (it's going to fail sometimes) TypeScript type checking.\n// Includes a separate command to help users debug their TypeScript configs.\n\nexport type TypecheckResult = \"cantTypeCheck\" | \"success\" | \"typecheckFailed\";\n\n/** Run the TypeScript compiler, as configured during  */\nexport const typecheck = new Command(\"typecheck\")\n  .description(\n    \"Run TypeScript typechecking on your Convex functions with `tsc --noEmit`.\",\n  )\n  .allowExcessArguments(false)\n  .action(async () => {\n    const ctx = await oneoffContext({\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    const { configPath, config: localConfig } = await readConfig(ctx, false);\n    await ensureHasConvexDependency(ctx, \"typecheck\");\n    await typeCheckFunctions(\n      ctx,\n      functionsDir(configPath, localConfig.projectConfig),\n      async (typecheckResult, logSpecificError, runOnError) => {\n        logSpecificError?.();\n        if (typecheckResult === \"typecheckFailed\") {\n          logMessage(ctx, chalk.gray(\"Typecheck failed\"));\n          try {\n            await runOnError?.();\n            // If runOnError doesn't throw then it worked the second time.\n            // No errors to report, but it's still a failure.\n          } catch {\n            // As expected, `runOnError` threw\n          }\n          return await ctx.crash({\n            exitCode: 1,\n            errorType: \"invalid filesystem data\",\n            printedMessage: null,\n          });\n        } else if (typecheckResult === \"cantTypeCheck\") {\n          logMessage(\n            ctx,\n            chalk.gray(\"Unable to typecheck; is TypeScript installed?\"),\n          );\n          return await ctx.crash({\n            exitCode: 1,\n            errorType: \"invalid filesystem data\",\n            printedMessage: null,\n          });\n        } else {\n          logFinishedStep(\n            ctx,\n            \"Typecheck passed: `tsc --noEmit` completed with exit code 0.\",\n          );\n          return await ctx.flushAndExit(0);\n        }\n      },\n    );\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,mBAAwD;AACxD,2BAAwB;AACxB,oBAA2B;AAC3B,uBAAmC;AACnC,qBAIO;AAQA,MAAM,YAAY,IAAI,6BAAQ,WAAW,EAC7C;AAAA,EACC;AACF,EACC,qBAAqB,KAAK,EAC1B,OAAO,YAAY;AAClB,QAAM,MAAM,UAAM,8BAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,EAAE,YAAY,QAAQ,YAAY,IAAI,UAAM,0BAAW,KAAK,KAAK;AACvE,YAAM,wCAA0B,KAAK,WAAW;AAChD,YAAM;AAAA,IACJ;AAAA,QACA,2BAAa,YAAY,YAAY,aAAa;AAAA,IAClD,OAAO,iBAAiB,kBAAkB,eAAe;AACvD,yBAAmB;AACnB,UAAI,oBAAoB,mBAAmB;AACzC,uCAAW,KAAK,aAAAA,QAAM,KAAK,kBAAkB,CAAC;AAC9C,YAAI;AACF,gBAAM,aAAa;AAAA,QAGrB,QAAQ;AAAA,QAER;AACA,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH,WAAW,oBAAoB,iBAAiB;AAC9C;AAAA,UACE;AAAA,UACA,aAAAA,QAAM,KAAK,+CAA+C;AAAA,QAC5D;AACA,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH,OAAO;AACL;AAAA,UACE;AAAA,UACA;AAAA,QACF;AACA,eAAO,MAAM,IAAI,aAAa,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF,CAAC;", "names": ["chalk"]}
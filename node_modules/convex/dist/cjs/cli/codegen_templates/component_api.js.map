{"version": 3, "sources": ["../../../../src/cli/codegen_templates/component_api.ts"], "sourcesContent": ["import path from \"path\";\nimport { Context } from \"../../bundler/context.js\";\nimport { entryPoints } from \"../../bundler/index.js\";\nimport {\n  ComponentDirectory,\n  toComponentDefinitionPath,\n} from \"../lib/components/definition/directoryStructure.js\";\nimport { StartPushResponse } from \"../lib/deployApi/startPush.js\";\nimport { importPath, moduleIdentifier } from \"./api.js\";\nimport { header } from \"./common.js\";\nimport {\n  ComponentExports,\n  EvaluatedComponentDefinition,\n} from \"../lib/deployApi/componentDefinition.js\";\nimport { ComponentDefinitionPath } from \"../lib/deployApi/paths.js\";\nimport { Identifier, Reference } from \"../lib/deployApi/types.js\";\nimport { CanonicalizedModulePath } from \"../lib/deployApi/paths.js\";\nimport {\n  AnalyzedFunction,\n  AnalyzedModule,\n  Visibility,\n} from \"../lib/deployApi/modules.js\";\nimport { parseValidator, validatorToType } from \"./validator_helpers.js\";\n\nexport function componentApiJs() {\n  const lines = [];\n  lines.push(header(\"Generated `api` utility.\"));\n  lines.push(`\n    import { anyApi, componentsGeneric } from \"convex/server\";\n\n    /**\n     * A utility for referencing Convex functions in your app's API.\n     *\n     * Usage:\n     * \\`\\`\\`js\n     * const myFunctionReference = api.myModule.myFunction;\n     * \\`\\`\\`\n     */\n    export const api = anyApi;\n    export const internal = anyApi;\n    export const components = componentsGeneric();\n  `);\n  return lines.join(\"\\n\");\n}\n\nexport function rootComponentApiCJS() {\n  const lines = [];\n  lines.push(header(\"Generated `api` utility.\"));\n  lines.push(`const { anyApi } = require(\"convex/server\");`);\n  lines.push(`module.exports = {\n    api: anyApi,\n    internal: anyApi,\n  };`);\n  return lines.join(\"\\n\");\n}\n\nexport function componentApiStubDTS() {\n  const lines = [];\n  lines.push(header(\"Generated `api` utility.\"));\n  lines.push(`import type { AnyApi, AnyComponents } from \"convex/server\";`);\n  lines.push(`\n    export declare const api: AnyApi;\n    export declare const internal: AnyApi;\n    export declare const components: AnyComponents;\n  `);\n\n  return lines.join(\"\\n\");\n}\n\nexport async function componentApiDTS(\n  ctx: Context,\n  startPush: StartPushResponse,\n  rootComponent: ComponentDirectory,\n  componentDirectory: ComponentDirectory,\n  opts: { staticApi: boolean },\n) {\n  const definitionPath = toComponentDefinitionPath(\n    rootComponent,\n    componentDirectory,\n  );\n\n  const analysis = startPush.analysis[definitionPath];\n  if (!analysis) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `No analysis found for component ${definitionPath} orig: ${definitionPath}\\nin\\n${Object.keys(startPush.analysis).toString()}`,\n    });\n  }\n\n  const lines = [];\n  lines.push(header(\"Generated `api` utility.\"));\n  let apiLines: AsyncGenerator<string>;\n  if (opts.staticApi) {\n    apiLines = codegenStaticApiObjects(ctx, analysis);\n  } else {\n    apiLines = codegenDynamicApiObjects(\n      ctx,\n      componentDirectory,\n      startPush,\n      definitionPath,\n    );\n  }\n  for await (const line of apiLines) {\n    lines.push(line);\n  }\n\n  lines.push(`\n  export declare const components: {`);\n  for (const childComponent of analysis.definition.childComponents) {\n    const childComponentAnalysis = startPush.analysis[childComponent.path];\n    if (!childComponentAnalysis) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `No analysis found for child component ${childComponent.path}`,\n      });\n    }\n    for await (const line of codegenExports(\n      ctx,\n      childComponent.name,\n      childComponentAnalysis,\n    )) {\n      lines.push(line);\n    }\n  }\n\n  lines.push(\"};\");\n\n  return lines.join(\"\\n\");\n}\n\nasync function* codegenStaticApiObjects(\n  ctx: Context,\n  analysis: EvaluatedComponentDefinition,\n) {\n  yield `import type { FunctionReference } from \"convex/server\";`;\n\n  const apiTree = await buildApiTree(ctx, analysis.functions, {\n    kind: \"public\",\n  });\n  yield `\n  /**\n   * A utility for referencing Convex functions in your app's public API.\n   *\n   * Usage:\n   * \\`\\`\\`js\n   * const myFunctionReference = api.myModule.myFunction;\n   * \\`\\`\\`\n   */`;\n  yield `export declare const api:`;\n  yield* codegenApiTree(ctx, apiTree);\n  yield \";\";\n\n  yield `\n  /**\n   * A utility for referencing Convex functions in your app's internal API.\n   *\n   * Usage:\n   * \\`\\`\\`js\n   * const myFunctionReference = internal.myModule.myFunction;\n   * \\`\\`\\`\n   */`;\n  const internalTree = await buildApiTree(ctx, analysis.functions, {\n    kind: \"internal\",\n  });\n  yield `export declare const internal:`;\n  yield* codegenApiTree(ctx, internalTree);\n  yield \";\";\n}\n\nasync function* codegenDynamicApiObjects(\n  ctx: Context,\n  componentDirectory: ComponentDirectory,\n  startPush: StartPushResponse,\n  definitionPath: ComponentDefinitionPath,\n) {\n  const absModulePaths = await entryPoints(ctx, componentDirectory.path);\n  const modulePaths = absModulePaths.map((p) =>\n    path.relative(componentDirectory.path, p),\n  );\n  for (const modulePath of modulePaths) {\n    const ident = moduleIdentifier(modulePath);\n    const path = importPath(modulePath);\n    yield `import type * as ${ident} from \"../${path}.js\";`;\n  }\n  yield `\n    import type {\n      ApiFromModules,\n      FilterApi,\n      FunctionReference,\n    } from \"convex/server\";\n\n    /**\n     * A utility for referencing Convex functions in your app's API.\n     *\n     * Usage:\n     * \\`\\`\\`js\n     * const myFunctionReference = api.myModule.myFunction;\n     * \\`\\`\\`\n     */\n    declare const fullApi: ApiFromModules<{\n  `;\n  for (const modulePath of modulePaths) {\n    const ident = moduleIdentifier(modulePath);\n    const path = importPath(modulePath);\n    yield `  \"${path}\": typeof ${ident},`;\n  }\n  yield `}>;`;\n  yield* codegenApiWithMounts(ctx, startPush, definitionPath);\n  yield `\n    export declare const api: FilterApi<typeof fullApiWithMounts, FunctionReference<any, \"public\">>;\n    export declare const internal: FilterApi<typeof fullApiWithMounts, FunctionReference<any, \"internal\">>;\n  `;\n}\n\ninterface ApiTree {\n  [identifier: string]:\n    | { type: \"branch\"; branch: ApiTree }\n    | { type: \"leaf\"; leaf: AnalyzedFunction };\n}\n\nasync function buildApiTree(\n  ctx: Context,\n  functions: Record<CanonicalizedModulePath, AnalyzedModule>,\n  visibility: Visibility,\n): Promise<ApiTree> {\n  const root: ApiTree = {};\n  for (const [modulePath, module] of Object.entries(functions)) {\n    const p = importPath(modulePath);\n    if (p.startsWith(\"_deps/\")) {\n      continue;\n    }\n    for (const f of module.functions) {\n      if (f.visibility?.kind !== visibility.kind) {\n        continue;\n      }\n      let current = root;\n      for (const pathComponent of p.split(\"/\")) {\n        let next = current[pathComponent];\n        if (!next) {\n          next = { type: \"branch\", branch: {} };\n          current[pathComponent] = next;\n        }\n        if (next.type === \"leaf\") {\n          return await ctx.crash({\n            exitCode: 1,\n            errorType: \"fatal\",\n            printedMessage: `Ambiguous function name: ${f.name} in ${modulePath}`,\n          });\n        }\n        current = next.branch;\n      }\n      if (current[f.name]) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `Duplicate function name: ${f.name} in ${modulePath}`,\n        });\n      }\n      current[f.name] = { type: \"leaf\", leaf: f };\n    }\n  }\n  return root;\n}\n\nasync function* codegenApiTree(\n  ctx: Context,\n  tree: ApiTree,\n): AsyncGenerator<string> {\n  yield \"{\";\n  for (const [identifier, subtree] of Object.entries(tree)) {\n    if (subtree.type === \"branch\") {\n      yield `\"${identifier}\":`;\n      yield* codegenApiTree(ctx, subtree.branch);\n      yield \",\";\n    } else {\n      const visibility = subtree.leaf.visibility?.kind;\n      if (!visibility) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `Function ${subtree.leaf.name} has no visibility`,\n        });\n      }\n      const ref = await codegenFunctionReference(\n        ctx,\n        subtree.leaf,\n        visibility,\n        true,\n      );\n      yield `\"${identifier}\": ${ref},`;\n    }\n  }\n  yield \"}\";\n}\n\nasync function* codegenApiWithMounts(\n  ctx: Context,\n  startPush: StartPushResponse,\n  definitionPath: ComponentDefinitionPath,\n): AsyncGenerator<string> {\n  const mountTree = await buildMountTree(ctx, startPush, definitionPath, []);\n  if (mountTree) {\n    yield \"export type Mounts = \";\n    yield* codegenMountTree(mountTree);\n    yield `;`;\n    yield `// For now fullApiWithMounts is only fullApi which provides`;\n    yield `// jump-to-definition in component client code.`;\n    yield `// Use Mounts for the same type without the inference.`;\n    yield \"declare const fullApiWithMounts: typeof fullApi;\";\n  } else {\n    yield \"declare const fullApiWithMounts: typeof fullApi;\";\n  }\n}\n\nfunction* codegenMountTree(tree: MountTree): Generator<string> {\n  yield `{`;\n  for (const [identifier, subtree] of Object.entries(tree)) {\n    if (typeof subtree === \"string\") {\n      yield `\"${identifier}\": ${subtree},`;\n    } else {\n      yield `\"${identifier}\":`;\n      yield* codegenMountTree(subtree);\n      yield `,`;\n    }\n  }\n  yield `}`;\n}\n\ninterface MountTree {\n  [identifier: string]: MountTree | string;\n}\n\nasync function buildMountTree(\n  ctx: Context,\n  startPush: StartPushResponse,\n  definitionPath: ComponentDefinitionPath,\n  attributes: string[],\n): Promise<MountTree | null> {\n  // TODO make these types more precise when receiving analysis from server\n  const analysis = startPush.analysis[definitionPath];\n  if (!analysis) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `No analysis found for component ${definitionPath} orig: ${definitionPath}\\nin\\n${Object.keys(startPush.analysis).toString()}`,\n    });\n  }\n  let current = analysis.definition.exports.branch;\n  for (const attribute of attributes) {\n    const componentExport = current.find(\n      ([identifier]) => identifier === attribute,\n    );\n    if (!componentExport) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `No export found for ${attribute}`,\n      });\n    }\n    const [_, node] = componentExport;\n    if (node.type !== \"branch\") {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Expected branch at ${attribute}`,\n      });\n    }\n    current = node.branch;\n  }\n  return buildComponentMountTree(ctx, startPush, analysis, current);\n}\n\nasync function buildComponentMountTree(\n  ctx: Context,\n  startPush: StartPushResponse,\n  analysis: EvaluatedComponentDefinition,\n  exports: Array<[Identifier, ComponentExports]>,\n): Promise<MountTree | null> {\n  const result: MountTree = {};\n  let nonEmpty = false;\n  for (const [identifier, componentExport] of exports) {\n    if (componentExport.type === \"leaf\") {\n      // If we're at a child component reference, follow it and build its export tree.\n      if (componentExport.leaf.startsWith(\"_reference/childComponent/\")) {\n        const suffix = componentExport.leaf.slice(\n          \"_reference/childComponent/\".length,\n        );\n        const [componentName, ...attributes] = suffix.split(\"/\");\n        const childComponent = analysis.definition.childComponents.find(\n          (c) => c.name === componentName,\n        );\n        if (!childComponent) {\n          return await ctx.crash({\n            exitCode: 1,\n            errorType: \"fatal\",\n            printedMessage: `No child component found for ${componentName}`,\n          });\n        }\n        const childTree = await buildMountTree(\n          ctx,\n          startPush,\n          childComponent.path,\n          attributes,\n        );\n        if (childTree) {\n          result[identifier] = childTree;\n          nonEmpty = true;\n        }\n      }\n      // If we're at a function reference outside the root, codegen it as a leaf.\n      const isRoot = analysis.definition.definitionType.type === \"app\";\n      if (!isRoot && componentExport.leaf.startsWith(\"_reference/function/\")) {\n        const leaf = await resolveFunctionReference(\n          ctx,\n          analysis,\n          componentExport.leaf,\n          \"public\",\n        );\n        result[identifier] = leaf;\n        nonEmpty = true;\n      }\n    } else {\n      const subTree = await buildComponentMountTree(\n        ctx,\n        startPush,\n        analysis,\n        componentExport.branch,\n      );\n      if (subTree) {\n        result[identifier] = subTree;\n        nonEmpty = true;\n      }\n    }\n  }\n  return nonEmpty ? result : null;\n}\n\nasync function* codegenExports(\n  ctx: Context,\n  name: Identifier,\n  analysis: EvaluatedComponentDefinition,\n): AsyncGenerator<string> {\n  yield `${name}: {`;\n  for (const [name, componentExport] of analysis.definition.exports.branch) {\n    yield `${name}:`;\n    yield* codegenExport(ctx, analysis, componentExport);\n    yield \",\";\n  }\n  yield \"},\";\n}\n\nasync function* codegenExport(\n  ctx: Context,\n  analysis: EvaluatedComponentDefinition,\n  componentExport: ComponentExports,\n): AsyncGenerator<string> {\n  if (componentExport.type === \"leaf\") {\n    yield await resolveFunctionReference(\n      ctx,\n      analysis,\n      componentExport.leaf,\n      \"internal\",\n    );\n  } else if (componentExport.type === \"branch\") {\n    yield \"{\";\n    for (const [name, childExport] of componentExport.branch) {\n      yield `${name}:`;\n      yield* codegenExport(ctx, analysis, childExport);\n      yield \",\";\n    }\n    yield \"}\";\n  }\n}\n\nexport async function resolveFunctionReference(\n  ctx: Context,\n  analysis: EvaluatedComponentDefinition,\n  reference: Reference,\n  visibility: \"public\" | \"internal\",\n) {\n  if (!reference.startsWith(\"_reference/function/\")) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Invalid function reference: ${reference}`,\n    });\n  }\n  const udfPath = reference.slice(\"_reference/function/\".length);\n\n  const [modulePath, functionName] = udfPath.split(\":\");\n  const canonicalizedModulePath = canonicalizeModulePath(modulePath);\n\n  const analyzedModule = analysis.functions[canonicalizedModulePath];\n  if (!analyzedModule) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Module not found: ${modulePath}`,\n    });\n  }\n  const analyzedFunction = analyzedModule.functions.find(\n    (f) => f.name === functionName,\n  );\n  if (!analyzedFunction) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Function not found: ${functionName}`,\n    });\n  }\n  return await codegenFunctionReference(\n    ctx,\n    analyzedFunction,\n    visibility,\n    false,\n  );\n}\n\nasync function codegenFunctionReference(\n  ctx: Context,\n  analyzedFunction: AnalyzedFunction,\n  visibility: \"public\" | \"internal\",\n  useIdType: boolean,\n): Promise<string> {\n  // The server sends down `udfType` capitalized.\n  const udfType = analyzedFunction.udfType.toLowerCase();\n\n  let argsType = \"any\";\n  try {\n    const argsValidator = parseValidator(analyzedFunction.args);\n    if (argsValidator) {\n      if (argsValidator.type === \"object\" || argsValidator.type === \"any\") {\n        argsType = validatorToType(argsValidator, useIdType);\n      } else {\n        // eslint-disable-next-line no-restricted-syntax\n        throw new Error(\n          `Unexpected argument validator type: ${argsValidator.type}`,\n        );\n      }\n    }\n  } catch (e) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Invalid function args: ${analyzedFunction.args}`,\n      errForSentry: e,\n    });\n  }\n\n  let returnsType = \"any\";\n  try {\n    const returnsValidator = parseValidator(analyzedFunction.returns);\n    if (returnsValidator) {\n      returnsType = validatorToType(returnsValidator, useIdType);\n    }\n  } catch (e) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Invalid function returns: ${analyzedFunction.returns}`,\n      errForSentry: e,\n    });\n  }\n\n  return `FunctionReference<\"${udfType}\", \"${visibility}\", ${argsType}, ${returnsType}>`;\n}\n\nfunction canonicalizeModulePath(modulePath: string): CanonicalizedModulePath {\n  if (!modulePath.endsWith(\".js\")) {\n    return modulePath + \".js\";\n  }\n  return modulePath;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AAEjB,qBAA4B;AAC5B,gCAGO;AAEP,iBAA6C;AAC7C,oBAAuB;AAavB,+BAAgD;AAEzC,SAAS,iBAAiB;AAC/B,QAAM,QAAQ,CAAC;AACf,QAAM,SAAK,sBAAO,0BAA0B,CAAC;AAC7C,QAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAcV;AACD,SAAO,MAAM,KAAK,IAAI;AACxB;AAEO,SAAS,sBAAsB;AACpC,QAAM,QAAQ,CAAC;AACf,QAAM,SAAK,sBAAO,0BAA0B,CAAC;AAC7C,QAAM,KAAK,8CAA8C;AACzD,QAAM,KAAK;AAAA;AAAA;AAAA,KAGR;AACH,SAAO,MAAM,KAAK,IAAI;AACxB;AAEO,SAAS,sBAAsB;AACpC,QAAM,QAAQ,CAAC;AACf,QAAM,SAAK,sBAAO,0BAA0B,CAAC;AAC7C,QAAM,KAAK,6DAA6D;AACxE,QAAM,KAAK;AAAA;AAAA;AAAA;AAAA,GAIV;AAED,SAAO,MAAM,KAAK,IAAI;AACxB;AAEA,eAAsB,gBACpB,KACA,WACA,eACA,oBACA,MACA;AACA,QAAM,qBAAiB;AAAA,IACrB;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAW,UAAU,SAAS,cAAc;AAClD,MAAI,CAAC,UAAU;AACb,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,mCAAmC,cAAc,UAAU,cAAc;AAAA;AAAA,EAAS,OAAO,KAAK,UAAU,QAAQ,EAAE,SAAS,CAAC;AAAA,IAC9I,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,CAAC;AACf,QAAM,SAAK,sBAAO,0BAA0B,CAAC;AAC7C,MAAI;AACJ,MAAI,KAAK,WAAW;AAClB,eAAW,wBAAwB,KAAK,QAAQ;AAAA,EAClD,OAAO;AACL,eAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,mBAAiB,QAAQ,UAAU;AACjC,UAAM,KAAK,IAAI;AAAA,EACjB;AAEA,QAAM,KAAK;AAAA,qCACwB;AACnC,aAAW,kBAAkB,SAAS,WAAW,iBAAiB;AAChE,UAAM,yBAAyB,UAAU,SAAS,eAAe,IAAI;AACrE,QAAI,CAAC,wBAAwB;AAC3B,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,yCAAyC,eAAe,IAAI;AAAA,MAC9E,CAAC;AAAA,IACH;AACA,qBAAiB,QAAQ;AAAA,MACvB;AAAA,MACA,eAAe;AAAA,MACf;AAAA,IACF,GAAG;AACD,YAAM,KAAK,IAAI;AAAA,IACjB;AAAA,EACF;AAEA,QAAM,KAAK,IAAI;AAEf,SAAO,MAAM,KAAK,IAAI;AACxB;AAEA,gBAAgB,wBACd,KACA,UACA;AACA,QAAM;AAEN,QAAM,UAAU,MAAM,aAAa,KAAK,SAAS,WAAW;AAAA,IAC1D,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASN,QAAM;AACN,SAAO,eAAe,KAAK,OAAO;AAClC,QAAM;AAEN,QAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASN,QAAM,eAAe,MAAM,aAAa,KAAK,SAAS,WAAW;AAAA,IAC/D,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AACN,SAAO,eAAe,KAAK,YAAY;AACvC,QAAM;AACR;AAEA,gBAAgB,yBACd,KACA,oBACA,WACA,gBACA;AACA,QAAM,iBAAiB,UAAM,4BAAY,KAAK,mBAAmB,IAAI;AACrE,QAAM,cAAc,eAAe;AAAA,IAAI,CAAC,MACtC,YAAAA,QAAK,SAAS,mBAAmB,MAAM,CAAC;AAAA,EAC1C;AACA,aAAW,cAAc,aAAa;AACpC,UAAM,YAAQ,6BAAiB,UAAU;AACzC,UAAMA,YAAO,uBAAW,UAAU;AAClC,UAAM,oBAAoB,KAAK,aAAaA,KAAI;AAAA,EAClD;AACA,QAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBN,aAAW,cAAc,aAAa;AACpC,UAAM,YAAQ,6BAAiB,UAAU;AACzC,UAAMA,YAAO,uBAAW,UAAU;AAClC,UAAM,MAAMA,KAAI,aAAa,KAAK;AAAA,EACpC;AACA,QAAM;AACN,SAAO,qBAAqB,KAAK,WAAW,cAAc;AAC1D,QAAM;AAAA;AAAA;AAAA;AAIR;AAQA,eAAe,aACb,KACA,WACA,YACkB;AAClB,QAAM,OAAgB,CAAC;AACvB,aAAW,CAAC,YAAYC,OAAM,KAAK,OAAO,QAAQ,SAAS,GAAG;AAC5D,UAAM,QAAI,uBAAW,UAAU;AAC/B,QAAI,EAAE,WAAW,QAAQ,GAAG;AAC1B;AAAA,IACF;AACA,eAAW,KAAKA,QAAO,WAAW;AAChC,UAAI,EAAE,YAAY,SAAS,WAAW,MAAM;AAC1C;AAAA,MACF;AACA,UAAI,UAAU;AACd,iBAAW,iBAAiB,EAAE,MAAM,GAAG,GAAG;AACxC,YAAI,OAAO,QAAQ,aAAa;AAChC,YAAI,CAAC,MAAM;AACT,iBAAO,EAAE,MAAM,UAAU,QAAQ,CAAC,EAAE;AACpC,kBAAQ,aAAa,IAAI;AAAA,QAC3B;AACA,YAAI,KAAK,SAAS,QAAQ;AACxB,iBAAO,MAAM,IAAI,MAAM;AAAA,YACrB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBAAgB,4BAA4B,EAAE,IAAI,OAAO,UAAU;AAAA,UACrE,CAAC;AAAA,QACH;AACA,kBAAU,KAAK;AAAA,MACjB;AACA,UAAI,QAAQ,EAAE,IAAI,GAAG;AACnB,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,4BAA4B,EAAE,IAAI,OAAO,UAAU;AAAA,QACrE,CAAC;AAAA,MACH;AACA,cAAQ,EAAE,IAAI,IAAI,EAAE,MAAM,QAAQ,MAAM,EAAE;AAAA,IAC5C;AAAA,EACF;AACA,SAAO;AACT;AAEA,gBAAgB,eACd,KACA,MACwB;AACxB,QAAM;AACN,aAAW,CAAC,YAAY,OAAO,KAAK,OAAO,QAAQ,IAAI,GAAG;AACxD,QAAI,QAAQ,SAAS,UAAU;AAC7B,YAAM,IAAI,UAAU;AACpB,aAAO,eAAe,KAAK,QAAQ,MAAM;AACzC,YAAM;AAAA,IACR,OAAO;AACL,YAAM,aAAa,QAAQ,KAAK,YAAY;AAC5C,UAAI,CAAC,YAAY;AACf,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,YAAY,QAAQ,KAAK,IAAI;AAAA,QAC/C,CAAC;AAAA,MACH;AACA,YAAM,MAAM,MAAM;AAAA,QAChB;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,MACF;AACA,YAAM,IAAI,UAAU,MAAM,GAAG;AAAA,IAC/B;AAAA,EACF;AACA,QAAM;AACR;AAEA,gBAAgB,qBACd,KACA,WACA,gBACwB;AACxB,QAAM,YAAY,MAAM,eAAe,KAAK,WAAW,gBAAgB,CAAC,CAAC;AACzE,MAAI,WAAW;AACb,UAAM;AACN,WAAO,iBAAiB,SAAS;AACjC,UAAM;AACN,UAAM;AACN,UAAM;AACN,UAAM;AACN,UAAM;AAAA,EACR,OAAO;AACL,UAAM;AAAA,EACR;AACF;AAEA,UAAU,iBAAiB,MAAoC;AAC7D,QAAM;AACN,aAAW,CAAC,YAAY,OAAO,KAAK,OAAO,QAAQ,IAAI,GAAG;AACxD,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,IAAI,UAAU,MAAM,OAAO;AAAA,IACnC,OAAO;AACL,YAAM,IAAI,UAAU;AACpB,aAAO,iBAAiB,OAAO;AAC/B,YAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM;AACR;AAMA,eAAe,eACb,KACA,WACA,gBACA,YAC2B;AAE3B,QAAM,WAAW,UAAU,SAAS,cAAc;AAClD,MAAI,CAAC,UAAU;AACb,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,mCAAmC,cAAc,UAAU,cAAc;AAAA;AAAA,EAAS,OAAO,KAAK,UAAU,QAAQ,EAAE,SAAS,CAAC;AAAA,IAC9I,CAAC;AAAA,EACH;AACA,MAAI,UAAU,SAAS,WAAW,QAAQ;AAC1C,aAAW,aAAa,YAAY;AAClC,UAAM,kBAAkB,QAAQ;AAAA,MAC9B,CAAC,CAAC,UAAU,MAAM,eAAe;AAAA,IACnC;AACA,QAAI,CAAC,iBAAiB;AACpB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,uBAAuB,SAAS;AAAA,MAClD,CAAC;AAAA,IACH;AACA,UAAM,CAAC,GAAG,IAAI,IAAI;AAClB,QAAI,KAAK,SAAS,UAAU;AAC1B,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,sBAAsB,SAAS;AAAA,MACjD,CAAC;AAAA,IACH;AACA,cAAU,KAAK;AAAA,EACjB;AACA,SAAO,wBAAwB,KAAK,WAAW,UAAU,OAAO;AAClE;AAEA,eAAe,wBACb,KACA,WACA,UACA,SAC2B;AAC3B,QAAM,SAAoB,CAAC;AAC3B,MAAI,WAAW;AACf,aAAW,CAAC,YAAY,eAAe,KAAK,SAAS;AACnD,QAAI,gBAAgB,SAAS,QAAQ;AAEnC,UAAI,gBAAgB,KAAK,WAAW,4BAA4B,GAAG;AACjE,cAAM,SAAS,gBAAgB,KAAK;AAAA,UAClC,6BAA6B;AAAA,QAC/B;AACA,cAAM,CAAC,eAAe,GAAG,UAAU,IAAI,OAAO,MAAM,GAAG;AACvD,cAAM,iBAAiB,SAAS,WAAW,gBAAgB;AAAA,UACzD,CAAC,MAAM,EAAE,SAAS;AAAA,QACpB;AACA,YAAI,CAAC,gBAAgB;AACnB,iBAAO,MAAM,IAAI,MAAM;AAAA,YACrB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBAAgB,gCAAgC,aAAa;AAAA,UAC/D,CAAC;AAAA,QACH;AACA,cAAM,YAAY,MAAM;AAAA,UACtB;AAAA,UACA;AAAA,UACA,eAAe;AAAA,UACf;AAAA,QACF;AACA,YAAI,WAAW;AACb,iBAAO,UAAU,IAAI;AACrB,qBAAW;AAAA,QACb;AAAA,MACF;AAEA,YAAM,SAAS,SAAS,WAAW,eAAe,SAAS;AAC3D,UAAI,CAAC,UAAU,gBAAgB,KAAK,WAAW,sBAAsB,GAAG;AACtE,cAAM,OAAO,MAAM;AAAA,UACjB;AAAA,UACA;AAAA,UACA,gBAAgB;AAAA,UAChB;AAAA,QACF;AACA,eAAO,UAAU,IAAI;AACrB,mBAAW;AAAA,MACb;AAAA,IACF,OAAO;AACL,YAAM,UAAU,MAAM;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,MAClB;AACA,UAAI,SAAS;AACX,eAAO,UAAU,IAAI;AACrB,mBAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,SAAO,WAAW,SAAS;AAC7B;AAEA,gBAAgB,eACd,KACA,MACA,UACwB;AACxB,QAAM,GAAG,IAAI;AACb,aAAW,CAACC,OAAM,eAAe,KAAK,SAAS,WAAW,QAAQ,QAAQ;AACxE,UAAM,GAAGA,KAAI;AACb,WAAO,cAAc,KAAK,UAAU,eAAe;AACnD,UAAM;AAAA,EACR;AACA,QAAM;AACR;AAEA,gBAAgB,cACd,KACA,UACA,iBACwB;AACxB,MAAI,gBAAgB,SAAS,QAAQ;AACnC,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA,IACF;AAAA,EACF,WAAW,gBAAgB,SAAS,UAAU;AAC5C,UAAM;AACN,eAAW,CAAC,MAAM,WAAW,KAAK,gBAAgB,QAAQ;AACxD,YAAM,GAAG,IAAI;AACb,aAAO,cAAc,KAAK,UAAU,WAAW;AAC/C,YAAM;AAAA,IACR;AACA,UAAM;AAAA,EACR;AACF;AAEA,eAAsB,yBACpB,KACA,UACA,WACA,YACA;AACA,MAAI,CAAC,UAAU,WAAW,sBAAsB,GAAG;AACjD,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,+BAA+B,SAAS;AAAA,IAC1D,CAAC;AAAA,EACH;AACA,QAAM,UAAU,UAAU,MAAM,uBAAuB,MAAM;AAE7D,QAAM,CAAC,YAAY,YAAY,IAAI,QAAQ,MAAM,GAAG;AACpD,QAAM,0BAA0B,uBAAuB,UAAU;AAEjE,QAAM,iBAAiB,SAAS,UAAU,uBAAuB;AACjE,MAAI,CAAC,gBAAgB;AACnB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,qBAAqB,UAAU;AAAA,IACjD,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,eAAe,UAAU;AAAA,IAChD,CAAC,MAAM,EAAE,SAAS;AAAA,EACpB;AACA,MAAI,CAAC,kBAAkB;AACrB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,uBAAuB,YAAY;AAAA,IACrD,CAAC;AAAA,EACH;AACA,SAAO,MAAM;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,eAAe,yBACb,KACA,kBACA,YACA,WACiB;AAEjB,QAAM,UAAU,iBAAiB,QAAQ,YAAY;AAErD,MAAI,WAAW;AACf,MAAI;AACF,UAAM,oBAAgB,yCAAe,iBAAiB,IAAI;AAC1D,QAAI,eAAe;AACjB,UAAI,cAAc,SAAS,YAAY,cAAc,SAAS,OAAO;AACnE,uBAAW,0CAAgB,eAAe,SAAS;AAAA,MACrD,OAAO;AAEL,cAAM,IAAI;AAAA,UACR,uCAAuC,cAAc,IAAI;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,0BAA0B,iBAAiB,IAAI;AAAA,MAC/D,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,MAAI,cAAc;AAClB,MAAI;AACF,UAAM,uBAAmB,yCAAe,iBAAiB,OAAO;AAChE,QAAI,kBAAkB;AACpB,wBAAc,0CAAgB,kBAAkB,SAAS;AAAA,IAC3D;AAAA,EACF,SAAS,GAAG;AACV,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,6BAA6B,iBAAiB,OAAO;AAAA,MACrE,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,SAAO,sBAAsB,OAAO,OAAO,UAAU,MAAM,QAAQ,KAAK,WAAW;AACrF;AAEA,SAAS,uBAAuB,YAA6C;AAC3E,MAAI,CAAC,WAAW,SAAS,KAAK,GAAG;AAC/B,WAAO,aAAa;AAAA,EACtB;AACA,SAAO;AACT;", "names": ["path", "module", "name"]}
{"version": 3, "sources": ["../../../../src/cli/codegen_templates/component_server.ts"], "sourcesContent": ["import { ComponentDirectory } from \"../lib/components/definition/directoryStructure.js\";\nimport { header } from \"./common.js\";\n\nexport function componentServerJS(): string {\n  const result = `\n  ${header(\n    \"Generated utilities for implementing server-side Convex query and mutation functions.\",\n  )}\n  import {\n    actionGeneric,\n    httpActionGeneric,\n    queryGeneric,\n    mutationGeneric,\n    internalActionGeneric,\n    internalMutationGeneric,\n    internalQueryGeneric,\n    componentsGeneric,\n  } from \"convex/server\";\n\n  /**\n   * Define a query in this Convex app's public API.\n   *\n   * This function will be allowed to read your Convex database and will be accessible from the client.\n   *\n   * @param func - The query function. It receives a {@link QueryCtx} as its first argument.\n   * @returns The wrapped query. Include this as an \\`export\\` to name it and make it accessible.\n   */\n  export const query = queryGeneric;\n\n  /**\n   * Define a query that is only accessible from other Convex functions (but not from the client).\n   *\n   * This function will be allowed to read from your Convex database. It will not be accessible from the client.\n   *\n   * @param func - The query function. It receives a {@link QueryCtx} as its first argument.\n   * @returns The wrapped query. Include this as an \\`export\\` to name it and make it accessible.\n   */\n  export const internalQuery = internalQueryGeneric;\n\n  /**\n   * Define a mutation in this Convex app's public API.\n   *\n   * This function will be allowed to modify your Convex database and will be accessible from the client.\n   *\n   * @param func - The mutation function. It receives a {@link MutationCtx} as its first argument.\n   * @returns The wrapped mutation. Include this as an \\`export\\` to name it and make it accessible.\n   */\n  export const mutation = mutationGeneric;\n\n  /**\n   * Define a mutation that is only accessible from other Convex functions (but not from the client).\n   *\n   * This function will be allowed to modify your Convex database. It will not be accessible from the client.\n   *\n   * @param func - The mutation function. It receives a {@link MutationCtx} as its first argument.\n   * @returns The wrapped mutation. Include this as an \\`export\\` to name it and make it accessible.\n   */\n  export const internalMutation = internalMutationGeneric;\n\n  /**\n   * Define an action in this Convex app's public API.\n   *\n   * An action is a function which can execute any JavaScript code, including non-deterministic\n   * code and code with side-effects, like calling third-party services.\n   * They can be run in Convex's JavaScript environment or in Node.js using the \"use node\" directive.\n   * They can interact with the database indirectly by calling queries and mutations using the {@link ActionCtx}.\n   *\n   * @param func - The action. It receives an {@link ActionCtx} as its first argument.\n   * @returns The wrapped action. Include this as an \\`export\\` to name it and make it accessible.\n   */\n  export const action = actionGeneric;\n\n  /**\n   * Define an action that is only accessible from other Convex functions (but not from the client).\n   *\n   * @param func - The function. It receives an {@link ActionCtx} as its first argument.\n   * @returns The wrapped function. Include this as an \\`export\\` to name it and make it accessible.\n   */\n  export const internalAction = internalActionGeneric;\n\n  /**\n   * Define a Convex HTTP action.\n   *\n   * @param func - The function. It receives an {@link ActionCtx} as its first argument, and a \\`Request\\` object\n   * as its second.\n   * @returns The wrapped endpoint function. Route a URL path to this function in \\`convex/http.js\\`.\n   */\n  export const httpAction = httpActionGeneric;\n  `;\n  return result;\n}\n\nfunction componentServerDTSPrelude(_isRoot: boolean): string {\n  return `\n    ${header(\n      \"Generated utilities for implementing server-side Convex query and mutation functions.\",\n    )}\n    import {\n      ActionBuilder,\n      AnyComponents,\n      HttpActionBuilder,\n      MutationBuilder,\n      QueryBuilder,\n      GenericActionCtx,\n      GenericMutationCtx,\n      GenericQueryCtx,\n      GenericDatabaseReader,\n      GenericDatabaseWriter,\n      FunctionReference,\n    } from \"convex/server\";\n    import type { DataModel } from \"./dataModel.js\";\n\n    type GenericCtx = GenericActionCtx<DataModel> | GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>;\n\n    /**\n     * Define a query in this Convex app's public API.\n     *\n     * This function will be allowed to read your Convex database and will be accessible from the client.\n     *\n     * @param func - The query function. It receives a {@link QueryCtx} as its first argument.\n     * @returns The wrapped query. Include this as an \\`export\\` to name it and make it accessible.\n     */\n    export declare const query: QueryBuilder<DataModel, \"public\">;\n\n    /**\n     * Define a query that is only accessible from other Convex functions (but not from the client).\n     *\n     * This function will be allowed to read from your Convex database. It will not be accessible from the client.\n     *\n     * @param func - The query function. It receives a {@link QueryCtx} as its first argument.\n     * @returns The wrapped query. Include this as an \\`export\\` to name it and make it accessible.\n     */\n    export declare const internalQuery: QueryBuilder<DataModel, \"internal\">;\n\n    /**\n     * Define a mutation in this Convex app's public API.\n     *\n     * This function will be allowed to modify your Convex database and will be accessible from the client.\n     *\n     * @param func - The mutation function. It receives a {@link MutationCtx} as its first argument.\n     * @returns The wrapped mutation. Include this as an \\`export\\` to name it and make it accessible.\n     */\n    export declare const mutation: MutationBuilder<DataModel, \"public\">;\n\n    /**\n     * Define a mutation that is only accessible from other Convex functions (but not from the client).\n     *\n     * This function will be allowed to modify your Convex database. It will not be accessible from the client.\n     *\n     * @param func - The mutation function. It receives a {@link MutationCtx} as its first argument.\n     * @returns The wrapped mutation. Include this as an \\`export\\` to name it and make it accessible.\n     */\n    export declare const internalMutation: MutationBuilder<DataModel, \"internal\">;\n\n    /**\n     * Define an action in this Convex app's public API.\n     *\n     * An action is a function which can execute any JavaScript code, including non-deterministic\n     * code and code with side-effects, like calling third-party services.\n     * They can be run in Convex's JavaScript environment or in Node.js using the \"use node\" directive.\n     * They can interact with the database indirectly by calling queries and mutations using the {@link ActionCtx}.\n     *\n     * @param func - The action. It receives an {@link ActionCtx} as its first argument.\n     * @returns The wrapped action. Include this as an \\`export\\` to name it and make it accessible.\n     */\n    export declare const action: ActionBuilder<DataModel, \"public\">;\n\n    /**\n     * Define an action that is only accessible from other Convex functions (but not from the client).\n     *\n     * @param func - The function. It receives an {@link ActionCtx} as its first argument.\n     * @returns The wrapped function. Include this as an \\`export\\` to name it and make it accessible.\n     */\n    export declare const internalAction: ActionBuilder<DataModel, \"internal\">;\n\n    /**\n     * Define an HTTP action.\n     *\n     * This function will be used to respond to HTTP requests received by a Convex\n     * deployment if the requests matches the path and method where this action\n     * is routed. Be sure to route your action in \\`convex/http.js\\`.\n     *\n     * @param func - The function. It receives an {@link ActionCtx} as its first argument.\n     * @returns The wrapped function. Import this function from \\`convex/http.js\\` and route it to hook it up.\n     */\n    export declare const httpAction: HttpActionBuilder;\n\n    /**\n     * A set of services for use within Convex query functions.\n     *\n     * The query context is passed as the first argument to any Convex query\n     * function run on the server.\n     *\n     * This differs from the {@link MutationCtx} because all of the services are\n     * read-only.\n     */\n    export type QueryCtx = GenericQueryCtx<DataModel>;\n\n    /**\n     * A set of services for use within Convex mutation functions.\n     *\n     * The mutation context is passed as the first argument to any Convex mutation\n     * function run on the server.\n     */\n    export type MutationCtx = GenericMutationCtx<DataModel>;\n\n    /**\n     * A set of services for use within Convex action functions.\n     *\n     * The action context is passed as the first argument to any Convex action\n     * function run on the server.\n     */\n    export type ActionCtx = GenericActionCtx<DataModel>;\n\n    /**\n     * An interface to read from the database within Convex query functions.\n     *\n     * The two entry points are {@link DatabaseReader.get}, which fetches a single\n     * document by its {@link Id}, or {@link DatabaseReader.query}, which starts\n     * building a query.\n     */\n    export type DatabaseReader = GenericDatabaseReader<DataModel>;\n\n    /**\n     * An interface to read from and write to the database within Convex mutation\n     * functions.\n     *\n     * Convex guarantees that all writes within a single mutation are\n     * executed atomically, so you never have to worry about partial writes leaving\n     * your data in an inconsistent state. See [the Convex Guide](https://docs.convex.dev/understanding/convex-fundamentals/functions#atomicity-and-optimistic-concurrency-control)\n     * for the guarantees Convex provides your functions.\n     */\n    export type DatabaseWriter = GenericDatabaseWriter<DataModel>;\n  `;\n}\n\nexport function componentServerStubDTS(isRoot: boolean): string {\n  return componentServerDTSPrelude(isRoot);\n}\n\nexport async function componentServerDTS(\n  componentDirectory: ComponentDirectory,\n): Promise<string> {\n  const result = [componentServerDTSPrelude(componentDirectory.isRoot)];\n  return result.join(\"\\n\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAuB;AAEhB,SAAS,oBAA4B;AAC1C,QAAM,SAAS;AAAA,QACb;AAAA,IACA;AAAA,EACF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkFD,SAAO;AACT;AAEA,SAAS,0BAA0B,SAA0B;AAC3D,SAAO;AAAA,UACH;AAAA,IACA;AAAA,EACF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0IL;AAEO,SAAS,uBAAuB,QAAyB;AAC9D,SAAO,0BAA0B,MAAM;AACzC;AAEA,eAAsB,mBACpB,oBACiB;AACjB,QAAM,SAAS,CAAC,0BAA0B,mBAAmB,MAAM,CAAC;AACpE,SAAO,OAAO,KAAK,IAAI;AACzB;", "names": []}
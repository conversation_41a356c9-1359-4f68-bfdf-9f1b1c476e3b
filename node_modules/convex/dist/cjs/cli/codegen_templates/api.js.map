{"version": 3, "sources": ["../../../../src/cli/codegen_templates/api.ts"], "sourcesContent": ["import { header } from \"./common.js\";\n\nexport function importPath(modulePath: string) {\n  // Replace backslashes with forward slashes.\n  const filePath = modulePath.replace(/\\\\/g, \"/\");\n  // Strip off the file extension.\n  const lastDot = filePath.lastIndexOf(\".\");\n  return filePath.slice(0, lastDot === -1 ? undefined : lastDot);\n}\n\nexport function moduleIdentifier(modulePath: string) {\n  // TODO: This encoding is ambiguous (`foo/bar` vs `foo_bar` vs `foo-bar`).\n  // Also we should be renaming keywords like `delete`.\n  let safeModulePath = importPath(modulePath)\n    .replace(/\\//g, \"_\")\n    .replace(/-/g, \"_\");\n  // Escape existing variable names in this file\n  if ([\"fullApi\", \"api\", \"internal\", \"components\"].includes(safeModulePath)) {\n    safeModulePath = `${safeModulePath}_`;\n  }\n  return safeModulePath;\n}\n\nexport function apiCodegen(modulePaths: string[]) {\n  const apiDTS = `${header(\"Generated `api` utility.\")}\n  import type { ApiFromModules, FilterApi, FunctionReference } from \"convex/server\";\n  ${modulePaths\n    .map(\n      (modulePath) =>\n        `import type * as ${moduleIdentifier(modulePath)} from \"../${importPath(\n          modulePath,\n        )}.js\";`,\n    )\n    .join(\"\\n\")}\n\n  /**\n   * A utility for referencing Convex functions in your app's API.\n   *\n   * Usage:\n   * \\`\\`\\`js\n   * const myFunctionReference = api.myModule.myFunction;\n   * \\`\\`\\`\n   */\n  declare const fullApi: ApiFromModules<{\n    ${modulePaths\n      .map(\n        (modulePath) =>\n          `\"${importPath(modulePath)}\": typeof ${moduleIdentifier(modulePath)},`,\n      )\n      .join(\"\\n\")}\n  }>;\n  export declare const api: FilterApi<typeof fullApi, FunctionReference<any, \"public\">>;\n  export declare const internal: FilterApi<typeof fullApi, FunctionReference<any, \"internal\">>;\n  `;\n\n  const apiJS = `${header(\"Generated `api` utility.\")}\n  import { anyApi } from \"convex/server\";\n\n  /**\n   * A utility for referencing Convex functions in your app's API.\n   *\n   * Usage:\n   * \\`\\`\\`js\n   * const myFunctionReference = api.myModule.myFunction;\n   * \\`\\`\\`\n   */\n  export const api = anyApi;\n  export const internal = anyApi;\n  `;\n  return {\n    DTS: apiDTS,\n    JS: apiJS,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAuB;AAEhB,SAAS,WAAW,YAAoB;AAE7C,QAAM,WAAW,WAAW,QAAQ,OAAO,GAAG;AAE9C,QAAM,UAAU,SAAS,YAAY,GAAG;AACxC,SAAO,SAAS,MAAM,GAAG,YAAY,KAAK,SAAY,OAAO;AAC/D;AAEO,SAAS,iBAAiB,YAAoB;AAGnD,MAAI,iBAAiB,WAAW,UAAU,EACvC,QAAQ,OAAO,GAAG,EAClB,QAAQ,MAAM,GAAG;AAEpB,MAAI,CAAC,WAAW,OAAO,YAAY,YAAY,EAAE,SAAS,cAAc,GAAG;AACzE,qBAAiB,GAAG,cAAc;AAAA,EACpC;AACA,SAAO;AACT;AAEO,SAAS,WAAW,aAAuB;AAChD,QAAM,SAAS,OAAG,sBAAO,0BAA0B,CAAC;AAAA;AAAA,IAElD,YACC;AAAA,IACC,CAAC,eACC,oBAAoB,iBAAiB,UAAU,CAAC,aAAa;AAAA,MAC3D;AAAA,IACF,CAAC;AAAA,EACL,EACC,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWT,YACC;AAAA,IACC,CAAC,eACC,IAAI,WAAW,UAAU,CAAC,aAAa,iBAAiB,UAAU,CAAC;AAAA,EACvE,EACC,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAMf,QAAM,QAAQ,OAAG,sBAAO,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcnD,SAAO;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,EACN;AACF;", "names": []}
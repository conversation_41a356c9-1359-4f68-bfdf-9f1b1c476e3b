{"version": 3, "sources": ["../../../src/cli/functionSpec.ts"], "sourcesContent": ["import { oneoffContext } from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { Command, Option } from \"@commander-js/extra-typings\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { functionSpecForDeployment } from \"./lib/functionSpec.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\nexport const functionSpec = new Command(\"function-spec\")\n  .summary(\"List function metadata from your deployment\")\n  .description(\n    \"List argument and return values to your Convex functions.\\n\\n\" +\n      \"By default, this inspects your dev deployment.\",\n  )\n  .allowExcessArguments(false)\n  .addOption(new Option(\"--file\", \"Output as JSON to a file.\"))\n  .addDeploymentSelectionOptions(\n    actionDescription(\"Read function metadata from\"),\n  )\n  .showHelpAfterError()\n  .action(async (options) => {\n    const ctx = await oneoffContext(options);\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n    const { adminKey, url: deploymentUrl } =\n      await loadSelectedDeploymentCredentials(\n        ctx,\n        deploymentSelection,\n        selectionWithinProject,\n      );\n\n    await functionSpecForDeployment(ctx, {\n      deploymentUrl,\n      adminKey,\n      file: !!options.file,\n    });\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAA8B;AAC9B,iBAGO;AACP,2BAAgC;AAChC,qBAAkC;AAClC,0BAA0C;AAC1C,iCAAuC;AAChC,MAAM,eAAe,IAAI,6BAAQ,eAAe,EACpD,QAAQ,6CAA6C,EACrD;AAAA,EACC;AAEF,EACC,qBAAqB,KAAK,EAC1B,UAAU,IAAI,4BAAO,UAAU,2BAA2B,CAAC,EAC3D;AAAA,MACC,kCAAkB,6BAA6B;AACjD,EACC,mBAAmB,EACnB,OAAO,OAAO,YAAY;AACzB,QAAM,MAAM,UAAM,8BAAc,OAAO;AACvC,QAAM,sBAAsB,UAAM,mDAAuB,KAAK,OAAO;AACrE,QAAM,yBACJ,UAAM,wDAA4C,KAAK,OAAO;AAChE,QAAM,EAAE,UAAU,KAAK,cAAc,IACnC,UAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEF,YAAM,+CAA0B,KAAK;AAAA,IACnC;AAAA,IACA;AAAA,IACA,MAAM,CAAC,CAAC,QAAQ;AAAA,EAClB,CAAC;AACH,CAAC;", "names": []}
{"version": 3, "sources": ["../../../src/cli/reinit.ts"], "sourcesContent": ["import { Command, Option } from \"@commander-js/extra-typings\";\nimport { oneoffContext } from \"../bundler/context.js\";\n\n// Reinitialize an existing Convex project.\n// This command is deprecated and hidden from the command help.\n// `npx convex dev --once --configure=existing` replaces it.\nexport const reinit = new Command(\"reinit\")\n  .description(\n    \"Reinitialize a Convex project in the local directory if you've lost your convex.json file\",\n  )\n  .allowExcessArguments(false)\n  .addOption(\n    new Option(\n      \"--team <team_slug>\",\n      \"The identifier of the team the project belongs to.\",\n    ),\n  )\n  .addOption(\n    new Option(\n      \"--project <project_slug>\",\n      \"The identifier of the project you'd like to reinitialize.\",\n    ),\n  )\n  .action(async (_options) => {\n    return (\n      await oneoffContext({\n        url: undefined,\n        adminKey: undefined,\n        envFile: undefined,\n      })\n    ).crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      errForSentry:\n        \"The `reinit` command is deprecated. Use `npx convex dev --once --configure=existing` instead.\",\n      printedMessage:\n        \"The `reinit` command is deprecated. Use `npx convex dev --once --configure=existing` instead.\",\n    });\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAgC;AAChC,qBAA8B;AAKvB,MAAM,SAAS,IAAI,6BAAQ,QAAQ,EACvC;AAAA,EACC;AACF,EACC,qBAAqB,KAAK,EAC1B;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF,EACC,OAAO,OAAO,aAAa;AAC1B,UACE,UAAM,8BAAc;AAAA,IAClB,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC,GACD,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cACE;AAAA,IACF,gBACE;AAAA,EACJ,CAAC;AACH,CAAC;", "names": []}
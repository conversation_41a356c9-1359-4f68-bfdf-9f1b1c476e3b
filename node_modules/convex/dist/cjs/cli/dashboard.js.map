{"version": 3, "sources": ["../../../src/cli/dashboard.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport chalk from \"chalk\";\nimport open from \"open\";\nimport {\n  Context,\n  logMessage,\n  logOutput,\n  logWarning,\n  oneoffContext,\n} from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\nimport { checkIfDashboardIsRunning } from \"./lib/localDeployment/dashboard.js\";\nimport { getDashboardUrl } from \"./lib/dashboard.js\";\nimport { isAnonymousDeployment } from \"./lib/deployment.js\";\n\nexport const DASHBOARD_HOST = process.env.CONVEX_PROVISION_HOST\n  ? \"http://localhost:6789\"\n  : \"https://dashboard.convex.dev\";\n\nexport const dashboard = new Command(\"dashboard\")\n  .alias(\"dash\")\n  .description(\"Open the dashboard in the browser\")\n  .allowExcessArguments(false)\n  .option(\n    \"--no-open\",\n    \"Don't automatically open the dashboard in the default browser\",\n  )\n  .addDeploymentSelectionOptions(actionDescription(\"Open the dashboard for\"))\n  .showHelpAfterError()\n  .action(async (options) => {\n    const ctx = await oneoffContext(options);\n\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n    const deployment = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n      { ensureLocalRunning: false },\n    );\n\n    if (deployment.deploymentFields === null) {\n      const msg = `Self-hosted deployment configured.\\n\\`${chalk.bold(\"npx convex dashboard\")}\\` is not supported for self-hosted deployments.\\nSee self-hosting instructions for how to self-host the dashboard.`;\n      logMessage(ctx, chalk.yellow(msg));\n      return;\n    }\n    const dashboardUrl = getDashboardUrl(ctx, deployment.deploymentFields);\n    if (isAnonymousDeployment(deployment.deploymentFields.deploymentName)) {\n      const warningMessage = `You are not currently running the dashboard locally. Make sure \\`npx convex dev\\` is running and try again.`;\n      if (dashboardUrl === null) {\n        logWarning(ctx, warningMessage);\n        return;\n      }\n      const isLocalDashboardRunning = await checkIfDashboardIsRunning(ctx);\n      if (!isLocalDashboardRunning) {\n        logWarning(ctx, warningMessage);\n        return;\n      }\n      await logOrOpenUrl(ctx, dashboardUrl, options.open);\n      return;\n    }\n\n    await logOrOpenUrl(ctx, dashboardUrl ?? DASHBOARD_HOST, options.open);\n  });\n\nasync function logOrOpenUrl(ctx: Context, url: string, shouldOpen: boolean) {\n  if (shouldOpen) {\n    logMessage(ctx, chalk.gray(`Opening ${url} in the default browser...`));\n    await open(url);\n  } else {\n    logOutput(ctx, url);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAwB;AACxB,mBAAkB;AAClB,kBAAiB;AACjB,qBAMO;AACP,iBAGO;AACP,qBAAkC;AAClC,iCAAuC;AACvC,uBAA0C;AAC1C,IAAAA,oBAAgC;AAChC,wBAAsC;AAE/B,MAAM,iBAAiB,QAAQ,IAAI,wBACtC,0BACA;AAEG,MAAM,YAAY,IAAI,6BAAQ,WAAW,EAC7C,MAAM,MAAM,EACZ,YAAY,mCAAmC,EAC/C,qBAAqB,KAAK,EAC1B;AAAA,EACC;AAAA,EACA;AACF,EACC,kCAA8B,kCAAkB,wBAAwB,CAAC,EACzE,mBAAmB,EACnB,OAAO,OAAO,YAAY;AACzB,QAAM,MAAM,UAAM,8BAAc,OAAO;AAEvC,QAAM,yBACJ,UAAM,wDAA4C,KAAK,OAAO;AAChE,QAAM,sBAAsB,UAAM,mDAAuB,KAAK,OAAO;AACrE,QAAM,aAAa,UAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA,EAAE,oBAAoB,MAAM;AAAA,EAC9B;AAEA,MAAI,WAAW,qBAAqB,MAAM;AACxC,UAAM,MAAM;AAAA,IAAyC,aAAAC,QAAM,KAAK,sBAAsB,CAAC;AAAA;AACvF,mCAAW,KAAK,aAAAA,QAAM,OAAO,GAAG,CAAC;AACjC;AAAA,EACF;AACA,QAAM,mBAAe,mCAAgB,KAAK,WAAW,gBAAgB;AACrE,UAAI,yCAAsB,WAAW,iBAAiB,cAAc,GAAG;AACrE,UAAM,iBAAiB;AACvB,QAAI,iBAAiB,MAAM;AACzB,qCAAW,KAAK,cAAc;AAC9B;AAAA,IACF;AACA,UAAM,0BAA0B,UAAM,4CAA0B,GAAG;AACnE,QAAI,CAAC,yBAAyB;AAC5B,qCAAW,KAAK,cAAc;AAC9B;AAAA,IACF;AACA,UAAM,aAAa,KAAK,cAAc,QAAQ,IAAI;AAClD;AAAA,EACF;AAEA,QAAM,aAAa,KAAK,gBAAgB,gBAAgB,QAAQ,IAAI;AACtE,CAAC;AAEH,eAAe,aAAa,KAAc,KAAa,YAAqB;AAC1E,MAAI,YAAY;AACd,mCAAW,KAAK,aAAAA,QAAM,KAAK,WAAW,GAAG,4BAA4B,CAAC;AACtE,cAAM,YAAAC,SAAK,GAAG;AAAA,EAChB,OAAO;AACL,kCAAU,KAAK,GAAG;AAAA,EACpB;AACF;", "names": ["import_dashboard", "chalk", "open"]}
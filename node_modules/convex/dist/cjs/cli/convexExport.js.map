{"version": 3, "sources": ["../../../src/cli/convexExport.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport chalk from \"chalk\";\nimport { ensureHasConvexDependency } from \"./lib/utils/utils.js\";\nimport { oneoffContext } from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { deploymentDashboardUrlPage } from \"./lib/dashboard.js\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { exportFromDeployment } from \"./lib/convexExport.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\nexport const convexExport = new Command(\"export\")\n  .summary(\"Export data from your deployment to a ZIP file\")\n  .description(\n    \"Export data, and optionally file storage, from your Convex deployment to a ZIP file.\\n\" +\n      \"By default, this exports from your dev deployment.\",\n  )\n  .allowExcessArguments(false)\n  .addExportOptions()\n  .addDeploymentSelectionOptions(actionDescription(\"Export data from\"))\n  .showHelpAfterError()\n  .action(async (options) => {\n    const ctx = await oneoffContext(options);\n    await ensureHasConvexDependency(ctx, \"export\");\n\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n\n    const deployment = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n    );\n\n    const deploymentNotice = options.prod\n      ? ` in your ${chalk.bold(\"prod\")} deployment`\n      : \"\";\n    await exportFromDeployment(ctx, {\n      ...options,\n      deploymentUrl: deployment.url,\n      adminKey: deployment.adminKey,\n      deploymentNotice,\n      snapshotExportDashboardLink: deploymentDashboardUrlPage(\n        deployment.deploymentFields?.deploymentName ?? null,\n        \"/settings/snapshot-export\",\n      ),\n    });\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAwB;AACxB,mBAAkB;AAClB,mBAA0C;AAC1C,qBAA8B;AAC9B,iBAGO;AACP,uBAA2C;AAC3C,qBAAkC;AAClC,0BAAqC;AACrC,iCAAuC;AAChC,MAAM,eAAe,IAAI,6BAAQ,QAAQ,EAC7C,QAAQ,gDAAgD,EACxD;AAAA,EACC;AAEF,EACC,qBAAqB,KAAK,EAC1B,iBAAiB,EACjB,kCAA8B,kCAAkB,kBAAkB,CAAC,EACnE,mBAAmB,EACnB,OAAO,OAAO,YAAY;AACzB,QAAM,MAAM,UAAM,8BAAc,OAAO;AACvC,YAAM,wCAA0B,KAAK,QAAQ;AAE7C,QAAM,sBAAsB,UAAM,mDAAuB,KAAK,OAAO;AAErE,QAAM,yBACJ,UAAM,wDAA4C,KAAK,OAAO;AAEhE,QAAM,aAAa,UAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,mBAAmB,QAAQ,OAC7B,YAAY,aAAAA,QAAM,KAAK,MAAM,CAAC,gBAC9B;AACJ,YAAM,0CAAqB,KAAK;AAAA,IAC9B,GAAG;AAAA,IACH,eAAe,WAAW;AAAA,IAC1B,UAAU,WAAW;AAAA,IACrB;AAAA,IACA,iCAA6B;AAAA,MAC3B,WAAW,kBAAkB,kBAAkB;AAAA,MAC/C;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;", "names": ["chalk"]}
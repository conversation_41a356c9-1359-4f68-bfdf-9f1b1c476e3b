{"version": 3, "sources": ["../../../src/cli/update.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { Command } from \"@commander-js/extra-typings\";\nimport { logMessage, oneoffContext } from \"../bundler/context.js\";\nimport { loadPackageJson } from \"./lib/utils/utils.js\";\n\nexport const update = new Command(\"update\")\n  .description(\"Print instructions for updating the convex package\")\n  .allowExcessArguments(false)\n  .action(async () => {\n    const ctx = await oneoffContext({\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    let updateInstructions = \"npm install convex@latest\\n\";\n    const packages = await loadPackageJson(ctx);\n    const oldPackageNames = Object.keys(packages).filter((name) =>\n      name.startsWith(\"@convex-dev\"),\n    );\n    for (const pkg of oldPackageNames) {\n      updateInstructions += `npm uninstall ${pkg}\\n`;\n    }\n\n    logMessage(\n      ctx,\n      chalk.green(\n        `To view the Convex changelog, go to https://news.convex.dev/tag/releases/\\nWhen you are ready to upgrade, run the following commands:\\n${updateInstructions}`,\n      ),\n    );\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,2BAAwB;AACxB,qBAA0C;AAC1C,mBAAgC;AAEzB,MAAM,SAAS,IAAI,6BAAQ,QAAQ,EACvC,YAAY,oDAAoD,EAChE,qBAAqB,KAAK,EAC1B,OAAO,YAAY;AAClB,QAAM,MAAM,UAAM,8BAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,MAAI,qBAAqB;AACzB,QAAM,WAAW,UAAM,8BAAgB,GAAG;AAC1C,QAAM,kBAAkB,OAAO,KAAK,QAAQ,EAAE;AAAA,IAAO,CAAC,SACpD,KAAK,WAAW,aAAa;AAAA,EAC/B;AACA,aAAW,OAAO,iBAAiB;AACjC,0BAAsB,iBAAiB,GAAG;AAAA;AAAA,EAC5C;AAEA;AAAA,IACE;AAAA,IACA,aAAAA,QAAM;AAAA,MACJ;AAAA;AAAA,EAA0I,kBAAkB;AAAA,IAC9J;AAAA,EACF;AACF,CAAC;", "names": ["chalk"]}
{"version": 3, "sources": ["../../../src/cli/disableLocalDev.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport { logFinishedStep, oneoffContext } from \"../bundler/context.js\";\nimport { deploymentCredentialsOrConfigure } from \"./configure.js\";\nimport {\n  modifyGlobalConfig,\n  readGlobalConfig,\n} from \"./lib/utils/globalConfig.js\";\nimport {\n  deploymentNameAndTypeFromSelection,\n  getDeploymentSelection,\n} from \"./lib/deploymentSelection.js\";\n\nexport const disableLocalDeployments = new Command(\"disable-local-deployments\")\n  .description(\n    \"Stop using a local deployment for the current project, or globally disable local depoyments with --global\",\n  )\n  .option(\n    \"--global\",\n    \"Disable local deployments on this machine until a future release when this feature is more stable.\",\n  )\n  .option(\"--undo-global\", \"Re-enable local deployments on this machine.\")\n  .allowExcessArguments(false)\n  .action(async (cmdOptions) => {\n    const ctx = await oneoffContext({\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n\n    if (cmdOptions.undoGlobal) {\n      return disableLocalDeploymentsGloballyUntilBetaOver(true);\n    }\n    if (cmdOptions.global) {\n      return disableLocalDeploymentsGloballyUntilBetaOver(\n        !!cmdOptions.undoGlobal,\n      );\n    }\n\n    const deploymentSelection = await getDeploymentSelection(ctx, {\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    const configuredDeployment =\n      deploymentNameAndTypeFromSelection(deploymentSelection);\n    if (\n      configuredDeployment?.type !== null &&\n      configuredDeployment?.type !== \"local\"\n    ) {\n      logFinishedStep(ctx, \"Local development is already not being used.\");\n      return;\n    }\n\n    await deploymentCredentialsOrConfigure(ctx, deploymentSelection, \"ask\", {\n      selectionWithinProject: { kind: \"ownDev\" },\n      prod: false,\n      localOptions: {\n        forceUpgrade: false,\n      },\n      cloud: true,\n    });\n\n    logFinishedStep(\n      ctx,\n      \"You are no longer using a local deployment for development.\",\n    );\n  });\n\nasync function disableLocalDeploymentsGloballyUntilBetaOver(\n  reenable: boolean,\n): Promise<void> {\n  const ctx = await oneoffContext({\n    url: undefined,\n    adminKey: undefined,\n    envFile: undefined,\n  });\n\n  // Ensure this is not used in CI or scripts, since it has global effects and will be deprecated\n  // in the future.\n  if (!process.stdin.isTTY) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage:\n        \"`disable-local-deployments --global` is not for scripting, it is temporary and only for interactive use.\",\n    });\n  }\n  const config = readGlobalConfig(ctx);\n  if (config === null) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Log in first with `npx convex login\",\n    });\n  }\n\n  if (reenable) {\n    if (\n      !(\"optOutOfLocalDevDeploymentsUntilBetaOver\" in config) ||\n      !config.optOutOfLocalDevDeploymentsUntilBetaOver\n    ) {\n      logFinishedStep(\n        ctx,\n        \"You are already opted into allowing local deployents on this machine.\",\n      );\n      return;\n    }\n    await modifyGlobalConfig(ctx, {\n      ...config,\n      optOutOfLocalDevDeploymentsUntilBetaOver: false,\n    });\n\n    logFinishedStep(\n      ctx,\n      \"You have been opted back into allowing local deployents on this machine.\",\n    );\n    return;\n  }\n\n  if (\n    \"optOutOfLocalDevDeploymentsUntilBetaOver\" in config &&\n    config.optOutOfLocalDevDeploymentsUntilBetaOver\n  ) {\n    logFinishedStep(\n      ctx,\n      \"You are already opted out of local deployents on this machine.\",\n    );\n    return;\n  }\n  await modifyGlobalConfig(ctx, {\n    ...config,\n    optOutOfLocalDevDeploymentsUntilBetaOver: true,\n  });\n\n  logFinishedStep(\n    ctx,\n    \"You have been opted out of local deployents on this machine until the beta is over. Run `npx convex disable-local-deployments --undo-global` to opt back in.\",\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAwB;AACxB,qBAA+C;AAC/C,uBAAiD;AACjD,0BAGO;AACP,iCAGO;AAEA,MAAM,0BAA0B,IAAI,6BAAQ,2BAA2B,EAC3E;AAAA,EACC;AACF,EACC;AAAA,EACC;AAAA,EACA;AACF,EACC,OAAO,iBAAiB,8CAA8C,EACtE,qBAAqB,KAAK,EAC1B,OAAO,OAAO,eAAe;AAC5B,QAAM,MAAM,UAAM,8BAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AAED,MAAI,WAAW,YAAY;AACzB,WAAO,6CAA6C,IAAI;AAAA,EAC1D;AACA,MAAI,WAAW,QAAQ;AACrB,WAAO;AAAA,MACL,CAAC,CAAC,WAAW;AAAA,IACf;AAAA,EACF;AAEA,QAAM,sBAAsB,UAAM,mDAAuB,KAAK;AAAA,IAC5D,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,2BACJ,+DAAmC,mBAAmB;AACxD,MACE,sBAAsB,SAAS,QAC/B,sBAAsB,SAAS,SAC/B;AACA,wCAAgB,KAAK,8CAA8C;AACnE;AAAA,EACF;AAEA,YAAM,mDAAiC,KAAK,qBAAqB,OAAO;AAAA,IACtE,wBAAwB,EAAE,MAAM,SAAS;AAAA,IACzC,MAAM;AAAA,IACN,cAAc;AAAA,MACZ,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AAED;AAAA,IACE;AAAA,IACA;AAAA,EACF;AACF,CAAC;AAEH,eAAe,6CACb,UACe;AACf,QAAM,MAAM,UAAM,8BAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AAID,MAAI,CAAC,QAAQ,MAAM,OAAO;AACxB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AACA,QAAM,aAAS,sCAAiB,GAAG;AACnC,MAAI,WAAW,MAAM;AACnB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,MAAI,UAAU;AACZ,QACE,EAAE,8CAA8C,WAChD,CAAC,OAAO,0CACR;AACA;AAAA,QACE;AAAA,QACA;AAAA,MACF;AACA;AAAA,IACF;AACA,cAAM,wCAAmB,KAAK;AAAA,MAC5B,GAAG;AAAA,MACH,0CAA0C;AAAA,IAC5C,CAAC;AAED;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA;AAAA,EACF;AAEA,MACE,8CAA8C,UAC9C,OAAO,0CACP;AACA;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA;AAAA,EACF;AACA,YAAM,wCAAmB,KAAK;AAAA,IAC5B,GAAG;AAAA,IACH,0CAA0C;AAAA,EAC5C,CAAC;AAED;AAAA,IACE;AAAA,IACA;AAAA,EACF;AACF;", "names": []}
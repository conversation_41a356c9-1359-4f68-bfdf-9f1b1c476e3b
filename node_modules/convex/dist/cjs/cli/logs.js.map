{"version": 3, "sources": ["../../../src/cli/logs.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport { oneoffContext } from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { logsForDeployment } from \"./lib/logs.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\n\nexport const logs = new Command(\"logs\")\n  .summary(\"Watch logs from your deployment\")\n  .description(\n    \"Stream function logs from your Convex deployment.\\nBy default, this streams from your project's dev deployment.\",\n  )\n  .allowExcessArguments(false)\n  .addLogsOptions()\n  .addDeploymentSelectionOptions(actionDescription(\"Watch logs from\"))\n  .showHelpAfterError()\n  .action(async (cmdOptions) => {\n    const ctx = await oneoffContext(cmdOptions);\n\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, cmdOptions);\n    const deploymentSelection = await getDeploymentSelection(ctx, cmdOptions);\n    const deployment = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n    );\n    const deploymentName = deployment.deploymentFields?.deploymentName\n      ? ` ${deployment.deploymentFields.deploymentName}`\n      : \"\";\n    const deploymentNotice = ` for ${cmdOptions.prod ? \"production\" : \"dev\"} deployment${deploymentName}`;\n    await logsForDeployment(ctx, deployment, {\n      history: cmdOptions.history,\n      success: cmdOptions.success,\n      deploymentNotice,\n    });\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAwB;AACxB,qBAA8B;AAC9B,iBAGO;AACP,qBAAkC;AAClC,kBAAkC;AAClC,iCAAuC;AAEhC,MAAM,OAAO,IAAI,6BAAQ,MAAM,EACnC,QAAQ,iCAAiC,EACzC;AAAA,EACC;AACF,EACC,qBAAqB,KAAK,EAC1B,eAAe,EACf,kCAA8B,kCAAkB,iBAAiB,CAAC,EAClE,mBAAmB,EACnB,OAAO,OAAO,eAAe;AAC5B,QAAM,MAAM,UAAM,8BAAc,UAAU;AAE1C,QAAM,yBACJ,UAAM,wDAA4C,KAAK,UAAU;AACnE,QAAM,sBAAsB,UAAM,mDAAuB,KAAK,UAAU;AACxE,QAAM,aAAa,UAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,iBAAiB,WAAW,kBAAkB,iBAChD,IAAI,WAAW,iBAAiB,cAAc,KAC9C;AACJ,QAAM,mBAAmB,QAAQ,WAAW,OAAO,eAAe,KAAK,cAAc,cAAc;AACnG,YAAM,+BAAkB,KAAK,YAAY;AAAA,IACvC,SAAS,WAAW;AAAA,IACpB,SAAS,WAAW;AAAA,IACpB;AAAA,EACF,CAAC;AACH,CAAC;", "names": []}
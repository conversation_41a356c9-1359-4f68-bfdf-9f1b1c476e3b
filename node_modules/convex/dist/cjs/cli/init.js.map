{"version": 3, "sources": ["../../../src/cli/init.ts"], "sourcesContent": ["import { Command, Option } from \"@commander-js/extra-typings\";\nimport path from \"path\";\nimport { oneoffContext } from \"../bundler/context.js\";\n\nconst cwd = path.basename(process.cwd());\n\n// Initialize a new Convex project.\n// This command is deprecated and hidden from the command help.\n// `npx convex dev --once --configure=new` replaces it.\nexport const init = new Command(\"init\")\n  .description(\"Initialize a new Convex project in the current directory\")\n  .allowExcessArguments(false)\n  .addOption(\n    new Option(\n      \"--project <name>\",\n      `Name of the project to create. Defaults to \\`${cwd}\\` (the current directory)`,\n    ),\n  )\n  .addOption(\n    new Option(\n      \"--team <slug>\",\n      \"Slug identifier for the team this project will belong to.\",\n    ),\n  )\n  .action(async (_options) => {\n    return (\n      await oneoffContext({\n        url: undefined,\n        adminKey: undefined,\n        envFile: undefined,\n      })\n    ).crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      errForSentry:\n        \"The `init` command is deprecated. Use `npx convex dev --once --configure=new` instead.\",\n      printedMessage:\n        \"The `init` command is deprecated. Use `npx convex dev --once --configure=new` instead.\",\n    });\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAgC;AAChC,kBAAiB;AACjB,qBAA8B;AAE9B,MAAM,MAAM,YAAAA,QAAK,SAAS,QAAQ,IAAI,CAAC;AAKhC,MAAM,OAAO,IAAI,6BAAQ,MAAM,EACnC,YAAY,0DAA0D,EACtE,qBAAqB,KAAK,EAC1B;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA,gDAAgD,GAAG;AAAA,EACrD;AACF,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF,EACC,OAAO,OAAO,aAAa;AAC1B,UACE,UAAM,8BAAc;AAAA,IAClB,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC,GACD,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cACE;AAAA,IACF,gBACE;AAAA,EACJ,CAAC;AACH,CAAC;", "names": ["path"]}
{"version": 3, "sources": ["../../../src/cli/deployments.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport { readProjectConfig } from \"./lib/config.js\";\nimport chalk from \"chalk\";\nimport { bigBrainAPI } from \"./lib/utils/utils.js\";\nimport {\n  logError,\n  logMessage,\n  logOutput,\n  oneoffContext,\n} from \"../bundler/context.js\";\n\ntype Deployment = {\n  id: number;\n  name: string;\n  create_time: number;\n  deployment_type: \"dev\" | \"prod\";\n};\n\nexport const deployments = new Command(\"deployments\")\n  .description(\"List deployments associated with a project\")\n  .allowExcessArguments(false)\n  .action(async () => {\n    const ctx = await oneoffContext({\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    const { projectConfig: config } = await readProjectConfig(ctx);\n\n    const url = `teams/${config.team}/projects/${config.project}/deployments`;\n\n    logMessage(ctx, `Deployments for project ${config.team}/${config.project}`);\n    const deployments = (await bigBrainAPI({\n      ctx,\n      method: \"GET\",\n      url,\n    })) as Deployment[];\n    logOutput(ctx, deployments);\n    if (deployments.length === 0) {\n      logError(ctx, chalk.yellow(`No deployments exist for project`));\n    }\n  });\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAwB;AACxB,oBAAkC;AAClC,mBAAkB;AAClB,mBAA4B;AAC5B,qBAKO;AASA,MAAM,cAAc,IAAI,6BAAQ,aAAa,EACjD,YAAY,4CAA4C,EACxD,qBAAqB,KAAK,EAC1B,OAAO,YAAY;AAClB,QAAM,MAAM,UAAM,8BAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,EAAE,eAAe,OAAO,IAAI,UAAM,iCAAkB,GAAG;AAE7D,QAAM,MAAM,SAAS,OAAO,IAAI,aAAa,OAAO,OAAO;AAE3D,iCAAW,KAAK,2BAA2B,OAAO,IAAI,IAAI,OAAO,OAAO,EAAE;AAC1E,QAAMA,eAAe,UAAM,0BAAY;AAAA,IACrC;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF,CAAC;AACD,gCAAU,KAAKA,YAAW;AAC1B,MAAIA,aAAY,WAAW,GAAG;AAC5B,iCAAS,KAAK,aAAAC,QAAM,OAAO,kCAAkC,CAAC;AAAA,EAChE;AACF,CAAC;", "names": ["deployments", "chalk"]}
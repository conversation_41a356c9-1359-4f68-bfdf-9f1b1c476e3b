{"version": 3, "sources": ["../../../src/cli/docs.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport chalk from \"chalk\";\nimport open from \"open\";\nimport { Context, logMessage, oneoffContext } from \"../bundler/context.js\";\nimport { bigBrainFetch, deprecationCheckWarning } from \"./lib/utils/utils.js\";\nimport {\n  getDeploymentSelection,\n  deploymentNameFromSelection,\n} from \"./lib/deploymentSelection.js\";\n\nexport const docs = new Command(\"docs\")\n  .description(\"Open the docs in the browser\")\n  .allowExcessArguments(false)\n  .option(\"--no-open\", \"Print docs URL instead of opening it in your browser\")\n  .action(async (options) => {\n    const ctx = await oneoffContext({\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    const deploymentSelection = await getDeploymentSelection(ctx, {\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    const configuredDeployment =\n      deploymentNameFromSelection(deploymentSelection);\n    if (configuredDeployment === null) {\n      await openDocs(ctx, options.open);\n      return;\n    }\n    const getCookieUrl = `get_cookie/${configuredDeployment}`;\n    const fetch = await bigBrainFetch(ctx);\n    try {\n      const res = await fetch(getCookieUrl);\n      deprecationCheckWarning(ctx, res);\n      const { cookie } = await res.json();\n      await openDocs(ctx, options.open, cookie);\n    } catch {\n      await openDocs(ctx, options.open);\n    }\n  });\n\nasync function openDocs(ctx: Context, toOpen: boolean, cookie?: string) {\n  let docsUrl = \"https://docs.convex.dev\";\n  if (cookie !== undefined) {\n    docsUrl += \"/?t=\" + cookie;\n  }\n  if (toOpen) {\n    await open(docsUrl);\n    logMessage(ctx, chalk.green(\"Docs have launched! Check your browser.\"));\n  } else {\n    logMessage(ctx, chalk.green(`Find Convex docs here: ${docsUrl}`));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAwB;AACxB,mBAAkB;AAClB,kBAAiB;AACjB,qBAAmD;AACnD,mBAAuD;AACvD,iCAGO;AAEA,MAAM,OAAO,IAAI,6BAAQ,MAAM,EACnC,YAAY,8BAA8B,EAC1C,qBAAqB,KAAK,EAC1B,OAAO,aAAa,sDAAsD,EAC1E,OAAO,OAAO,YAAY;AACzB,QAAM,MAAM,UAAM,8BAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,sBAAsB,UAAM,mDAAuB,KAAK;AAAA,IAC5D,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,2BACJ,wDAA4B,mBAAmB;AACjD,MAAI,yBAAyB,MAAM;AACjC,UAAM,SAAS,KAAK,QAAQ,IAAI;AAChC;AAAA,EACF;AACA,QAAM,eAAe,cAAc,oBAAoB;AACvD,QAAM,QAAQ,UAAM,4BAAc,GAAG;AACrC,MAAI;AACF,UAAM,MAAM,MAAM,MAAM,YAAY;AACpC,8CAAwB,KAAK,GAAG;AAChC,UAAM,EAAE,OAAO,IAAI,MAAM,IAAI,KAAK;AAClC,UAAM,SAAS,KAAK,QAAQ,MAAM,MAAM;AAAA,EAC1C,QAAQ;AACN,UAAM,SAAS,KAAK,QAAQ,IAAI;AAAA,EAClC;AACF,CAAC;AAEH,eAAe,SAAS,KAAc,QAAiB,QAAiB;AACtE,MAAI,UAAU;AACd,MAAI,WAAW,QAAW;AACxB,eAAW,SAAS;AAAA,EACtB;AACA,MAAI,QAAQ;AACV,cAAM,YAAAA,SAAK,OAAO;AAClB,mCAAW,KAAK,aAAAC,QAAM,MAAM,yCAAyC,CAAC;AAAA,EACxE,OAAO;AACL,mCAAW,KAAK,aAAAA,QAAM,MAAM,0BAA0B,OAAO,EAAE,CAAC;AAAA,EAClE;AACF;", "names": ["open", "chalk"]}
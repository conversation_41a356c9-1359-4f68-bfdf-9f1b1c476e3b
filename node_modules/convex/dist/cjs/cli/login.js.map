{"version": 3, "sources": ["../../../src/cli/login.ts"], "sourcesContent": ["import { Command, Option } from \"@commander-js/extra-typings\";\nimport {\n  Context,\n  logFailure,\n  logFinishedStep,\n  logMessage,\n  oneoffContext,\n} from \"../bundler/context.js\";\nimport { checkAuthorization, performLogin } from \"./lib/login.js\";\nimport { loadUuidForAnonymousUser } from \"./lib/localDeployment/filePaths.js\";\nimport {\n  handleLinkToProject,\n  listExistingAnonymousDeployments,\n} from \"./lib/localDeployment/anonymous.js\";\nimport {\n  DASHBOARD_HOST,\n  deploymentDashboardUrlPage,\n  teamDashboardUrl,\n} from \"./lib/dashboard.js\";\nimport { promptSearch, promptYesNo } from \"./lib/utils/prompts.js\";\nimport { bigBrainAPI, validateOrSelectTeam } from \"./lib/utils/utils.js\";\nimport {\n  selectProject,\n  updateEnvAndConfigForDeploymentSelection,\n} from \"./configure.js\";\nimport {\n  getDeploymentSelection,\n  shouldAllowAnonymousDevelopment,\n} from \"./lib/deploymentSelection.js\";\nimport { removeAnonymousPrefix } from \"./lib/deployment.js\";\n\nexport const login = new Command(\"login\")\n  .description(\"Login to Convex\")\n  .allowExcessArguments(false)\n  .option(\n    \"--device-name <name>\",\n    \"Provide a name for the device being authorized\",\n  )\n  .option(\n    \"-f, --force\",\n    \"Proceed with login even if a valid access token already exists for this device\",\n  )\n  .option(\n    \"--no-open\",\n    \"Don't automatically open the login link in the default browser\",\n  )\n  .addOption(\n    new Option(\n      \"--login-flow <mode>\",\n      `How to log in; defaults to guessing based on the environment.`,\n    )\n      .choices([\"paste\", \"auto\", \"poll\"] as const)\n      .default(\"auto\" as const),\n  )\n  .addOption(new Option(\"--link-deployments\").hideHelp())\n  // These options are hidden from the help/usage message, but allow overriding settings for testing.\n  // Change the auth credentials with the auth provider\n  .addOption(new Option(\"--override-auth-url <url>\").hideHelp())\n  .addOption(new Option(\"--override-auth-client <id>\").hideHelp())\n  .addOption(new Option(\"--override-auth-username <username>\").hideHelp())\n  .addOption(new Option(\"--override-auth-password <password>\").hideHelp())\n  // Skip the auth provider login and directly use this access token\n  .addOption(new Option(\"--override-access-token <token>\").hideHelp())\n  // Automatically accept opt ins without prompting\n  .addOption(new Option(\"--accept-opt-ins\").hideHelp())\n  // Dump the access token from the auth provider and skip authorization with Convex\n  .addOption(new Option(\"--dump-access-token\").hideHelp())\n  // Hidden option for tests to check if the user is logged in.\n  .addOption(new Option(\"--check-login\").hideHelp())\n  .action(async (options, cmd: Command) => {\n    const ctx = await oneoffContext({\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    if (\n      !options.force &&\n      (await checkAuthorization(ctx, !!options.acceptOptIns))\n    ) {\n      logFinishedStep(\n        ctx,\n        \"This device has previously been authorized and is ready for use with Convex.\",\n      );\n      await handleLinkingDeployments(ctx, {\n        interactive: !!options.linkDeployments,\n      });\n      return;\n    }\n    if (!options.force && options.checkLogin) {\n      const isLoggedIn = await checkAuthorization(ctx, !!options.acceptOptIns);\n      if (!isLoggedIn) {\n        return ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          errForSentry: \"You are not logged in.\",\n          printedMessage: \"You are not logged in.\",\n        });\n      }\n    }\n    if (!!options.overrideAuthUsername !== !!options.overrideAuthPassword) {\n      cmd.error(\n        \"If overriding credentials, both username and password must be provided\",\n      );\n    }\n\n    const uuid = loadUuidForAnonymousUser(ctx);\n    await performLogin(ctx, {\n      ...options,\n      anonymousId: uuid,\n    });\n\n    await handleLinkingDeployments(ctx, {\n      interactive: !!options.linkDeployments,\n    });\n  });\n\nasync function handleLinkingDeployments(\n  ctx: Context,\n  args: {\n    interactive: boolean;\n  },\n) {\n  if (!shouldAllowAnonymousDevelopment()) {\n    return;\n  }\n  const anonymousDeployments = await listExistingAnonymousDeployments(ctx);\n  if (anonymousDeployments.length === 0) {\n    if (args.interactive) {\n      logMessage(\n        ctx,\n        \"It doesn't look like you have any deployments to link. You can run `npx convex dev` to set up a new project or select an existing one.\",\n      );\n    }\n    return;\n  }\n\n  if (!args.interactive) {\n    const message = getMessage(\n      anonymousDeployments.map((d) => d.deploymentName),\n    );\n    const createProjects = await promptYesNo(ctx, {\n      message,\n      default: true,\n    });\n    if (!createProjects) {\n      logMessage(\n        ctx,\n        \"Not linking your existing deployments. If you want to link them later, run `npx convex login --link-deployments`.\",\n      );\n      logMessage(\n        ctx,\n        `Visit ${DASHBOARD_HOST} or run \\`npx convex dev\\` to get started with your new account.`,\n      );\n      return;\n    }\n\n    const { teamSlug } = await validateOrSelectTeam(\n      ctx,\n      undefined,\n      \"Choose a team for your deployments:\",\n    );\n    const projectsRemaining = await getProjectsRemaining(ctx, teamSlug);\n    if (anonymousDeployments.length > projectsRemaining) {\n      logFailure(\n        ctx,\n        `You have ${anonymousDeployments.length} deployments to link, but only have ${projectsRemaining} projects remaining. If you'd like to choose which ones to link, run this command with the --link-deployments flag.`,\n      );\n      return;\n    }\n\n    const deploymentSelection = await getDeploymentSelection(ctx, {\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    const configuredDeployment =\n      deploymentSelection.kind === \"anonymous\"\n        ? deploymentSelection.deploymentName\n        : null;\n\n    let dashboardUrl = teamDashboardUrl(teamSlug);\n\n    for (const deployment of anonymousDeployments) {\n      const linkedDeployment = await handleLinkToProject(ctx, {\n        deploymentName: deployment.deploymentName,\n        teamSlug,\n        projectSlug: null,\n      });\n      logFinishedStep(\n        ctx,\n        `Added ${deployment.deploymentName} to project ${linkedDeployment.projectSlug}`,\n      );\n      if (deployment.deploymentName === configuredDeployment) {\n        // If the current project has a `CONVEX_DEPLOYMENT` env var configured, replace\n        // it with the new value.\n        await updateEnvAndConfigForDeploymentSelection(\n          ctx,\n          {\n            url: linkedDeployment.deploymentUrl,\n            deploymentName: linkedDeployment.deploymentName,\n            teamSlug,\n            projectSlug: linkedDeployment.projectSlug,\n            deploymentType: \"local\",\n          },\n          configuredDeployment,\n        );\n        dashboardUrl = deploymentDashboardUrlPage(\n          linkedDeployment.deploymentName,\n          \"\",\n        );\n      }\n    }\n    logFinishedStep(\n      ctx,\n      `Sucessfully linked your deployments! Visit ${dashboardUrl} to get started.`,\n    );\n    return;\n  }\n\n  const deploymentSelection = await getDeploymentSelection(ctx, {\n    url: undefined,\n    adminKey: undefined,\n    envFile: undefined,\n  });\n  const configuredDeployment =\n    deploymentSelection.kind === \"anonymous\"\n      ? deploymentSelection.deploymentName\n      : null;\n  while (true) {\n    logMessage(\n      ctx,\n      getDeploymentListMessage(\n        anonymousDeployments.map((d) => d.deploymentName),\n      ),\n    );\n    const updatedAnonymousDeployments =\n      await listExistingAnonymousDeployments(ctx);\n    const deploymentToLink = await promptSearch(ctx, {\n      message: \"Which deployment would you like to link to your account?\",\n      choices: updatedAnonymousDeployments.map((d) => ({\n        name: d.deploymentName,\n        value: d.deploymentName,\n      })),\n    });\n    const { teamSlug } = await validateOrSelectTeam(\n      ctx,\n      undefined,\n      \"Choose a team for your deployment:\",\n    );\n    const { projectSlug } = await selectProject(ctx, \"ask\", {\n      team: teamSlug,\n      devDeployment: \"local\",\n      defaultProjectName: removeAnonymousPrefix(deploymentToLink),\n    });\n    const linkedDeployment = await handleLinkToProject(ctx, {\n      deploymentName: deploymentToLink,\n      teamSlug,\n      projectSlug,\n    });\n    logFinishedStep(\n      ctx,\n      `Added ${deploymentToLink} to project ${linkedDeployment.projectSlug}`,\n    );\n    if (deploymentToLink === configuredDeployment) {\n      await updateEnvAndConfigForDeploymentSelection(\n        ctx,\n        {\n          url: linkedDeployment.deploymentUrl,\n          deploymentName: linkedDeployment.deploymentName,\n          teamSlug,\n          projectSlug: linkedDeployment.projectSlug,\n          deploymentType: \"local\",\n        },\n        configuredDeployment,\n      );\n    }\n    const shouldContinue = await promptYesNo(ctx, {\n      message: \"Would you like to link another deployment?\",\n      default: true,\n    });\n    if (!shouldContinue) {\n      break;\n    }\n  }\n}\n\nasync function getProjectsRemaining(ctx: Context, teamSlug: string) {\n  const response = await bigBrainAPI<{ projectsRemaining: number }>({\n    ctx,\n    method: \"GET\",\n    url: `/api/teams/${teamSlug}/projects_remaining`,\n  });\n\n  return response.projectsRemaining;\n}\n\nfunction getDeploymentListMessage(anonymousDeploymentNames: string[]) {\n  let message = `You have ${anonymousDeploymentNames.length} existing deployments.`;\n  message += `\\n\\nDeployments:`;\n  for (const deploymentName of anonymousDeploymentNames) {\n    message += `\\n- ${deploymentName}`;\n  }\n  return message;\n}\n\nfunction getMessage(anonymousDeploymentNames: string[]) {\n  if (anonymousDeploymentNames.length === 1) {\n    return `Would you like to link your existing deployment to your account? (\"${anonymousDeploymentNames[0]}\")`;\n  }\n  let message = `You have ${anonymousDeploymentNames.length} existing deployments. Would you like to link them to your account?`;\n  message += `\\n\\nDeployments:`;\n  for (const deploymentName of anonymousDeploymentNames) {\n    message += `\\n- ${deploymentName}`;\n  }\n  message += `\\n\\nYou can alternatively run \\`npx convex login --link-deployments\\` to interactively choose which deployments to add.`;\n  return message;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAgC;AAChC,qBAMO;AACP,mBAAiD;AACjD,uBAAyC;AACzC,uBAGO;AACP,uBAIO;AACP,qBAA0C;AAC1C,mBAAkD;AAClD,uBAGO;AACP,iCAGO;AACP,wBAAsC;AAE/B,MAAM,QAAQ,IAAI,6BAAQ,OAAO,EACrC,YAAY,iBAAiB,EAC7B,qBAAqB,KAAK,EAC1B;AAAA,EACC;AAAA,EACA;AACF,EACC;AAAA,EACC;AAAA,EACA;AACF,EACC;AAAA,EACC;AAAA,EACA;AACF,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,CAAC,SAAS,QAAQ,MAAM,CAAU,EAC1C,QAAQ,MAAe;AAC5B,EACC,UAAU,IAAI,4BAAO,oBAAoB,EAAE,SAAS,CAAC,EAGrD,UAAU,IAAI,4BAAO,2BAA2B,EAAE,SAAS,CAAC,EAC5D,UAAU,IAAI,4BAAO,6BAA6B,EAAE,SAAS,CAAC,EAC9D,UAAU,IAAI,4BAAO,qCAAqC,EAAE,SAAS,CAAC,EACtE,UAAU,IAAI,4BAAO,qCAAqC,EAAE,SAAS,CAAC,EAEtE,UAAU,IAAI,4BAAO,iCAAiC,EAAE,SAAS,CAAC,EAElE,UAAU,IAAI,4BAAO,kBAAkB,EAAE,SAAS,CAAC,EAEnD,UAAU,IAAI,4BAAO,qBAAqB,EAAE,SAAS,CAAC,EAEtD,UAAU,IAAI,4BAAO,eAAe,EAAE,SAAS,CAAC,EAChD,OAAO,OAAO,SAAS,QAAiB;AACvC,QAAM,MAAM,UAAM,8BAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,MACE,CAAC,QAAQ,SACR,UAAM,iCAAmB,KAAK,CAAC,CAAC,QAAQ,YAAY,GACrD;AACA;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA,UAAM,yBAAyB,KAAK;AAAA,MAClC,aAAa,CAAC,CAAC,QAAQ;AAAA,IACzB,CAAC;AACD;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,SAAS,QAAQ,YAAY;AACxC,UAAM,aAAa,UAAM,iCAAmB,KAAK,CAAC,CAAC,QAAQ,YAAY;AACvE,QAAI,CAAC,YAAY;AACf,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,CAAC,CAAC,QAAQ,yBAAyB,CAAC,CAAC,QAAQ,sBAAsB;AACrE,QAAI;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,WAAO,2CAAyB,GAAG;AACzC,YAAM,2BAAa,KAAK;AAAA,IACtB,GAAG;AAAA,IACH,aAAa;AAAA,EACf,CAAC;AAED,QAAM,yBAAyB,KAAK;AAAA,IAClC,aAAa,CAAC,CAAC,QAAQ;AAAA,EACzB,CAAC;AACH,CAAC;AAEH,eAAe,yBACb,KACA,MAGA;AACA,MAAI,KAAC,4DAAgC,GAAG;AACtC;AAAA,EACF;AACA,QAAM,uBAAuB,UAAM,mDAAiC,GAAG;AACvE,MAAI,qBAAqB,WAAW,GAAG;AACrC,QAAI,KAAK,aAAa;AACpB;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA;AAAA,EACF;AAEA,MAAI,CAAC,KAAK,aAAa;AACrB,UAAM,UAAU;AAAA,MACd,qBAAqB,IAAI,CAAC,MAAM,EAAE,cAAc;AAAA,IAClD;AACA,UAAM,iBAAiB,UAAM,4BAAY,KAAK;AAAA,MAC5C;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AACD,QAAI,CAAC,gBAAgB;AACnB;AAAA,QACE;AAAA,QACA;AAAA,MACF;AACA;AAAA,QACE;AAAA,QACA,SAAS,+BAAc;AAAA,MACzB;AACA;AAAA,IACF;AAEA,UAAM,EAAE,SAAS,IAAI,UAAM;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,oBAAoB,MAAM,qBAAqB,KAAK,QAAQ;AAClE,QAAI,qBAAqB,SAAS,mBAAmB;AACnD;AAAA,QACE;AAAA,QACA,YAAY,qBAAqB,MAAM,uCAAuC,iBAAiB;AAAA,MACjG;AACA;AAAA,IACF;AAEA,UAAMA,uBAAsB,UAAM,mDAAuB,KAAK;AAAA,MAC5D,KAAK;AAAA,MACL,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AACD,UAAMC,wBACJD,qBAAoB,SAAS,cACzBA,qBAAoB,iBACpB;AAEN,QAAI,mBAAe,mCAAiB,QAAQ;AAE5C,eAAW,cAAc,sBAAsB;AAC7C,YAAM,mBAAmB,UAAM,sCAAoB,KAAK;AAAA,QACtD,gBAAgB,WAAW;AAAA,QAC3B;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AACD;AAAA,QACE;AAAA,QACA,SAAS,WAAW,cAAc,eAAe,iBAAiB,WAAW;AAAA,MAC/E;AACA,UAAI,WAAW,mBAAmBC,uBAAsB;AAGtD,kBAAM;AAAA,UACJ;AAAA,UACA;AAAA,YACE,KAAK,iBAAiB;AAAA,YACtB,gBAAgB,iBAAiB;AAAA,YACjC;AAAA,YACA,aAAa,iBAAiB;AAAA,YAC9B,gBAAgB;AAAA,UAClB;AAAA,UACAA;AAAA,QACF;AACA,2BAAe;AAAA,UACb,iBAAiB;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA;AAAA,MACE;AAAA,MACA,8CAA8C,YAAY;AAAA,IAC5D;AACA;AAAA,EACF;AAEA,QAAM,sBAAsB,UAAM,mDAAuB,KAAK;AAAA,IAC5D,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,uBACJ,oBAAoB,SAAS,cACzB,oBAAoB,iBACpB;AACN,SAAO,MAAM;AACX;AAAA,MACE;AAAA,MACA;AAAA,QACE,qBAAqB,IAAI,CAAC,MAAM,EAAE,cAAc;AAAA,MAClD;AAAA,IACF;AACA,UAAM,8BACJ,UAAM,mDAAiC,GAAG;AAC5C,UAAM,mBAAmB,UAAM,6BAAa,KAAK;AAAA,MAC/C,SAAS;AAAA,MACT,SAAS,4BAA4B,IAAI,CAAC,OAAO;AAAA,QAC/C,MAAM,EAAE;AAAA,QACR,OAAO,EAAE;AAAA,MACX,EAAE;AAAA,IACJ,CAAC;AACD,UAAM,EAAE,SAAS,IAAI,UAAM;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,EAAE,YAAY,IAAI,UAAM,gCAAc,KAAK,OAAO;AAAA,MACtD,MAAM;AAAA,MACN,eAAe;AAAA,MACf,wBAAoB,yCAAsB,gBAAgB;AAAA,IAC5D,CAAC;AACD,UAAM,mBAAmB,UAAM,sCAAoB,KAAK;AAAA,MACtD,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,IACF,CAAC;AACD;AAAA,MACE;AAAA,MACA,SAAS,gBAAgB,eAAe,iBAAiB,WAAW;AAAA,IACtE;AACA,QAAI,qBAAqB,sBAAsB;AAC7C,gBAAM;AAAA,QACJ;AAAA,QACA;AAAA,UACE,KAAK,iBAAiB;AAAA,UACtB,gBAAgB,iBAAiB;AAAA,UACjC;AAAA,UACA,aAAa,iBAAiB;AAAA,UAC9B,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,UAAM,iBAAiB,UAAM,4BAAY,KAAK;AAAA,MAC5C,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,qBAAqB,KAAc,UAAkB;AAClE,QAAM,WAAW,UAAM,0BAA2C;AAAA,IAChE;AAAA,IACA,QAAQ;AAAA,IACR,KAAK,cAAc,QAAQ;AAAA,EAC7B,CAAC;AAED,SAAO,SAAS;AAClB;AAEA,SAAS,yBAAyB,0BAAoC;AACpE,MAAI,UAAU,YAAY,yBAAyB,MAAM;AACzD,aAAW;AAAA;AAAA;AACX,aAAW,kBAAkB,0BAA0B;AACrD,eAAW;AAAA,IAAO,cAAc;AAAA,EAClC;AACA,SAAO;AACT;AAEA,SAAS,WAAW,0BAAoC;AACtD,MAAI,yBAAyB,WAAW,GAAG;AACzC,WAAO,sEAAsE,yBAAyB,CAAC,CAAC;AAAA,EAC1G;AACA,MAAI,UAAU,YAAY,yBAAyB,MAAM;AACzD,aAAW;AAAA;AAAA;AACX,aAAW,kBAAkB,0BAA0B;AACrD,eAAW;AAAA,IAAO,cAAc;AAAA,EAClC;AACA,aAAW;AAAA;AAAA;AACX,SAAO;AACT;", "names": ["deploymentSelection", "configuredDeployment"]}
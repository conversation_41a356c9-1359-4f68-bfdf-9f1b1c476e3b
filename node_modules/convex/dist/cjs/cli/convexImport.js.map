{"version": 3, "sources": ["../../../src/cli/convexImport.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { ensureHasConvexDependency } from \"./lib/utils/utils.js\";\nimport { oneoffContext } from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { Command } from \"@commander-js/extra-typings\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { deploymentDashboardUrlPage } from \"./lib/dashboard.js\";\nimport { importIntoDeployment } from \"./lib/convexImport.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\n\nexport const convexImport = new Command(\"import\")\n  .summary(\"Import data from a file to your deployment\")\n  .description(\n    \"Import data from a file to your Convex deployment.\\n\\n\" +\n      \"  From a snapshot: `npx convex import snapshot.zip`\\n\" +\n      \"  For a single table: `npx convex import --table tableName file.json`\\n\\n\" +\n      \"By default, this imports into your dev deployment.\",\n  )\n  .allowExcessArguments(false)\n  .addImportOptions()\n  .addDeploymentSelectionOptions(actionDescription(\"Import data into\"))\n  .showHelpAfterError()\n  .action(async (filePath, options) => {\n    const ctx = await oneoffContext(options);\n\n    await ensureHasConvexDependency(ctx, \"import\");\n\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n    const deployment = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n    );\n\n    const deploymentNotice = options.prod\n      ? ` in your ${chalk.bold(\"prod\")} deployment`\n      : \"\";\n\n    await importIntoDeployment(ctx, filePath, {\n      ...options,\n      deploymentUrl: deployment.url,\n      adminKey: deployment.adminKey,\n      deploymentNotice,\n      snapshotImportDashboardLink: snapshotImportDashboardLink(\n        deployment.deploymentFields?.deploymentName ?? null,\n      ),\n    });\n  });\n\nfunction snapshotImportDashboardLink(deploymentName: string | null) {\n  return deploymentName === null\n    ? \"https://dashboard.convex.dev/deployment/settings/snapshots\"\n    : deploymentDashboardUrlPage(deploymentName, \"/settings/snapshots\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,mBAA0C;AAC1C,qBAA8B;AAC9B,iBAGO;AACP,2BAAwB;AACxB,qBAAkC;AAClC,uBAA2C;AAC3C,0BAAqC;AACrC,iCAAuC;AAEhC,MAAM,eAAe,IAAI,6BAAQ,QAAQ,EAC7C,QAAQ,4CAA4C,EACpD;AAAA,EACC;AAIF,EACC,qBAAqB,KAAK,EAC1B,iBAAiB,EACjB,kCAA8B,kCAAkB,kBAAkB,CAAC,EACnE,mBAAmB,EACnB,OAAO,OAAO,UAAU,YAAY;AACnC,QAAM,MAAM,UAAM,8BAAc,OAAO;AAEvC,YAAM,wCAA0B,KAAK,QAAQ;AAE7C,QAAM,yBACJ,UAAM,wDAA4C,KAAK,OAAO;AAEhE,QAAM,sBAAsB,UAAM,mDAAuB,KAAK,OAAO;AACrE,QAAM,aAAa,UAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,mBAAmB,QAAQ,OAC7B,YAAY,aAAAA,QAAM,KAAK,MAAM,CAAC,gBAC9B;AAEJ,YAAM,0CAAqB,KAAK,UAAU;AAAA,IACxC,GAAG;AAAA,IACH,eAAe,WAAW;AAAA,IAC1B,UAAU,WAAW;AAAA,IACrB;AAAA,IACA,6BAA6B;AAAA,MAC3B,WAAW,kBAAkB,kBAAkB;AAAA,IACjD;AAAA,EACF,CAAC;AACH,CAAC;AAEH,SAAS,4BAA4B,gBAA+B;AAClE,SAAO,mBAAmB,OACtB,mEACA,6CAA2B,gBAAgB,qBAAqB;AACtE;", "names": ["chalk"]}
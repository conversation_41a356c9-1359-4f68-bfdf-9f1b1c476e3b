{"version": 3, "sources": ["../../../../src/cli/lib/config.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport equal from \"deep-equal\";\nimport { EOL } from \"os\";\nimport path from \"path\";\nimport {\n  changeSpinner,\n  Context,\n  logError,\n  logFailure,\n  logFinishedStep,\n  logMessage,\n  showSpinner,\n} from \"../../bundler/context.js\";\nimport {\n  Bundle,\n  BundleHash,\n  bundle,\n  bundleAuthConfig,\n  entryPointsByEnvironment,\n} from \"../../bundler/index.js\";\nimport { version } from \"../version.js\";\nimport { deploymentDashboardUrlPage } from \"./dashboard.js\";\nimport {\n  formatSize,\n  functionsDir,\n  ErrorData,\n  loadPackageJson,\n  deploymentFetch,\n  deprecationCheckWarning,\n  logAndHandleFetchError,\n  ThrowingFetchError,\n} from \"./utils/utils.js\";\nimport { createHash } from \"crypto\";\nimport { promisify } from \"util\";\nimport zlib from \"zlib\";\nimport { recursivelyDelete } from \"./fsUtils.js\";\nimport { NodeDependency } from \"./deployApi/modules.js\";\nimport { ComponentDefinitionPath } from \"./components/definition/directoryStructure.js\";\nimport {\n  LocalDeploymentError,\n  printLocalDeploymentOnError,\n} from \"./localDeployment/errors.js\";\nexport { productionProvisionHost, provisionHost } from \"./utils/utils.js\";\n\nconst brotli = promisify(zlib.brotliCompress);\n\n/** Type representing auth configuration. */\nexport interface AuthInfo {\n  // Provider-specific application identifier. Corresponds to the `aud` field in an OIDC token.\n  applicationID: string;\n  // Domain used for authentication. Corresponds to the `iss` field in an OIDC token.\n  domain: string;\n}\n\n/** Type representing Convex project configuration. */\nexport interface ProjectConfig {\n  functions: string;\n  node: {\n    externalPackages: string[];\n  };\n  generateCommonJSApi: boolean;\n  // deprecated\n  project?: string;\n  // deprecated\n  team?: string;\n  // deprecated\n  prodUrl?: string;\n  // deprecated\n  authInfo?: AuthInfo[];\n\n  // These are beta flags for using static codegen from the `api.d.ts` and `dataModel.d.ts` files.\n  codegen: {\n    staticApi: boolean;\n    staticDataModel: boolean;\n  };\n}\n\nexport interface Config {\n  projectConfig: ProjectConfig;\n  modules: Bundle[];\n  nodeDependencies: NodeDependency[];\n  schemaId?: string;\n  udfServerVersion?: string;\n}\n\nexport interface ConfigWithModuleHashes {\n  projectConfig: ProjectConfig;\n  moduleHashes: BundleHash[];\n  nodeDependencies: NodeDependency[];\n  schemaId?: string;\n  udfServerVersion?: string;\n}\n\nconst DEFAULT_FUNCTIONS_PATH = \"convex/\";\n\n/** Check if object is of AuthInfo type. */\nfunction isAuthInfo(object: any): object is AuthInfo {\n  return (\n    \"applicationID\" in object &&\n    typeof object.applicationID === \"string\" &&\n    \"domain\" in object &&\n    typeof object.domain === \"string\"\n  );\n}\n\nfunction isAuthInfos(object: any): object is AuthInfo[] {\n  return Array.isArray(object) && object.every((item: any) => isAuthInfo(item));\n}\n\n/** Error parsing ProjectConfig representation. */\nclass ParseError extends Error {}\n\n/** Parse object to ProjectConfig. */\nexport async function parseProjectConfig(\n  ctx: Context,\n  obj: any,\n): Promise<ProjectConfig> {\n  if (typeof obj !== \"object\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: \"Expected `convex.json` to contain an object\",\n    });\n  }\n  if (typeof obj.node === \"undefined\") {\n    obj.node = {\n      externalPackages: [],\n    };\n  } else if (typeof obj.node.externalPackages === \"undefined\") {\n    obj.node.externalPackages = [];\n  } else if (\n    !Array.isArray(obj.node.externalPackages) ||\n    !obj.node.externalPackages.every((item: any) => typeof item === \"string\")\n  ) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage:\n        \"Expected `node.externalPackages` in `convex.json` to be an array of strings\",\n    });\n  }\n  if (typeof obj.generateCommonJSApi === \"undefined\") {\n    obj.generateCommonJSApi = false;\n  } else if (typeof obj.generateCommonJSApi !== \"boolean\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage:\n        \"Expected `generateCommonJSApi` in `convex.json` to be true or false\",\n    });\n  }\n\n  if (typeof obj.functions === \"undefined\") {\n    obj.functions = DEFAULT_FUNCTIONS_PATH;\n  } else if (typeof obj.functions !== \"string\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: \"Expected `functions` in `convex.json` to be a string\",\n    });\n  }\n\n  // Allow the `authInfo` key to be omitted, treating it as an empty list of providers.\n  if (obj.authInfo !== undefined) {\n    if (!isAuthInfos(obj.authInfo)) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage:\n          \"Expected `authInfo` in `convex.json` to be type AuthInfo[]\",\n      });\n    }\n  }\n\n  if (typeof obj.codegen === \"undefined\") {\n    obj.codegen = {};\n  }\n  if (typeof obj.codegen !== \"object\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: \"Expected `codegen` in `convex.json` to be an object\",\n    });\n  }\n  if (typeof obj.codegen.staticApi === \"undefined\") {\n    obj.codegen.staticApi = false;\n  }\n  if (typeof obj.codegen.staticDataModel === \"undefined\") {\n    obj.codegen.staticDataModel = false;\n  }\n  if (\n    typeof obj.codegen.staticApi !== \"boolean\" ||\n    typeof obj.codegen.staticDataModel !== \"boolean\"\n  ) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage:\n        \"Expected `codegen.staticApi` and `codegen.staticDataModel` in `convex.json` to be booleans\",\n    });\n  }\n\n  return obj;\n}\n\n// Parse a deployment config returned by the backend, picking out\n// the fields we care about.\nfunction parseBackendConfig(obj: any): {\n  functions: string;\n  authInfo?: AuthInfo[];\n} {\n  if (typeof obj !== \"object\") {\n    // Unexpected error\n    // eslint-disable-next-line no-restricted-syntax\n    throw new ParseError(\"Expected an object\");\n  }\n  const { functions, authInfo } = obj;\n  if (typeof functions !== \"string\") {\n    // Unexpected error\n    // eslint-disable-next-line no-restricted-syntax\n    throw new ParseError(\"Expected functions to be a string\");\n  }\n\n  // Allow the `authInfo` key to be omitted\n  if ((authInfo ?? null) !== null && !isAuthInfos(authInfo)) {\n    // Unexpected error\n    // eslint-disable-next-line no-restricted-syntax\n    throw new ParseError(\"Expected authInfo to be type AuthInfo[]\");\n  }\n\n  return {\n    functions,\n    ...((authInfo ?? null) !== null ? { authInfo: authInfo } : {}),\n  };\n}\n\nexport function configName(): string {\n  return \"convex.json\";\n}\n\nexport async function configFilepath(ctx: Context): Promise<string> {\n  const configFn = configName();\n  // We used to allow src/convex.json, but no longer (as of 10/7/2022).\n  // Leave an error message around to help people out. We can remove this\n  // error message after a couple months.\n  const preferredLocation = configFn;\n  const wrongLocation = path.join(\"src\", configFn);\n\n  // Allow either location, but not both.\n  const preferredLocationExists = ctx.fs.exists(preferredLocation);\n  const wrongLocationExists = ctx.fs.exists(wrongLocation);\n  if (preferredLocationExists && wrongLocationExists) {\n    const message = `${chalk.red(`Error: both ${preferredLocation} and ${wrongLocation} files exist!`)}\\nConsolidate these and remove ${wrongLocation}.`;\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: message,\n    });\n  }\n  if (!preferredLocationExists && wrongLocationExists) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `Error: Please move ${wrongLocation} to the root of your project`,\n    });\n  }\n\n  return preferredLocation;\n}\n\nexport async function getFunctionsDirectoryPath(ctx: Context): Promise<string> {\n  const { projectConfig, configPath } = await readProjectConfig(ctx);\n  return functionsDir(configPath, projectConfig);\n}\n\n/** Read configuration from a local `convex.json` file. */\nexport async function readProjectConfig(ctx: Context): Promise<{\n  projectConfig: ProjectConfig;\n  configPath: string;\n}> {\n  if (!ctx.fs.exists(\"convex.json\")) {\n    // create-react-app bans imports from outside of src, so we can just\n    // put the functions directory inside of src/ to work around this issue.\n    const packages = await loadPackageJson(ctx);\n    const isCreateReactApp = \"react-scripts\" in packages;\n    return {\n      projectConfig: {\n        functions: isCreateReactApp\n          ? `src/${DEFAULT_FUNCTIONS_PATH}`\n          : DEFAULT_FUNCTIONS_PATH,\n        node: {\n          externalPackages: [],\n        },\n        generateCommonJSApi: false,\n        codegen: {\n          staticApi: false,\n          staticDataModel: false,\n        },\n      },\n      configPath: configName(),\n    };\n  }\n  let projectConfig;\n  const configPath = await configFilepath(ctx);\n  try {\n    projectConfig = await parseProjectConfig(\n      ctx,\n      JSON.parse(ctx.fs.readUtf8File(configPath)),\n    );\n  } catch (err) {\n    if (err instanceof ParseError || err instanceof SyntaxError) {\n      logError(ctx, chalk.red(`Error: Parsing \"${configPath}\" failed`));\n      logMessage(ctx, chalk.gray(err.toString()));\n    } else {\n      logFailure(\n        ctx,\n        `Error: Unable to read project config file \"${configPath}\"\\n` +\n          \"  Are you running this command from the root directory of a Convex project? If so, run `npx convex dev` first.\",\n      );\n      if (err instanceof Error) {\n        logError(ctx, chalk.red(err.message));\n      }\n    }\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      errForSentry: err,\n      // TODO -- move the logging above in here\n      printedMessage: null,\n    });\n  }\n  return {\n    projectConfig,\n    configPath,\n  };\n}\n\nexport async function enforceDeprecatedConfigField(\n  ctx: Context,\n  config: ProjectConfig,\n  field: \"team\" | \"project\" | \"prodUrl\",\n): Promise<string> {\n  const value = config[field];\n  if (typeof value === \"string\") {\n    return value;\n  }\n  const err = new ParseError(`Expected ${field} to be a string`);\n  return await ctx.crash({\n    exitCode: 1,\n    errorType: \"invalid filesystem data\",\n    errForSentry: err,\n    printedMessage: `Error: Parsing convex.json failed:\\n${chalk.gray(err.toString())}`,\n  });\n}\n\n/**\n * Given a {@link ProjectConfig}, add in the bundled modules to produce the\n * complete config.\n */\nexport async function configFromProjectConfig(\n  ctx: Context,\n  projectConfig: ProjectConfig,\n  configPath: string,\n  verbose: boolean,\n): Promise<{\n  config: Config;\n  bundledModuleInfos: BundledModuleInfo[];\n}> {\n  const baseDir = functionsDir(configPath, projectConfig);\n  // We bundle functions entry points separately since they execute on different\n  // platforms.\n  const entryPoints = await entryPointsByEnvironment(ctx, baseDir);\n  // es-build prints errors to console which would clobber\n  // our spinner.\n  if (verbose) {\n    showSpinner(ctx, \"Bundling modules for Convex's runtime...\");\n  }\n  const convexResult = await bundle(\n    ctx,\n    baseDir,\n    entryPoints.isolate,\n    true,\n    \"browser\",\n  );\n  if (verbose) {\n    logMessage(\n      ctx,\n      \"Convex's runtime modules: \",\n      convexResult.modules.map((m) => m.path),\n    );\n  }\n\n  // Bundle node modules.\n  if (verbose && entryPoints.node.length !== 0) {\n    showSpinner(ctx, \"Bundling modules for Node.js runtime...\");\n  }\n  const nodeResult = await bundle(\n    ctx,\n    baseDir,\n    entryPoints.node,\n    true,\n    \"node\",\n    path.join(\"_deps\", \"node\"),\n    projectConfig.node.externalPackages,\n  );\n  if (verbose && entryPoints.node.length !== 0) {\n    logMessage(\n      ctx,\n      \"Node.js runtime modules: \",\n      nodeResult.modules.map((m) => m.path),\n    );\n    if (projectConfig.node.externalPackages.length > 0) {\n      logMessage(\n        ctx,\n        \"Node.js runtime external dependencies (to be installed on the server): \",\n        [...nodeResult.externalDependencies.entries()].map(\n          (a) => `${a[0]}: ${a[1]}`,\n        ),\n      );\n    }\n  }\n  const modules = convexResult.modules;\n  modules.push(...nodeResult.modules);\n  modules.push(...(await bundleAuthConfig(ctx, baseDir)));\n\n  const nodeDependencies: NodeDependency[] = [];\n  for (const [moduleName, moduleVersion] of nodeResult.externalDependencies) {\n    nodeDependencies.push({ name: moduleName, version: moduleVersion });\n  }\n\n  const bundledModuleInfos: BundledModuleInfo[] = Array.from(\n    convexResult.bundledModuleNames.keys(),\n  ).map((moduleName) => {\n    return {\n      name: moduleName,\n      platform: \"convex\",\n    };\n  });\n  bundledModuleInfos.push(\n    ...Array.from(nodeResult.bundledModuleNames.keys()).map(\n      (moduleName): BundledModuleInfo => {\n        return {\n          name: moduleName,\n          platform: \"node\",\n        };\n      },\n    ),\n  );\n\n  return {\n    config: {\n      projectConfig: projectConfig,\n      modules: modules,\n      nodeDependencies: nodeDependencies,\n      // We're just using the version this CLI is running with for now.\n      // This could be different than the version of `convex` the app runs with\n      // if the CLI is installed globally.\n      udfServerVersion: version,\n    },\n    bundledModuleInfos,\n  };\n}\n\n/**\n * Read the config from `convex.json` and bundle all the modules.\n */\nexport async function readConfig(\n  ctx: Context,\n  verbose: boolean,\n): Promise<{\n  config: Config;\n  configPath: string;\n  bundledModuleInfos: BundledModuleInfo[];\n}> {\n  const { projectConfig, configPath } = await readProjectConfig(ctx);\n  const { config, bundledModuleInfos } = await configFromProjectConfig(\n    ctx,\n    projectConfig,\n    configPath,\n    verbose,\n  );\n  return { config, configPath, bundledModuleInfos };\n}\n\nexport async function upgradeOldAuthInfoToAuthConfig(\n  ctx: Context,\n  config: ProjectConfig,\n  functionsPath: string,\n) {\n  if (config.authInfo !== undefined) {\n    const authConfigPathJS = path.resolve(functionsPath, \"auth.config.js\");\n    const authConfigPathTS = path.resolve(functionsPath, \"auth.config.js\");\n    const authConfigPath = ctx.fs.exists(authConfigPathJS)\n      ? authConfigPathJS\n      : authConfigPathTS;\n    const authConfigRelativePath = path.join(\n      config.functions,\n      ctx.fs.exists(authConfigPathJS) ? \"auth.config.js\" : \"auth.config.ts\",\n    );\n    if (ctx.fs.exists(authConfigPath)) {\n      await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage:\n          `Cannot set auth config in both \\`${authConfigRelativePath}\\` and convex.json,` +\n          ` remove it from convex.json`,\n      });\n    }\n    if (config.authInfo.length > 0) {\n      const providersStringLines = JSON.stringify(\n        config.authInfo,\n        null,\n        2,\n      ).split(EOL);\n      const indentedProvidersString = [providersStringLines[0]]\n        .concat(providersStringLines.slice(1).map((line) => `  ${line}`))\n        .join(EOL);\n      ctx.fs.writeUtf8File(\n        authConfigPath,\n        `\\\n  export default {\n    providers: ${indentedProvidersString},\n  };`,\n      );\n      logMessage(\n        ctx,\n        chalk.yellowBright(\n          `Moved auth config from config.json to \\`${authConfigRelativePath}\\``,\n        ),\n      );\n    }\n    delete config.authInfo;\n  }\n  return config;\n}\n\n/** Write the config to `convex.json` in the current working directory. */\nexport async function writeProjectConfig(\n  ctx: Context,\n  projectConfig: ProjectConfig,\n  { deleteIfAllDefault }: { deleteIfAllDefault: boolean } = {\n    deleteIfAllDefault: false,\n  },\n) {\n  const configPath = await configFilepath(ctx);\n  const strippedConfig = filterWriteableConfig(stripDefaults(projectConfig));\n  if (Object.keys(strippedConfig).length > 0) {\n    try {\n      const contents = JSON.stringify(strippedConfig, undefined, 2) + \"\\n\";\n      ctx.fs.writeUtf8File(configPath, contents, 0o644);\n    } catch (err) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        errForSentry: err,\n        printedMessage:\n          `Error: Unable to write project config file \"${configPath}\" in current directory\\n` +\n          \"  Are you running this command from the root directory of a Convex project?\",\n      });\n    }\n  } else if (deleteIfAllDefault && ctx.fs.exists(configPath)) {\n    ctx.fs.unlink(configPath);\n    logMessage(\n      ctx,\n      chalk.yellowBright(\n        `Deleted ${configPath} since it completely matched defaults`,\n      ),\n    );\n  }\n  ctx.fs.mkdir(functionsDir(configPath, projectConfig), {\n    allowExisting: true,\n  });\n}\n\nfunction stripDefaults(projectConfig: ProjectConfig): any {\n  const stripped: any = { ...projectConfig };\n  if (stripped.functions === DEFAULT_FUNCTIONS_PATH) {\n    delete stripped.functions;\n  }\n  if (Array.isArray(stripped.authInfo) && stripped.authInfo.length === 0) {\n    delete stripped.authInfo;\n  }\n  if (stripped.node.externalPackages.length === 0) {\n    delete stripped.node.externalPackages;\n  }\n  if (stripped.generateCommonJSApi === false) {\n    delete stripped.generateCommonJSApi;\n  }\n  // Remove \"node\" field if it has nothing nested under it\n  if (Object.keys(stripped.node).length === 0) {\n    delete stripped.node;\n  }\n  if (stripped.codegen.staticApi === false) {\n    delete stripped.codegen.staticApi;\n  }\n  if (stripped.codegen.staticDataModel === false) {\n    delete stripped.codegen.staticDataModel;\n  }\n  if (Object.keys(stripped.codegen).length === 0) {\n    delete stripped.codegen;\n  }\n  return stripped;\n}\n\nfunction filterWriteableConfig(projectConfig: any) {\n  const writeable: any = { ...projectConfig };\n  delete writeable.project;\n  delete writeable.team;\n  delete writeable.prodUrl;\n  return writeable;\n}\n\nexport function removedExistingConfig(\n  ctx: Context,\n  configPath: string,\n  options: { allowExistingConfig?: boolean },\n) {\n  if (!options.allowExistingConfig) {\n    return false;\n  }\n  recursivelyDelete(ctx, configPath);\n  logFinishedStep(ctx, `Removed existing ${configPath}`);\n  return true;\n}\n\n/** Pull configuration from the given remote origin. */\nexport async function pullConfig(\n  ctx: Context,\n  project: string | undefined,\n  team: string | undefined,\n  origin: string,\n  adminKey: string,\n): Promise<ConfigWithModuleHashes> {\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: origin,\n    adminKey,\n  });\n\n  changeSpinner(ctx, \"Downloading current deployment state...\");\n  try {\n    const res = await fetch(\"/api/get_config_hashes\", {\n      method: \"POST\",\n      body: JSON.stringify({ version, adminKey }),\n    });\n    deprecationCheckWarning(ctx, res);\n    const data = await res.json();\n    const backendConfig = parseBackendConfig(data.config);\n    const projectConfig = {\n      ...backendConfig,\n      // This field is not stored in the backend, which is ok since it is also\n      // not used to diff configs.\n      node: {\n        externalPackages: [],\n      },\n      // This field is not stored in the backend, it only affects the client.\n      generateCommonJSApi: false,\n      // This field is also not stored in the backend, it only affects the client.\n      codegen: {\n        staticApi: false,\n        staticDataModel: false,\n      },\n      project,\n      team,\n      prodUrl: origin,\n    };\n    return {\n      projectConfig,\n      moduleHashes: data.moduleHashes,\n      // TODO(presley): Add this to diffConfig().\n      nodeDependencies: data.nodeDependencies,\n      udfServerVersion: data.udfServerVersion,\n    };\n  } catch (err: unknown) {\n    logFailure(ctx, `Error: Unable to pull deployment config from ${origin}`);\n    return await logAndHandleFetchError(ctx, err);\n  }\n}\n\ninterface BundledModuleInfo {\n  name: string;\n  platform: \"node\" | \"convex\";\n}\n\n/**\n * A component definition spec contains enough information to create bundles\n * of code that must be analyzed in order to construct a ComponentDefinition.\n *\n * Most paths are relative to the directory of the definitionPath.\n */\nexport type ComponentDefinitionSpec = {\n  /** This path is relative to the app (root component) directory. */\n  definitionPath: ComponentDefinitionPath;\n\n  /** Dependencies are paths to the directory of the dependency component definition from the app (root component) directory */\n  dependencies: ComponentDefinitionPath[];\n\n  // All other paths are relative to the directory of the definitionPath above.\n  definition: Bundle;\n  schema: Bundle;\n  functions: Bundle[];\n};\n\nexport type AppDefinitionSpec = Omit<\n  ComponentDefinitionSpec,\n  \"definitionPath\"\n> & {\n  // Only app (root) component specs contain an auth bundle.\n  auth: Bundle | null;\n};\n\nexport type ComponentDefinitionSpecWithoutImpls = Omit<\n  ComponentDefinitionSpec,\n  \"schema\" | \"functions\"\n>;\nexport type AppDefinitionSpecWithoutImpls = Omit<\n  AppDefinitionSpec,\n  \"schema\" | \"functions\" | \"auth\"\n>;\n\nexport function configJSON(\n  config: Config,\n  adminKey: string,\n  schemaId?: string,\n  pushMetrics?: PushMetrics,\n  bundledModuleInfos?: BundledModuleInfo[],\n) {\n  // Override origin with the url\n  const projectConfig = {\n    projectSlug: config.projectConfig.project,\n    teamSlug: config.projectConfig.team,\n    functions: config.projectConfig.functions,\n    authInfo: config.projectConfig.authInfo,\n  };\n  return {\n    config: projectConfig,\n    modules: config.modules,\n    nodeDependencies: config.nodeDependencies,\n    udfServerVersion: config.udfServerVersion,\n    schemaId,\n    adminKey,\n    pushMetrics,\n    bundledModuleInfos,\n  };\n}\n\n// Time in seconds of various spans of time during a push.\nexport type PushMetrics = {\n  typecheck: number;\n  bundle: number;\n  schemaPush: number;\n  codePull: number;\n  totalBeforePush: number;\n};\n\n/** Push configuration to the given remote origin. */\nexport async function pushConfig(\n  ctx: Context,\n  config: Config,\n  options: {\n    adminKey: string;\n    url: string;\n    deploymentName: string | null;\n    pushMetrics?: PushMetrics;\n    schemaId?: string;\n    bundledModuleInfos?: BundledModuleInfo[];\n  },\n): Promise<void> {\n  const serializedConfig = configJSON(\n    config,\n    options.adminKey,\n    options.schemaId,\n    options.pushMetrics,\n    options.bundledModuleInfos,\n  );\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: options.url,\n    adminKey: options.adminKey,\n  });\n  try {\n    if (config.nodeDependencies.length > 0) {\n      changeSpinner(\n        ctx,\n        \"Installing external packages and deploying source code...\",\n      );\n    } else {\n      changeSpinner(ctx, \"Analyzing and deploying source code...\");\n    }\n    await fetch(\"/api/push_config\", {\n      body: await brotli(JSON.stringify(serializedConfig), {\n        params: {\n          [zlib.constants.BROTLI_PARAM_MODE]: zlib.constants.BROTLI_MODE_TEXT,\n          [zlib.constants.BROTLI_PARAM_QUALITY]: 4,\n        },\n      }),\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Content-Encoding\": \"br\",\n      },\n    });\n  } catch (error: unknown) {\n    await handlePushConfigError(\n      ctx,\n      error,\n      \"Error: Unable to push deployment config to \" + options.url,\n      options.deploymentName,\n    );\n  }\n}\n\ntype Files = { source: string; filename: string }[];\n\nexport type CodegenResponse =\n  | {\n      success: true;\n      files: Files;\n    }\n  | {\n      success: false;\n      error: string;\n    };\n\nfunction renderModule(module: {\n  path: string;\n  sourceMapSize: number;\n  sourceSize: number;\n}): string {\n  return (\n    module.path +\n    ` (${formatSize(module.sourceSize)}, source map ${module.sourceMapSize})`\n  );\n}\n\nfunction hash(bundle: Bundle) {\n  return createHash(\"sha256\")\n    .update(bundle.source)\n    .update(bundle.sourceMap || \"\")\n    .digest(\"hex\");\n}\n\ntype ModuleDiffStat = { count: number; size: number };\nexport type ModuleDiffStats = {\n  updated: ModuleDiffStat;\n  identical: ModuleDiffStat;\n  added: ModuleDiffStat;\n  numDropped: number;\n};\n\nfunction compareModules(\n  oldModules: BundleHash[],\n  newModules: Bundle[],\n): {\n  diffString: string;\n  stats: ModuleDiffStats;\n} {\n  let diff = \"\";\n  const oldModuleMap = new Map(\n    oldModules.map((value) => [value.path, value.hash]),\n  );\n  const newModuleMap = new Map(\n    newModules.map((value) => [\n      value.path,\n      {\n        hash: hash(value),\n        sourceMapSize: value.sourceMap?.length ?? 0,\n        sourceSize: value.source.length,\n      },\n    ]),\n  );\n  const updatedModules: Array<{\n    path: string;\n    sourceMapSize: number;\n    sourceSize: number;\n  }> = [];\n  const identicalModules: Array<{ path: string; size: number }> = [];\n  const droppedModules: Array<string> = [];\n  const addedModules: Array<{\n    path: string;\n    sourceMapSize: number;\n    sourceSize: number;\n  }> = [];\n  for (const [path, oldHash] of oldModuleMap.entries()) {\n    const newModule = newModuleMap.get(path);\n    if (newModule === undefined) {\n      droppedModules.push(path);\n    } else if (newModule.hash !== oldHash) {\n      updatedModules.push({\n        path,\n        sourceMapSize: newModule.sourceMapSize,\n        sourceSize: newModule.sourceSize,\n      });\n    } else {\n      identicalModules.push({\n        path,\n        size: newModule.sourceSize + newModule.sourceMapSize,\n      });\n    }\n  }\n  for (const [path, newModule] of newModuleMap.entries()) {\n    if (oldModuleMap.get(path) === undefined) {\n      addedModules.push({\n        path,\n        sourceMapSize: newModule.sourceMapSize,\n        sourceSize: newModule.sourceSize,\n      });\n    }\n  }\n  if (droppedModules.length > 0 || updatedModules.length > 0) {\n    diff += \"Delete the following modules:\\n\";\n    for (const module of droppedModules) {\n      diff += `[-] ${module}\\n`;\n    }\n    for (const module of updatedModules) {\n      diff += `[-] ${module.path}\\n`;\n    }\n  }\n\n  if (addedModules.length > 0 || updatedModules.length > 0) {\n    diff += \"Add the following modules:\\n\";\n    for (const module of addedModules) {\n      diff += \"[+] \" + renderModule(module) + \"\\n\";\n    }\n    for (const module of updatedModules) {\n      diff += \"[+] \" + renderModule(module) + \"\\n\";\n    }\n  }\n\n  return {\n    diffString: diff,\n    stats: {\n      updated: {\n        count: updatedModules.length,\n        size: updatedModules.reduce((acc, curr) => {\n          return acc + curr.sourceMapSize + curr.sourceSize;\n        }, 0),\n      },\n      identical: {\n        count: identicalModules.length,\n        size: identicalModules.reduce((acc, curr) => {\n          return acc + curr.size;\n        }, 0),\n      },\n      added: {\n        count: addedModules.length,\n        size: addedModules.reduce((acc, curr) => {\n          return acc + curr.sourceMapSize + curr.sourceSize;\n        }, 0),\n      },\n      numDropped: droppedModules.length,\n    },\n  };\n}\n\n/** Generate a human-readable diff between the two configs. */\nexport function diffConfig(\n  oldConfig: ConfigWithModuleHashes,\n  newConfig: Config,\n): { diffString: string; stats: ModuleDiffStats } {\n  const { diffString, stats } = compareModules(\n    oldConfig.moduleHashes,\n    newConfig.modules,\n  );\n  let diff = diffString;\n  const droppedAuth = [];\n  if (\n    oldConfig.projectConfig.authInfo !== undefined &&\n    newConfig.projectConfig.authInfo !== undefined\n  ) {\n    for (const oldAuth of oldConfig.projectConfig.authInfo) {\n      let matches = false;\n      for (const newAuth of newConfig.projectConfig.authInfo) {\n        if (equal(oldAuth, newAuth)) {\n          matches = true;\n          break;\n        }\n      }\n      if (!matches) {\n        droppedAuth.push(oldAuth);\n      }\n    }\n    if (droppedAuth.length > 0) {\n      diff += \"Remove the following auth providers:\\n\";\n      for (const authInfo of droppedAuth) {\n        diff += \"[-] \" + JSON.stringify(authInfo) + \"\\n\";\n      }\n    }\n\n    const addedAuth = [];\n    for (const newAuth of newConfig.projectConfig.authInfo) {\n      let matches = false;\n      for (const oldAuth of oldConfig.projectConfig.authInfo) {\n        if (equal(newAuth, oldAuth)) {\n          matches = true;\n          break;\n        }\n      }\n      if (!matches) {\n        addedAuth.push(newAuth);\n      }\n    }\n    if (addedAuth.length > 0) {\n      diff += \"Add the following auth providers:\\n\";\n      for (const auth of addedAuth) {\n        diff += \"[+] \" + JSON.stringify(auth) + \"\\n\";\n      }\n    }\n  } else if (\n    (oldConfig.projectConfig.authInfo !== undefined) !==\n    (newConfig.projectConfig.authInfo !== undefined)\n  ) {\n    diff += \"Moved auth config into auth.config.ts\\n\";\n  }\n\n  let versionMessage = \"\";\n  const matches = oldConfig.udfServerVersion === newConfig.udfServerVersion;\n  if (oldConfig.udfServerVersion && (!newConfig.udfServerVersion || !matches)) {\n    versionMessage += `[-] ${oldConfig.udfServerVersion}\\n`;\n  }\n  if (newConfig.udfServerVersion && (!oldConfig.udfServerVersion || !matches)) {\n    versionMessage += `[+] ${newConfig.udfServerVersion}\\n`;\n  }\n  if (versionMessage) {\n    diff += \"Change the server's function version:\\n\";\n    diff += versionMessage;\n  }\n\n  return { diffString: diff, stats };\n}\n\nexport async function handlePushConfigError(\n  ctx: Context,\n  error: unknown,\n  defaultMessage: string,\n  deploymentName: string | null,\n) {\n  const data: ErrorData | undefined =\n    error instanceof ThrowingFetchError ? error.serverErrorData : undefined;\n  if (data?.code === \"AuthConfigMissingEnvironmentVariable\") {\n    const errorMessage = data.message || \"(no error message given)\";\n    const [, variableName] =\n      errorMessage.match(/Environment variable (\\S+)/i) ?? [];\n    const envVarMessage =\n      `Environment variable ${chalk.bold(\n        variableName,\n      )} is used in auth config file but ` + `its value was not set.`;\n    let setEnvVarInstructions =\n      \"Go set it in the dashboard or using `npx convex env set`\";\n\n    // If `npx convex dev` is running using --url there might not be a configured deployment\n    if (deploymentName !== null) {\n      const variableQuery =\n        variableName !== undefined ? `?var=${variableName}` : \"\";\n      const dashboardUrl = deploymentDashboardUrlPage(\n        deploymentName,\n        `/settings/environment-variables${variableQuery}`,\n      );\n      setEnvVarInstructions = `Go to:\\n\\n    ${chalk.bold(\n        dashboardUrl,\n      )}\\n\\n  to set it up. `;\n    }\n    await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem or env vars\",\n      errForSentry: error,\n      printedMessage: envVarMessage + \"\\n\" + setEnvVarInstructions,\n    });\n  }\n\n  if (data?.code === \"InternalServerError\") {\n    if (deploymentName?.startsWith(\"local-\")) {\n      printLocalDeploymentOnError(ctx);\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        errForSentry: new LocalDeploymentError(\n          \"InternalServerError while pushing to local deployment\",\n        ),\n        printedMessage: defaultMessage,\n      });\n    }\n  }\n\n  logFailure(ctx, defaultMessage);\n  return await logAndHandleFetchError(ctx, error);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,wBAAkB;AAClB,gBAAoB;AACpB,kBAAiB;AACjB,qBAQO;AACP,qBAMO;AACP,qBAAwB;AACxB,uBAA2C;AAC3C,mBASO;AACP,oBAA2B;AAC3B,kBAA0B;AAC1B,kBAAiB;AACjB,qBAAkC;AAGlC,oBAGO;AACP,IAAAA,gBAAuD;AAEvD,MAAM,aAAS,uBAAU,YAAAC,QAAK,cAAc;AAiD5C,MAAM,yBAAyB;AAG/B,SAAS,WAAW,QAAiC;AACnD,SACE,mBAAmB,UACnB,OAAO,OAAO,kBAAkB,YAChC,YAAY,UACZ,OAAO,OAAO,WAAW;AAE7B;AAEA,SAAS,YAAY,QAAmC;AACtD,SAAO,MAAM,QAAQ,MAAM,KAAK,OAAO,MAAM,CAAC,SAAc,WAAW,IAAI,CAAC;AAC9E;AAGA,MAAM,mBAAmB,MAAM;AAAC;AAGhC,eAAsB,mBACpB,KACA,KACwB;AACxB,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,MAAI,OAAO,IAAI,SAAS,aAAa;AACnC,QAAI,OAAO;AAAA,MACT,kBAAkB,CAAC;AAAA,IACrB;AAAA,EACF,WAAW,OAAO,IAAI,KAAK,qBAAqB,aAAa;AAC3D,QAAI,KAAK,mBAAmB,CAAC;AAAA,EAC/B,WACE,CAAC,MAAM,QAAQ,IAAI,KAAK,gBAAgB,KACxC,CAAC,IAAI,KAAK,iBAAiB,MAAM,CAAC,SAAc,OAAO,SAAS,QAAQ,GACxE;AACA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AACA,MAAI,OAAO,IAAI,wBAAwB,aAAa;AAClD,QAAI,sBAAsB;AAAA,EAC5B,WAAW,OAAO,IAAI,wBAAwB,WAAW;AACvD,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,IAAI,cAAc,aAAa;AACxC,QAAI,YAAY;AAAA,EAClB,WAAW,OAAO,IAAI,cAAc,UAAU;AAC5C,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAGA,MAAI,IAAI,aAAa,QAAW;AAC9B,QAAI,CAAC,YAAY,IAAI,QAAQ,GAAG;AAC9B,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,OAAO,IAAI,YAAY,aAAa;AACtC,QAAI,UAAU,CAAC;AAAA,EACjB;AACA,MAAI,OAAO,IAAI,YAAY,UAAU;AACnC,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,MAAI,OAAO,IAAI,QAAQ,cAAc,aAAa;AAChD,QAAI,QAAQ,YAAY;AAAA,EAC1B;AACA,MAAI,OAAO,IAAI,QAAQ,oBAAoB,aAAa;AACtD,QAAI,QAAQ,kBAAkB;AAAA,EAChC;AACA,MACE,OAAO,IAAI,QAAQ,cAAc,aACjC,OAAO,IAAI,QAAQ,oBAAoB,WACvC;AACA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAIA,SAAS,mBAAmB,KAG1B;AACA,MAAI,OAAO,QAAQ,UAAU;AAG3B,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AACA,QAAM,EAAE,WAAW,SAAS,IAAI;AAChC,MAAI,OAAO,cAAc,UAAU;AAGjC,UAAM,IAAI,WAAW,mCAAmC;AAAA,EAC1D;AAGA,OAAK,YAAY,UAAU,QAAQ,CAAC,YAAY,QAAQ,GAAG;AAGzD,UAAM,IAAI,WAAW,yCAAyC;AAAA,EAChE;AAEA,SAAO;AAAA,IACL;AAAA,IACA,IAAK,YAAY,UAAU,OAAO,EAAE,SAAmB,IAAI,CAAC;AAAA,EAC9D;AACF;AAEO,SAAS,aAAqB;AACnC,SAAO;AACT;AAEA,eAAsB,eAAe,KAA+B;AAClE,QAAM,WAAW,WAAW;AAI5B,QAAM,oBAAoB;AAC1B,QAAM,gBAAgB,YAAAC,QAAK,KAAK,OAAO,QAAQ;AAG/C,QAAM,0BAA0B,IAAI,GAAG,OAAO,iBAAiB;AAC/D,QAAM,sBAAsB,IAAI,GAAG,OAAO,aAAa;AACvD,MAAI,2BAA2B,qBAAqB;AAClD,UAAM,UAAU,GAAG,aAAAC,QAAM,IAAI,eAAe,iBAAiB,QAAQ,aAAa,eAAe,CAAC;AAAA,+BAAkC,aAAa;AACjJ,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,MAAI,CAAC,2BAA2B,qBAAqB;AACnD,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,sBAAsB,aAAa;AAAA,IACrD,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,eAAsB,0BAA0B,KAA+B;AAC7E,QAAM,EAAE,eAAe,WAAW,IAAI,MAAM,kBAAkB,GAAG;AACjE,aAAO,2BAAa,YAAY,aAAa;AAC/C;AAGA,eAAsB,kBAAkB,KAGrC;AACD,MAAI,CAAC,IAAI,GAAG,OAAO,aAAa,GAAG;AAGjC,UAAM,WAAW,UAAM,8BAAgB,GAAG;AAC1C,UAAM,mBAAmB,mBAAmB;AAC5C,WAAO;AAAA,MACL,eAAe;AAAA,QACb,WAAW,mBACP,OAAO,sBAAsB,KAC7B;AAAA,QACJ,MAAM;AAAA,UACJ,kBAAkB,CAAC;AAAA,QACrB;AAAA,QACA,qBAAqB;AAAA,QACrB,SAAS;AAAA,UACP,WAAW;AAAA,UACX,iBAAiB;AAAA,QACnB;AAAA,MACF;AAAA,MACA,YAAY,WAAW;AAAA,IACzB;AAAA,EACF;AACA,MAAI;AACJ,QAAM,aAAa,MAAM,eAAe,GAAG;AAC3C,MAAI;AACF,oBAAgB,MAAM;AAAA,MACpB;AAAA,MACA,KAAK,MAAM,IAAI,GAAG,aAAa,UAAU,CAAC;AAAA,IAC5C;AAAA,EACF,SAAS,KAAK;AACZ,QAAI,eAAe,cAAc,eAAe,aAAa;AAC3D,mCAAS,KAAK,aAAAA,QAAM,IAAI,mBAAmB,UAAU,UAAU,CAAC;AAChE,qCAAW,KAAK,aAAAA,QAAM,KAAK,IAAI,SAAS,CAAC,CAAC;AAAA,IAC5C,OAAO;AACL;AAAA,QACE;AAAA,QACA,8CAA8C,UAAU;AAAA;AAAA,MAE1D;AACA,UAAI,eAAe,OAAO;AACxB,qCAAS,KAAK,aAAAA,QAAM,IAAI,IAAI,OAAO,CAAC;AAAA,MACtC;AAAA,IACF;AACA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA;AAAA,MAEd,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,eAAsB,6BACpB,KACA,QACA,OACiB;AACjB,QAAM,QAAQ,OAAO,KAAK;AAC1B,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,MAAM,IAAI,WAAW,YAAY,KAAK,iBAAiB;AAC7D,SAAO,MAAM,IAAI,MAAM;AAAA,IACrB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,gBAAgB;AAAA,EAAuC,aAAAA,QAAM,KAAK,IAAI,SAAS,CAAC,CAAC;AAAA,EACnF,CAAC;AACH;AAMA,eAAsB,wBACpB,KACA,eACA,YACA,SAIC;AACD,QAAM,cAAU,2BAAa,YAAY,aAAa;AAGtD,QAAM,cAAc,UAAM,yCAAyB,KAAK,OAAO;AAG/D,MAAI,SAAS;AACX,oCAAY,KAAK,0CAA0C;AAAA,EAC7D;AACA,QAAM,eAAe,UAAM;AAAA,IACzB;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACA,MAAI,SAAS;AACX;AAAA,MACE;AAAA,MACA;AAAA,MACA,aAAa,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAI;AAAA,IACxC;AAAA,EACF;AAGA,MAAI,WAAW,YAAY,KAAK,WAAW,GAAG;AAC5C,oCAAY,KAAK,yCAAyC;AAAA,EAC5D;AACA,QAAM,aAAa,UAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,YAAAD,QAAK,KAAK,SAAS,MAAM;AAAA,IACzB,cAAc,KAAK;AAAA,EACrB;AACA,MAAI,WAAW,YAAY,KAAK,WAAW,GAAG;AAC5C;AAAA,MACE;AAAA,MACA;AAAA,MACA,WAAW,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAI;AAAA,IACtC;AACA,QAAI,cAAc,KAAK,iBAAiB,SAAS,GAAG;AAClD;AAAA,QACE;AAAA,QACA;AAAA,QACA,CAAC,GAAG,WAAW,qBAAqB,QAAQ,CAAC,EAAE;AAAA,UAC7C,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,aAAa;AAC7B,UAAQ,KAAK,GAAG,WAAW,OAAO;AAClC,UAAQ,KAAK,GAAI,UAAM,iCAAiB,KAAK,OAAO,CAAE;AAEtD,QAAM,mBAAqC,CAAC;AAC5C,aAAW,CAAC,YAAY,aAAa,KAAK,WAAW,sBAAsB;AACzE,qBAAiB,KAAK,EAAE,MAAM,YAAY,SAAS,cAAc,CAAC;AAAA,EACpE;AAEA,QAAM,qBAA0C,MAAM;AAAA,IACpD,aAAa,mBAAmB,KAAK;AAAA,EACvC,EAAE,IAAI,CAAC,eAAe;AACpB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACD,qBAAmB;AAAA,IACjB,GAAG,MAAM,KAAK,WAAW,mBAAmB,KAAK,CAAC,EAAE;AAAA,MAClD,CAAC,eAAkC;AACjC,eAAO;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAIA,kBAAkB;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AACF;AAKA,eAAsB,WACpB,KACA,SAKC;AACD,QAAM,EAAE,eAAe,WAAW,IAAI,MAAM,kBAAkB,GAAG;AACjE,QAAM,EAAE,QAAQ,mBAAmB,IAAI,MAAM;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,EAAE,QAAQ,YAAY,mBAAmB;AAClD;AAEA,eAAsB,+BACpB,KACA,QACA,eACA;AACA,MAAI,OAAO,aAAa,QAAW;AACjC,UAAM,mBAAmB,YAAAA,QAAK,QAAQ,eAAe,gBAAgB;AACrE,UAAM,mBAAmB,YAAAA,QAAK,QAAQ,eAAe,gBAAgB;AACrE,UAAM,iBAAiB,IAAI,GAAG,OAAO,gBAAgB,IACjD,mBACA;AACJ,UAAM,yBAAyB,YAAAA,QAAK;AAAA,MAClC,OAAO;AAAA,MACP,IAAI,GAAG,OAAO,gBAAgB,IAAI,mBAAmB;AAAA,IACvD;AACA,QAAI,IAAI,GAAG,OAAO,cAAc,GAAG;AACjC,YAAM,IAAI,MAAM;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE,oCAAoC,sBAAsB;AAAA,MAE9D,CAAC;AAAA,IACH;AACA,QAAI,OAAO,SAAS,SAAS,GAAG;AAC9B,YAAM,uBAAuB,KAAK;AAAA,QAChC,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF,EAAE,MAAM,aAAG;AACX,YAAM,0BAA0B,CAAC,qBAAqB,CAAC,CAAC,EACrD,OAAO,qBAAqB,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC,EAC/D,KAAK,aAAG;AACX,UAAI,GAAG;AAAA,QACL;AAAA,QACA;AAAA,iBAES,uBAAuB;AAAA;AAAA,MAElC;AACA;AAAA,QACE;AAAA,QACA,aAAAC,QAAM;AAAA,UACJ,2CAA2C,sBAAsB;AAAA,QACnE;AAAA,MACF;AAAA,IACF;AACA,WAAO,OAAO;AAAA,EAChB;AACA,SAAO;AACT;AAGA,eAAsB,mBACpB,KACA,eACA,EAAE,mBAAmB,IAAqC;AAAA,EACxD,oBAAoB;AACtB,GACA;AACA,QAAM,aAAa,MAAM,eAAe,GAAG;AAC3C,QAAM,iBAAiB,sBAAsB,cAAc,aAAa,CAAC;AACzE,MAAI,OAAO,KAAK,cAAc,EAAE,SAAS,GAAG;AAC1C,QAAI;AACF,YAAM,WAAW,KAAK,UAAU,gBAAgB,QAAW,CAAC,IAAI;AAChE,UAAI,GAAG,cAAc,YAAY,UAAU,GAAK;AAAA,IAClD,SAAS,KAAK;AACZ,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBACE,+CAA+C,UAAU;AAAA;AAAA,MAE7D,CAAC;AAAA,IACH;AAAA,EACF,WAAW,sBAAsB,IAAI,GAAG,OAAO,UAAU,GAAG;AAC1D,QAAI,GAAG,OAAO,UAAU;AACxB;AAAA,MACE;AAAA,MACA,aAAAA,QAAM;AAAA,QACJ,WAAW,UAAU;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,MAAI,GAAG,UAAM,2BAAa,YAAY,aAAa,GAAG;AAAA,IACpD,eAAe;AAAA,EACjB,CAAC;AACH;AAEA,SAAS,cAAc,eAAmC;AACxD,QAAM,WAAgB,EAAE,GAAG,cAAc;AACzC,MAAI,SAAS,cAAc,wBAAwB;AACjD,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,MAAM,QAAQ,SAAS,QAAQ,KAAK,SAAS,SAAS,WAAW,GAAG;AACtE,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,SAAS,KAAK,iBAAiB,WAAW,GAAG;AAC/C,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,MAAI,SAAS,wBAAwB,OAAO;AAC1C,WAAO,SAAS;AAAA,EAClB;AAEA,MAAI,OAAO,KAAK,SAAS,IAAI,EAAE,WAAW,GAAG;AAC3C,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,SAAS,QAAQ,cAAc,OAAO;AACxC,WAAO,SAAS,QAAQ;AAAA,EAC1B;AACA,MAAI,SAAS,QAAQ,oBAAoB,OAAO;AAC9C,WAAO,SAAS,QAAQ;AAAA,EAC1B;AACA,MAAI,OAAO,KAAK,SAAS,OAAO,EAAE,WAAW,GAAG;AAC9C,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,eAAoB;AACjD,QAAM,YAAiB,EAAE,GAAG,cAAc;AAC1C,SAAO,UAAU;AACjB,SAAO,UAAU;AACjB,SAAO,UAAU;AACjB,SAAO;AACT;AAEO,SAAS,sBACd,KACA,YACA,SACA;AACA,MAAI,CAAC,QAAQ,qBAAqB;AAChC,WAAO;AAAA,EACT;AACA,wCAAkB,KAAK,UAAU;AACjC,sCAAgB,KAAK,oBAAoB,UAAU,EAAE;AACrD,SAAO;AACT;AAGA,eAAsB,WACpB,KACA,SACA,MACA,QACA,UACiC;AACjC,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC,eAAe;AAAA,IACf;AAAA,EACF,CAAC;AAED,oCAAc,KAAK,yCAAyC;AAC5D,MAAI;AACF,UAAM,MAAM,MAAM,MAAM,0BAA0B;AAAA,MAChD,QAAQ;AAAA,MACR,MAAM,KAAK,UAAU,EAAE,iCAAS,SAAS,CAAC;AAAA,IAC5C,CAAC;AACD,8CAAwB,KAAK,GAAG;AAChC,UAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,UAAM,gBAAgB,mBAAmB,KAAK,MAAM;AACpD,UAAM,gBAAgB;AAAA,MACpB,GAAG;AAAA;AAAA;AAAA,MAGH,MAAM;AAAA,QACJ,kBAAkB,CAAC;AAAA,MACrB;AAAA;AAAA,MAEA,qBAAqB;AAAA;AAAA,MAErB,SAAS;AAAA,QACP,WAAW;AAAA,QACX,iBAAiB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX;AACA,WAAO;AAAA,MACL;AAAA,MACA,cAAc,KAAK;AAAA;AAAA,MAEnB,kBAAkB,KAAK;AAAA,MACvB,kBAAkB,KAAK;AAAA,IACzB;AAAA,EACF,SAAS,KAAc;AACrB,mCAAW,KAAK,gDAAgD,MAAM,EAAE;AACxE,WAAO,UAAM,qCAAuB,KAAK,GAAG;AAAA,EAC9C;AACF;AA2CO,SAAS,WACd,QACA,UACA,UACA,aACA,oBACA;AAEA,QAAM,gBAAgB;AAAA,IACpB,aAAa,OAAO,cAAc;AAAA,IAClC,UAAU,OAAO,cAAc;AAAA,IAC/B,WAAW,OAAO,cAAc;AAAA,IAChC,UAAU,OAAO,cAAc;AAAA,EACjC;AACA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,SAAS,OAAO;AAAA,IAChB,kBAAkB,OAAO;AAAA,IACzB,kBAAkB,OAAO;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAYA,eAAsB,WACpB,KACA,QACA,SAQe;AACf,QAAM,mBAAmB;AAAA,IACvB;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AACA,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC,eAAe,QAAQ;AAAA,IACvB,UAAU,QAAQ;AAAA,EACpB,CAAC;AACD,MAAI;AACF,QAAI,OAAO,iBAAiB,SAAS,GAAG;AACtC;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,wCAAc,KAAK,wCAAwC;AAAA,IAC7D;AACA,UAAM,MAAM,oBAAoB;AAAA,MAC9B,MAAM,MAAM,OAAO,KAAK,UAAU,gBAAgB,GAAG;AAAA,QACnD,QAAQ;AAAA,UACN,CAAC,YAAAF,QAAK,UAAU,iBAAiB,GAAG,YAAAA,QAAK,UAAU;AAAA,UACnD,CAAC,YAAAA,QAAK,UAAU,oBAAoB,GAAG;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,MACD,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH,SAAS,OAAgB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,gDAAgD,QAAQ;AAAA,MACxD,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AAcA,SAAS,aAAaG,SAIX;AACT,SACEA,QAAO,OACP,SAAK,yBAAWA,QAAO,UAAU,CAAC,gBAAgBA,QAAO,aAAa;AAE1E;AAEA,SAAS,KAAKC,SAAgB;AAC5B,aAAO,0BAAW,QAAQ,EACvB,OAAOA,QAAO,MAAM,EACpB,OAAOA,QAAO,aAAa,EAAE,EAC7B,OAAO,KAAK;AACjB;AAUA,SAAS,eACP,YACA,YAIA;AACA,MAAI,OAAO;AACX,QAAM,eAAe,IAAI;AAAA,IACvB,WAAW,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,CAAC;AAAA,EACpD;AACA,QAAM,eAAe,IAAI;AAAA,IACvB,WAAW,IAAI,CAAC,UAAU;AAAA,MACxB,MAAM;AAAA,MACN;AAAA,QACE,MAAM,KAAK,KAAK;AAAA,QAChB,eAAe,MAAM,WAAW,UAAU;AAAA,QAC1C,YAAY,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,iBAID,CAAC;AACN,QAAM,mBAA0D,CAAC;AACjE,QAAM,iBAAgC,CAAC;AACvC,QAAM,eAID,CAAC;AACN,aAAW,CAACH,OAAM,OAAO,KAAK,aAAa,QAAQ,GAAG;AACpD,UAAM,YAAY,aAAa,IAAIA,KAAI;AACvC,QAAI,cAAc,QAAW;AAC3B,qBAAe,KAAKA,KAAI;AAAA,IAC1B,WAAW,UAAU,SAAS,SAAS;AACrC,qBAAe,KAAK;AAAA,QAClB,MAAAA;AAAA,QACA,eAAe,UAAU;AAAA,QACzB,YAAY,UAAU;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,uBAAiB,KAAK;AAAA,QACpB,MAAAA;AAAA,QACA,MAAM,UAAU,aAAa,UAAU;AAAA,MACzC,CAAC;AAAA,IACH;AAAA,EACF;AACA,aAAW,CAACA,OAAM,SAAS,KAAK,aAAa,QAAQ,GAAG;AACtD,QAAI,aAAa,IAAIA,KAAI,MAAM,QAAW;AACxC,mBAAa,KAAK;AAAA,QAChB,MAAAA;AAAA,QACA,eAAe,UAAU;AAAA,QACzB,YAAY,UAAU;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,eAAe,SAAS,KAAK,eAAe,SAAS,GAAG;AAC1D,YAAQ;AACR,eAAWE,WAAU,gBAAgB;AACnC,cAAQ,OAAOA,OAAM;AAAA;AAAA,IACvB;AACA,eAAWA,WAAU,gBAAgB;AACnC,cAAQ,OAAOA,QAAO,IAAI;AAAA;AAAA,IAC5B;AAAA,EACF;AAEA,MAAI,aAAa,SAAS,KAAK,eAAe,SAAS,GAAG;AACxD,YAAQ;AACR,eAAWA,WAAU,cAAc;AACjC,cAAQ,SAAS,aAAaA,OAAM,IAAI;AAAA,IAC1C;AACA,eAAWA,WAAU,gBAAgB;AACnC,cAAQ,SAAS,aAAaA,OAAM,IAAI;AAAA,IAC1C;AAAA,EACF;AAEA,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,OAAO;AAAA,MACL,SAAS;AAAA,QACP,OAAO,eAAe;AAAA,QACtB,MAAM,eAAe,OAAO,CAAC,KAAK,SAAS;AACzC,iBAAO,MAAM,KAAK,gBAAgB,KAAK;AAAA,QACzC,GAAG,CAAC;AAAA,MACN;AAAA,MACA,WAAW;AAAA,QACT,OAAO,iBAAiB;AAAA,QACxB,MAAM,iBAAiB,OAAO,CAAC,KAAK,SAAS;AAC3C,iBAAO,MAAM,KAAK;AAAA,QACpB,GAAG,CAAC;AAAA,MACN;AAAA,MACA,OAAO;AAAA,QACL,OAAO,aAAa;AAAA,QACpB,MAAM,aAAa,OAAO,CAAC,KAAK,SAAS;AACvC,iBAAO,MAAM,KAAK,gBAAgB,KAAK;AAAA,QACzC,GAAG,CAAC;AAAA,MACN;AAAA,MACA,YAAY,eAAe;AAAA,IAC7B;AAAA,EACF;AACF;AAGO,SAAS,WACd,WACA,WACgD;AAChD,QAAM,EAAE,YAAY,MAAM,IAAI;AAAA,IAC5B,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AACA,MAAI,OAAO;AACX,QAAM,cAAc,CAAC;AACrB,MACE,UAAU,cAAc,aAAa,UACrC,UAAU,cAAc,aAAa,QACrC;AACA,eAAW,WAAW,UAAU,cAAc,UAAU;AACtD,UAAIE,WAAU;AACd,iBAAW,WAAW,UAAU,cAAc,UAAU;AACtD,gBAAI,kBAAAC,SAAM,SAAS,OAAO,GAAG;AAC3B,UAAAD,WAAU;AACV;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAACA,UAAS;AACZ,oBAAY,KAAK,OAAO;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,YAAY,SAAS,GAAG;AAC1B,cAAQ;AACR,iBAAW,YAAY,aAAa;AAClC,gBAAQ,SAAS,KAAK,UAAU,QAAQ,IAAI;AAAA,MAC9C;AAAA,IACF;AAEA,UAAM,YAAY,CAAC;AACnB,eAAW,WAAW,UAAU,cAAc,UAAU;AACtD,UAAIA,WAAU;AACd,iBAAW,WAAW,UAAU,cAAc,UAAU;AACtD,gBAAI,kBAAAC,SAAM,SAAS,OAAO,GAAG;AAC3B,UAAAD,WAAU;AACV;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAACA,UAAS;AACZ,kBAAU,KAAK,OAAO;AAAA,MACxB;AAAA,IACF;AACA,QAAI,UAAU,SAAS,GAAG;AACxB,cAAQ;AACR,iBAAW,QAAQ,WAAW;AAC5B,gBAAQ,SAAS,KAAK,UAAU,IAAI,IAAI;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,WACG,UAAU,cAAc,aAAa,YACrC,UAAU,cAAc,aAAa,SACtC;AACA,YAAQ;AAAA,EACV;AAEA,MAAI,iBAAiB;AACrB,QAAM,UAAU,UAAU,qBAAqB,UAAU;AACzD,MAAI,UAAU,qBAAqB,CAAC,UAAU,oBAAoB,CAAC,UAAU;AAC3E,sBAAkB,OAAO,UAAU,gBAAgB;AAAA;AAAA,EACrD;AACA,MAAI,UAAU,qBAAqB,CAAC,UAAU,oBAAoB,CAAC,UAAU;AAC3E,sBAAkB,OAAO,UAAU,gBAAgB;AAAA;AAAA,EACrD;AACA,MAAI,gBAAgB;AAClB,YAAQ;AACR,YAAQ;AAAA,EACV;AAEA,SAAO,EAAE,YAAY,MAAM,MAAM;AACnC;AAEA,eAAsB,sBACpB,KACA,OACA,gBACA,gBACA;AACA,QAAM,OACJ,iBAAiB,kCAAqB,MAAM,kBAAkB;AAChE,MAAI,MAAM,SAAS,wCAAwC;AACzD,UAAM,eAAe,KAAK,WAAW;AACrC,UAAM,CAAC,EAAE,YAAY,IACnB,aAAa,MAAM,6BAA6B,KAAK,CAAC;AACxD,UAAM,gBACJ,wBAAwB,aAAAH,QAAM;AAAA,MAC5B;AAAA,IACF,CAAC;AACH,QAAI,wBACF;AAGF,QAAI,mBAAmB,MAAM;AAC3B,YAAM,gBACJ,iBAAiB,SAAY,QAAQ,YAAY,KAAK;AACxD,YAAM,mBAAe;AAAA,QACnB;AAAA,QACA,kCAAkC,aAAa;AAAA,MACjD;AACA,8BAAwB;AAAA;AAAA,MAAiB,aAAAA,QAAM;AAAA,QAC7C;AAAA,MACF,CAAC;AAAA;AAAA;AAAA,IACH;AACA,UAAM,IAAI,MAAM;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB,gBAAgB,OAAO;AAAA,IACzC,CAAC;AAAA,EACH;AAEA,MAAI,MAAM,SAAS,uBAAuB;AACxC,QAAI,gBAAgB,WAAW,QAAQ,GAAG;AACxC,qDAA4B,GAAG;AAC/B,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc,IAAI;AAAA,UAChB;AAAA,QACF;AAAA,QACA,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,iCAAW,KAAK,cAAc;AAC9B,SAAO,UAAM,qCAAuB,KAAK,KAAK;AAChD;", "names": ["import_utils", "zlib", "path", "chalk", "module", "bundle", "matches", "equal"]}
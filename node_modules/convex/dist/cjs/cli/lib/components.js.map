{"version": 3, "sources": ["../../../../src/cli/lib/components.ts"], "sourcesContent": ["import path from \"path\";\nimport {\n  Context,\n  changeSpinner,\n  logFinishedStep,\n  logMessage,\n} from \"../../bundler/context.js\";\nimport {\n  ProjectConfig,\n  configFromProjectConfig,\n  getFunctionsDirectoryPath,\n  readProjectConfig,\n} from \"./config.js\";\nimport {\n  finishPush,\n  reportPushCompleted,\n  startPush,\n  waitForSchema,\n} from \"./deploy2.js\";\nimport { version } from \"../version.js\";\nimport { PushOptions, runNonComponentsPush } from \"./push.js\";\nimport { ensureHasConvexDependency, functionsDir } from \"./utils/utils.js\";\nimport {\n  bundleDefinitions,\n  bundleImplementations,\n  componentGraph,\n} from \"./components/definition/bundle.js\";\nimport { isComponentDirectory } from \"./components/definition/directoryStructure.js\";\nimport {\n  doFinalComponentCodegen,\n  doInitialComponentCodegen,\n  CodegenOptions,\n  doInitCodegen,\n  doCodegen,\n} from \"./codegen.js\";\nimport {\n  AppDefinitionConfig,\n  ComponentDefinitionConfig,\n} from \"./deployApi/definitionConfig.js\";\nimport { typeCheckFunctionsInMode, TypeCheckMode } from \"./typecheck.js\";\nimport { withTmpDir } from \"../../bundler/fs.js\";\nimport { handleDebugBundlePath } from \"./debugBundlePath.js\";\nimport chalk from \"chalk\";\nimport { StartPushRequest, StartPushResponse } from \"./deployApi/startPush.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./api.js\";\nimport {\n  FinishPushDiff,\n  DeveloperIndexConfig,\n} from \"./deployApi/finishPush.js\";\nimport { Reporter, Span } from \"./tracing.js\";\nimport {\n  DEFINITION_FILENAME_JS,\n  DEFINITION_FILENAME_TS,\n} from \"./components/constants.js\";\nimport { DeploymentSelection } from \"./deploymentSelection.js\";\nasync function findComponentRootPath(ctx: Context, functionsDir: string) {\n  // Default to `.ts` but fallback to `.js` if not present.\n  let componentRootPath = path.resolve(\n    path.join(functionsDir, DEFINITION_FILENAME_TS),\n  );\n  if (!ctx.fs.exists(componentRootPath)) {\n    componentRootPath = path.resolve(\n      path.join(functionsDir, DEFINITION_FILENAME_JS),\n    );\n  }\n  return componentRootPath;\n}\n\nexport async function runCodegen(\n  ctx: Context,\n  deploymentSelection: DeploymentSelection,\n  options: CodegenOptions,\n) {\n  // This also ensures the current directory is the project root.\n  await ensureHasConvexDependency(ctx, \"codegen\");\n\n  const { configPath, projectConfig } = await readProjectConfig(ctx);\n  const functionsDirectoryPath = functionsDir(configPath, projectConfig);\n\n  const componentRootPath = await findComponentRootPath(\n    ctx,\n    functionsDirectoryPath,\n  );\n\n  if (ctx.fs.exists(componentRootPath)) {\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n    );\n\n    await startComponentsPushAndCodegen(\n      ctx,\n      Span.noop(),\n      projectConfig,\n      configPath,\n      {\n        ...options,\n        deploymentName: credentials.deploymentFields?.deploymentName ?? null,\n        url: credentials.url,\n        adminKey: credentials.adminKey,\n        generateCommonJSApi: options.commonjs,\n        verbose: options.dryRun,\n        codegen: true,\n        liveComponentSources: options.liveComponentSources,\n        typecheckComponents: false,\n      },\n    );\n  } else {\n    if (options.init) {\n      await doInitCodegen(ctx, functionsDirectoryPath, false, {\n        dryRun: options.dryRun,\n        debug: options.debug,\n      });\n    }\n\n    if (options.typecheck !== \"disable\") {\n      logMessage(ctx, chalk.gray(\"Running TypeScript typecheck…\"));\n    }\n\n    await doCodegen(ctx, functionsDirectoryPath, options.typecheck, {\n      dryRun: options.dryRun,\n      debug: options.debug,\n      generateCommonJSApi: options.commonjs,\n    });\n  }\n}\n\nexport async function runPush(ctx: Context, options: PushOptions) {\n  const { configPath, projectConfig } = await readProjectConfig(ctx);\n  const convexDir = functionsDir(configPath, projectConfig);\n  const componentRootPath = await findComponentRootPath(ctx, convexDir);\n  if (ctx.fs.exists(componentRootPath)) {\n    await runComponentsPush(ctx, options, configPath, projectConfig);\n  } else {\n    await runNonComponentsPush(ctx, options, configPath, projectConfig);\n  }\n}\n\nasync function startComponentsPushAndCodegen(\n  ctx: Context,\n  parentSpan: Span,\n  projectConfig: ProjectConfig,\n  configPath: string,\n  options: {\n    typecheck: TypeCheckMode;\n    typecheckComponents: boolean;\n    adminKey: string;\n    url: string;\n    deploymentName: string | null;\n    verbose: boolean;\n    debugBundlePath?: string;\n    dryRun: boolean;\n    generateCommonJSApi?: boolean;\n    debug: boolean;\n    writePushRequest?: string;\n    codegen: boolean;\n    liveComponentSources?: boolean;\n  },\n): Promise<StartPushResponse | null> {\n  const convexDir = await getFunctionsDirectoryPath(ctx);\n\n  // '.' means use the process current working directory, it's the default behavior.\n  // Spelling it out here to be explicit for a future where this code can run\n  // from other directories.\n  // In esbuild the working directory is used to print error messages and resolving\n  // relatives paths passed to it. It generally doesn't matter for resolving imports,\n  // imports are resolved from the file where they are written.\n  const absWorkingDir = path.resolve(\".\");\n  const isComponent = isComponentDirectory(ctx, convexDir, true);\n  if (isComponent.kind === \"err\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `Invalid component root directory (${isComponent.why}): ${convexDir}`,\n    });\n  }\n  const rootComponent = isComponent.component;\n\n  changeSpinner(ctx, \"Finding component definitions...\");\n  // Create a list of relevant component directories. These are just for knowing\n  // while directories to bundle in bundleDefinitions and bundleImplementations.\n  // This produces a bundle in memory as a side effect but it's thrown away.\n  const { components, dependencyGraph } = await parentSpan.enterAsync(\n    \"componentGraph\",\n    () =>\n      componentGraph(\n        ctx,\n        absWorkingDir,\n        rootComponent,\n        !!options.liveComponentSources,\n        options.verbose,\n      ),\n  );\n\n  if (options.codegen) {\n    changeSpinner(ctx, \"Generating server code...\");\n    await parentSpan.enterAsync(\"doInitialComponentCodegen\", () =>\n      withTmpDir(async (tmpDir) => {\n        await doInitialComponentCodegen(ctx, tmpDir, rootComponent, options);\n        for (const directory of components.values()) {\n          await doInitialComponentCodegen(ctx, tmpDir, directory, options);\n        }\n      }),\n    );\n  }\n\n  changeSpinner(ctx, \"Bundling component definitions...\");\n  // This bundles everything but the actual function definitions\n  const {\n    appDefinitionSpecWithoutImpls,\n    componentDefinitionSpecsWithoutImpls,\n  } = await parentSpan.enterAsync(\"bundleDefinitions\", () =>\n    bundleDefinitions(\n      ctx,\n      absWorkingDir,\n      dependencyGraph,\n      rootComponent,\n      // Note that this *includes* the root component.\n      [...components.values()],\n      !!options.liveComponentSources,\n    ),\n  );\n\n  changeSpinner(ctx, \"Bundling component schemas and implementations...\");\n  const { appImplementation, componentImplementations } =\n    await parentSpan.enterAsync(\"bundleImplementations\", () =>\n      bundleImplementations(\n        ctx,\n        rootComponent,\n        [...components.values()],\n        projectConfig.node.externalPackages,\n        options.liveComponentSources ? [\"@convex-dev/component-source\"] : [],\n        options.verbose,\n      ),\n    );\n  if (options.debugBundlePath) {\n    const { config: localConfig } = await configFromProjectConfig(\n      ctx,\n      projectConfig,\n      configPath,\n      options.verbose,\n    );\n    // TODO(ENG-6972): Actually write the bundles for components.\n    await handleDebugBundlePath(ctx, options.debugBundlePath, localConfig);\n    logMessage(\n      ctx,\n      `Wrote bundle and metadata for modules in the root to ${options.debugBundlePath}. Skipping rest of push.`,\n    );\n    return null;\n  }\n\n  // We're just using the version this CLI is running with for now.\n  // This could be different than the version of `convex` the app runs with\n  // if the CLI is installed globally.\n  // TODO: This should be the version of the `convex` package used by each\n  // component, and may be different for each component.\n  const udfServerVersion = version;\n\n  const appDefinition: AppDefinitionConfig = {\n    ...appDefinitionSpecWithoutImpls,\n    ...appImplementation,\n    udfServerVersion,\n  };\n\n  const componentDefinitions: ComponentDefinitionConfig[] = [];\n  for (const componentDefinition of componentDefinitionSpecsWithoutImpls) {\n    const impl = componentImplementations.filter(\n      (impl) => impl.definitionPath === componentDefinition.definitionPath,\n    )[0];\n    if (!impl) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `missing! couldn't find ${componentDefinition.definitionPath} in ${componentImplementations.map((impl) => impl.definitionPath).toString()}`,\n      });\n    }\n    componentDefinitions.push({\n      ...componentDefinition,\n      ...impl,\n      udfServerVersion,\n    });\n  }\n  const startPushRequest = {\n    adminKey: options.adminKey,\n    dryRun: options.dryRun,\n    functions: projectConfig.functions,\n    appDefinition,\n    componentDefinitions,\n    nodeDependencies: appImplementation.externalNodeDependencies,\n  };\n  if (options.writePushRequest) {\n    const pushRequestPath = path.resolve(options.writePushRequest);\n    ctx.fs.writeUtf8File(\n      `${pushRequestPath}.json`,\n      JSON.stringify(startPushRequest),\n    );\n    return null;\n  }\n  logStartPushSizes(parentSpan, startPushRequest);\n\n  changeSpinner(ctx, \"Uploading functions to Convex...\");\n  const startPushResponse = await parentSpan.enterAsync(\"startPush\", (span) =>\n    startPush(ctx, span, startPushRequest, options),\n  );\n\n  if (options.verbose) {\n    logMessage(ctx, \"startPush: \" + JSON.stringify(startPushResponse, null, 2));\n  }\n\n  if (options.codegen) {\n    changeSpinner(ctx, \"Generating TypeScript bindings...\");\n    await parentSpan.enterAsync(\"doFinalComponentCodegen\", () =>\n      withTmpDir(async (tmpDir) => {\n        await doFinalComponentCodegen(\n          ctx,\n          tmpDir,\n          rootComponent,\n          rootComponent,\n          startPushResponse,\n          options,\n        );\n        for (const directory of components.values()) {\n          await doFinalComponentCodegen(\n            ctx,\n            tmpDir,\n            rootComponent,\n            directory,\n            startPushResponse,\n            options,\n          );\n        }\n      }),\n    );\n  }\n\n  changeSpinner(ctx, \"Running TypeScript...\");\n  await parentSpan.enterAsync(\"typeCheckFunctionsInMode\", async () => {\n    await typeCheckFunctionsInMode(ctx, options.typecheck, rootComponent.path);\n    if (options.typecheckComponents) {\n      for (const directory of components.values()) {\n        await typeCheckFunctionsInMode(ctx, options.typecheck, directory.path);\n      }\n    }\n  });\n\n  return startPushResponse;\n}\n\nfunction logStartPushSizes(span: Span, startPushRequest: StartPushRequest) {\n  let v8Size = 0;\n  let v8Count = 0;\n  let nodeSize = 0;\n  let nodeCount = 0;\n\n  for (const componentDefinition of startPushRequest.componentDefinitions) {\n    for (const module of componentDefinition.functions) {\n      if (module.environment === \"isolate\") {\n        v8Size += module.source.length + (module.sourceMap ?? \"\").length;\n        v8Count += 1;\n      } else if (module.environment === \"node\") {\n        nodeSize += module.source.length + (module.sourceMap ?? \"\").length;\n        nodeCount += 1;\n      }\n    }\n  }\n  span.setProperty(\"v8_size\", v8Size.toString());\n  span.setProperty(\"v8_count\", v8Count.toString());\n  span.setProperty(\"node_size\", nodeSize.toString());\n  span.setProperty(\"node_count\", nodeCount.toString());\n}\n\nexport async function runComponentsPush(\n  ctx: Context,\n  options: PushOptions,\n  configPath: string,\n  projectConfig: ProjectConfig,\n) {\n  const reporter = new Reporter();\n  const pushSpan = Span.root(reporter, \"runComponentsPush\");\n  pushSpan.setProperty(\"cli_version\", version);\n\n  await ensureHasConvexDependency(ctx, \"push\");\n\n  const startPushResponse = await pushSpan.enterAsync(\n    \"startComponentsPushAndCodegen\",\n    (span) =>\n      startComponentsPushAndCodegen(\n        ctx,\n        span,\n        projectConfig,\n        configPath,\n        options,\n      ),\n  );\n  if (!startPushResponse) {\n    return;\n  }\n\n  await pushSpan.enterAsync(\"waitForSchema\", (span) =>\n    waitForSchema(ctx, span, startPushResponse, options),\n  );\n\n  const finishPushResponse = await pushSpan.enterAsync(\"finishPush\", (span) =>\n    finishPush(ctx, span, startPushResponse, options),\n  );\n  printDiff(ctx, finishPushResponse, options);\n  pushSpan.end();\n\n  // Asynchronously report that the push completed.\n  if (!options.dryRun) {\n    void reportPushCompleted(ctx, options.adminKey, options.url, reporter);\n  }\n}\n\nfunction printDiff(\n  ctx: Context,\n  finishPushResponse: FinishPushDiff,\n  opts: { verbose: boolean; dryRun: boolean },\n) {\n  if (opts.verbose) {\n    const diffString = JSON.stringify(finishPushResponse, null, 2);\n    logMessage(ctx, diffString);\n    return;\n  }\n  const { componentDiffs } = finishPushResponse;\n\n  // Print out index diffs for the root component.\n  let rootDiff = componentDiffs[\"\"];\n  if (rootDiff && rootDiff.indexDiff) {\n    if (rootDiff.indexDiff.removed_indexes.length > 0) {\n      let msg = `${opts.dryRun ? \"Would delete\" : \"Deleted\"} table indexes:\\n`;\n      for (let i = 0; i < rootDiff.indexDiff.removed_indexes.length; i++) {\n        const index = rootDiff.indexDiff.removed_indexes[i];\n        if (i > 0) {\n          msg += \"\\n\";\n        }\n        msg += `  [-] ${formatIndex(index)}`;\n      }\n      logFinishedStep(ctx, msg);\n    }\n    if (rootDiff.indexDiff.added_indexes.length > 0) {\n      let msg = `${opts.dryRun ? \"Would add\" : \"Added\"} table indexes:\\n`;\n      for (let i = 0; i < rootDiff.indexDiff.added_indexes.length; i++) {\n        const index = rootDiff.indexDiff.added_indexes[i];\n        if (i > 0) {\n          msg += \"\\n\";\n        }\n        msg += `  [+] ${formatIndex(index)}`;\n      }\n      logFinishedStep(ctx, msg);\n    }\n  }\n\n  // Only show component level diffs for other components.\n  for (const [componentPath, componentDiff] of Object.entries(componentDiffs)) {\n    if (componentPath === \"\") {\n      continue;\n    }\n    if (componentDiff.diffType.type === \"create\") {\n      logFinishedStep(ctx, `Installed component ${componentPath}.`);\n    }\n    if (componentDiff.diffType.type === \"unmount\") {\n      logFinishedStep(ctx, `Unmounted component ${componentPath}.`);\n    }\n    if (componentDiff.diffType.type === \"remount\") {\n      logFinishedStep(ctx, `Remounted component ${componentPath}.`);\n    }\n  }\n}\n\nfunction formatIndex(index: DeveloperIndexConfig) {\n  return `${index.name}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AACjB,qBAKO;AACP,oBAKO;AACP,qBAKO;AACP,qBAAwB;AACxB,kBAAkD;AAClD,mBAAwD;AACxD,oBAIO;AACP,gCAAqC;AACrC,qBAMO;AAKP,uBAAwD;AACxD,gBAA2B;AAC3B,6BAAsC;AACtC,mBAAkB;AAElB,iBAGO;AAKP,qBAA+B;AAC/B,uBAGO;AAEP,eAAe,sBAAsB,KAAcA,eAAsB;AAEvE,MAAI,oBAAoB,YAAAC,QAAK;AAAA,IAC3B,YAAAA,QAAK,KAAKD,eAAc,uCAAsB;AAAA,EAChD;AACA,MAAI,CAAC,IAAI,GAAG,OAAO,iBAAiB,GAAG;AACrC,wBAAoB,YAAAC,QAAK;AAAA,MACvB,YAAAA,QAAK,KAAKD,eAAc,uCAAsB;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;AAEA,eAAsB,WACpB,KACA,qBACA,SACA;AAEA,YAAM,wCAA0B,KAAK,SAAS;AAE9C,QAAM,EAAE,YAAY,cAAc,IAAI,UAAM,iCAAkB,GAAG;AACjE,QAAM,6BAAyB,2BAAa,YAAY,aAAa;AAErE,QAAM,oBAAoB,MAAM;AAAA,IAC9B;AAAA,IACA;AAAA,EACF;AAEA,MAAI,IAAI,GAAG,OAAO,iBAAiB,GAAG;AACpC,UAAM,yBACJ,UAAM,wDAA4C,KAAK,OAAO;AAChE,UAAM,cAAc,UAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM;AAAA,MACJ;AAAA,MACA,oBAAK,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,gBAAgB,YAAY,kBAAkB,kBAAkB;AAAA,QAChE,KAAK,YAAY;AAAA,QACjB,UAAU,YAAY;AAAA,QACtB,qBAAqB,QAAQ;AAAA,QAC7B,SAAS,QAAQ;AAAA,QACjB,SAAS;AAAA,QACT,sBAAsB,QAAQ;AAAA,QAC9B,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,QAAQ,MAAM;AAChB,gBAAM,8BAAc,KAAK,wBAAwB,OAAO;AAAA,QACtD,QAAQ,QAAQ;AAAA,QAChB,OAAO,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,cAAc,WAAW;AACnC,qCAAW,KAAK,aAAAE,QAAM,KAAK,oCAA+B,CAAC;AAAA,IAC7D;AAEA,cAAM,0BAAU,KAAK,wBAAwB,QAAQ,WAAW;AAAA,MAC9D,QAAQ,QAAQ;AAAA,MAChB,OAAO,QAAQ;AAAA,MACf,qBAAqB,QAAQ;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAEA,eAAsB,QAAQ,KAAc,SAAsB;AAChE,QAAM,EAAE,YAAY,cAAc,IAAI,UAAM,iCAAkB,GAAG;AACjE,QAAM,gBAAY,2BAAa,YAAY,aAAa;AACxD,QAAM,oBAAoB,MAAM,sBAAsB,KAAK,SAAS;AACpE,MAAI,IAAI,GAAG,OAAO,iBAAiB,GAAG;AACpC,UAAM,kBAAkB,KAAK,SAAS,YAAY,aAAa;AAAA,EACjE,OAAO;AACL,cAAM,kCAAqB,KAAK,SAAS,YAAY,aAAa;AAAA,EACpE;AACF;AAEA,eAAe,8BACb,KACA,YACA,eACA,YACA,SAemC;AACnC,QAAM,YAAY,UAAM,yCAA0B,GAAG;AAQrD,QAAM,gBAAgB,YAAAD,QAAK,QAAQ,GAAG;AACtC,QAAM,kBAAc,gDAAqB,KAAK,WAAW,IAAI;AAC7D,MAAI,YAAY,SAAS,OAAO;AAC9B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,qCAAqC,YAAY,GAAG,MAAM,SAAS;AAAA,IACrF,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,YAAY;AAElC,oCAAc,KAAK,kCAAkC;AAIrD,QAAM,EAAE,YAAY,gBAAgB,IAAI,MAAM,WAAW;AAAA,IACvD;AAAA,IACA,UACE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,CAAC,QAAQ;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,EACJ;AAEA,MAAI,QAAQ,SAAS;AACnB,sCAAc,KAAK,2BAA2B;AAC9C,UAAM,WAAW;AAAA,MAAW;AAAA,MAA6B,UACvD,sBAAW,OAAO,WAAW;AAC3B,kBAAM,0CAA0B,KAAK,QAAQ,eAAe,OAAO;AACnE,mBAAW,aAAa,WAAW,OAAO,GAAG;AAC3C,oBAAM,0CAA0B,KAAK,QAAQ,WAAW,OAAO;AAAA,QACjE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,oCAAc,KAAK,mCAAmC;AAEtD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,MAAM,WAAW;AAAA,IAAW;AAAA,IAAqB,UACnD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA,CAAC,GAAG,WAAW,OAAO,CAAC;AAAA,MACvB,CAAC,CAAC,QAAQ;AAAA,IACZ;AAAA,EACF;AAEA,oCAAc,KAAK,mDAAmD;AACtE,QAAM,EAAE,mBAAmB,yBAAyB,IAClD,MAAM,WAAW;AAAA,IAAW;AAAA,IAAyB,UACnD;AAAA,MACE;AAAA,MACA;AAAA,MACA,CAAC,GAAG,WAAW,OAAO,CAAC;AAAA,MACvB,cAAc,KAAK;AAAA,MACnB,QAAQ,uBAAuB,CAAC,8BAA8B,IAAI,CAAC;AAAA,MACnE,QAAQ;AAAA,IACV;AAAA,EACF;AACF,MAAI,QAAQ,iBAAiB;AAC3B,UAAM,EAAE,QAAQ,YAAY,IAAI,UAAM;AAAA,MACpC;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,IACV;AAEA,cAAM,8CAAsB,KAAK,QAAQ,iBAAiB,WAAW;AACrE;AAAA,MACE;AAAA,MACA,wDAAwD,QAAQ,eAAe;AAAA,IACjF;AACA,WAAO;AAAA,EACT;AAOA,QAAM,mBAAmB;AAEzB,QAAM,gBAAqC;AAAA,IACzC,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,EACF;AAEA,QAAM,uBAAoD,CAAC;AAC3D,aAAW,uBAAuB,sCAAsC;AACtE,UAAM,OAAO,yBAAyB;AAAA,MACpC,CAACE,UAASA,MAAK,mBAAmB,oBAAoB;AAAA,IACxD,EAAE,CAAC;AACH,QAAI,CAAC,MAAM;AACT,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,0BAA0B,oBAAoB,cAAc,OAAO,yBAAyB,IAAI,CAACA,UAASA,MAAK,cAAc,EAAE,SAAS,CAAC;AAAA,MAC3J,CAAC;AAAA,IACH;AACA,yBAAqB,KAAK;AAAA,MACxB,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB;AAAA,IACvB,UAAU,QAAQ;AAAA,IAClB,QAAQ,QAAQ;AAAA,IAChB,WAAW,cAAc;AAAA,IACzB;AAAA,IACA;AAAA,IACA,kBAAkB,kBAAkB;AAAA,EACtC;AACA,MAAI,QAAQ,kBAAkB;AAC5B,UAAM,kBAAkB,YAAAF,QAAK,QAAQ,QAAQ,gBAAgB;AAC7D,QAAI,GAAG;AAAA,MACL,GAAG,eAAe;AAAA,MAClB,KAAK,UAAU,gBAAgB;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACA,oBAAkB,YAAY,gBAAgB;AAE9C,oCAAc,KAAK,kCAAkC;AACrD,QAAM,oBAAoB,MAAM,WAAW;AAAA,IAAW;AAAA,IAAa,CAAC,aAClE,0BAAU,KAAK,MAAM,kBAAkB,OAAO;AAAA,EAChD;AAEA,MAAI,QAAQ,SAAS;AACnB,mCAAW,KAAK,gBAAgB,KAAK,UAAU,mBAAmB,MAAM,CAAC,CAAC;AAAA,EAC5E;AAEA,MAAI,QAAQ,SAAS;AACnB,sCAAc,KAAK,mCAAmC;AACtD,UAAM,WAAW;AAAA,MAAW;AAAA,MAA2B,UACrD,sBAAW,OAAO,WAAW;AAC3B,kBAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,mBAAW,aAAa,WAAW,OAAO,GAAG;AAC3C,oBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,oCAAc,KAAK,uBAAuB;AAC1C,QAAM,WAAW,WAAW,4BAA4B,YAAY;AAClE,cAAM,2CAAyB,KAAK,QAAQ,WAAW,cAAc,IAAI;AACzE,QAAI,QAAQ,qBAAqB;AAC/B,iBAAW,aAAa,WAAW,OAAO,GAAG;AAC3C,kBAAM,2CAAyB,KAAK,QAAQ,WAAW,UAAU,IAAI;AAAA,MACvE;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,SAAS,kBAAkB,MAAY,kBAAoC;AACzE,MAAI,SAAS;AACb,MAAI,UAAU;AACd,MAAI,WAAW;AACf,MAAI,YAAY;AAEhB,aAAW,uBAAuB,iBAAiB,sBAAsB;AACvE,eAAWG,WAAU,oBAAoB,WAAW;AAClD,UAAIA,QAAO,gBAAgB,WAAW;AACpC,kBAAUA,QAAO,OAAO,UAAUA,QAAO,aAAa,IAAI;AAC1D,mBAAW;AAAA,MACb,WAAWA,QAAO,gBAAgB,QAAQ;AACxC,oBAAYA,QAAO,OAAO,UAAUA,QAAO,aAAa,IAAI;AAC5D,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,OAAK,YAAY,WAAW,OAAO,SAAS,CAAC;AAC7C,OAAK,YAAY,YAAY,QAAQ,SAAS,CAAC;AAC/C,OAAK,YAAY,aAAa,SAAS,SAAS,CAAC;AACjD,OAAK,YAAY,cAAc,UAAU,SAAS,CAAC;AACrD;AAEA,eAAsB,kBACpB,KACA,SACA,YACA,eACA;AACA,QAAM,WAAW,IAAI,wBAAS;AAC9B,QAAM,WAAW,oBAAK,KAAK,UAAU,mBAAmB;AACxD,WAAS,YAAY,eAAe,sBAAO;AAE3C,YAAM,wCAA0B,KAAK,MAAM;AAE3C,QAAM,oBAAoB,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,CAAC,SACC;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACJ;AACA,MAAI,CAAC,mBAAmB;AACtB;AAAA,EACF;AAEA,QAAM,SAAS;AAAA,IAAW;AAAA,IAAiB,CAAC,aAC1C,8BAAc,KAAK,MAAM,mBAAmB,OAAO;AAAA,EACrD;AAEA,QAAM,qBAAqB,MAAM,SAAS;AAAA,IAAW;AAAA,IAAc,CAAC,aAClE,2BAAW,KAAK,MAAM,mBAAmB,OAAO;AAAA,EAClD;AACA,YAAU,KAAK,oBAAoB,OAAO;AAC1C,WAAS,IAAI;AAGb,MAAI,CAAC,QAAQ,QAAQ;AACnB,aAAK,oCAAoB,KAAK,QAAQ,UAAU,QAAQ,KAAK,QAAQ;AAAA,EACvE;AACF;AAEA,SAAS,UACP,KACA,oBACA,MACA;AACA,MAAI,KAAK,SAAS;AAChB,UAAM,aAAa,KAAK,UAAU,oBAAoB,MAAM,CAAC;AAC7D,mCAAW,KAAK,UAAU;AAC1B;AAAA,EACF;AACA,QAAM,EAAE,eAAe,IAAI;AAG3B,MAAI,WAAW,eAAe,EAAE;AAChC,MAAI,YAAY,SAAS,WAAW;AAClC,QAAI,SAAS,UAAU,gBAAgB,SAAS,GAAG;AACjD,UAAI,MAAM,GAAG,KAAK,SAAS,iBAAiB,SAAS;AAAA;AACrD,eAAS,IAAI,GAAG,IAAI,SAAS,UAAU,gBAAgB,QAAQ,KAAK;AAClE,cAAM,QAAQ,SAAS,UAAU,gBAAgB,CAAC;AAClD,YAAI,IAAI,GAAG;AACT,iBAAO;AAAA,QACT;AACA,eAAO,SAAS,YAAY,KAAK,CAAC;AAAA,MACpC;AACA,0CAAgB,KAAK,GAAG;AAAA,IAC1B;AACA,QAAI,SAAS,UAAU,cAAc,SAAS,GAAG;AAC/C,UAAI,MAAM,GAAG,KAAK,SAAS,cAAc,OAAO;AAAA;AAChD,eAAS,IAAI,GAAG,IAAI,SAAS,UAAU,cAAc,QAAQ,KAAK;AAChE,cAAM,QAAQ,SAAS,UAAU,cAAc,CAAC;AAChD,YAAI,IAAI,GAAG;AACT,iBAAO;AAAA,QACT;AACA,eAAO,SAAS,YAAY,KAAK,CAAC;AAAA,MACpC;AACA,0CAAgB,KAAK,GAAG;AAAA,IAC1B;AAAA,EACF;AAGA,aAAW,CAAC,eAAe,aAAa,KAAK,OAAO,QAAQ,cAAc,GAAG;AAC3E,QAAI,kBAAkB,IAAI;AACxB;AAAA,IACF;AACA,QAAI,cAAc,SAAS,SAAS,UAAU;AAC5C,0CAAgB,KAAK,uBAAuB,aAAa,GAAG;AAAA,IAC9D;AACA,QAAI,cAAc,SAAS,SAAS,WAAW;AAC7C,0CAAgB,KAAK,uBAAuB,aAAa,GAAG;AAAA,IAC9D;AACA,QAAI,cAAc,SAAS,SAAS,WAAW;AAC7C,0CAAgB,KAAK,uBAAuB,aAAa,GAAG;AAAA,IAC9D;AAAA,EACF;AACF;AAEA,SAAS,YAAY,OAA6B;AAChD,SAAO,GAAG,MAAM,IAAI;AACtB;", "names": ["functionsDir", "path", "chalk", "impl", "module"]}
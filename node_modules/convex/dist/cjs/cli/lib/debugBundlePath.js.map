{"version": 3, "sources": ["../../../../src/cli/lib/debugBundlePath.ts"], "sourcesContent": ["import path from \"path\";\nimport { Context } from \"../../bundler/context.js\";\nimport { Config } from \"./config.js\";\n\nexport async function handleDebugBundlePath(\n  ctx: Context,\n  debugBundleDir: string,\n  config: Config,\n) {\n  if (!ctx.fs.exists(debugBundleDir)) {\n    ctx.fs.mkdir(debugBundleDir);\n  } else if (!ctx.fs.stat(debugBundleDir).isDirectory()) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Path \\`${debugBundleDir}\\` is not a directory. Please choose an empty directory for \\`--debug-bundle-path\\`.`,\n    });\n  } else if (ctx.fs.listDir(debugBundleDir).length !== 0) {\n    await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Directory \\`${debugBundleDir}\\` is not empty. Please remove it or choose an empty directory for \\`--debug-bundle-path\\`.`,\n    });\n  }\n  ctx.fs.writeUtf8File(\n    path.join(debugBundleDir, \"fullConfig.json\"),\n    JSON.stringify(config),\n  );\n\n  for (const moduleInfo of config.modules) {\n    const trimmedPath = moduleInfo.path.endsWith(\".js\")\n      ? moduleInfo.path.slice(0, moduleInfo.path.length - \".js\".length)\n      : moduleInfo.path;\n    const environmentDir = path.join(debugBundleDir, moduleInfo.environment);\n    ctx.fs.mkdir(path.dirname(path.join(environmentDir, `${trimmedPath}.js`)), {\n      allowExisting: true,\n      recursive: true,\n    });\n    ctx.fs.writeUtf8File(\n      path.join(environmentDir, `${trimmedPath}.js`),\n      moduleInfo.source,\n    );\n    if (moduleInfo.sourceMap !== undefined) {\n      ctx.fs.writeUtf8File(\n        path.join(environmentDir, `${trimmedPath}.js.map`),\n        moduleInfo.sourceMap,\n      );\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AAIjB,eAAsB,sBACpB,KACA,gBACA,QACA;AACA,MAAI,CAAC,IAAI,GAAG,OAAO,cAAc,GAAG;AAClC,QAAI,GAAG,MAAM,cAAc;AAAA,EAC7B,WAAW,CAAC,IAAI,GAAG,KAAK,cAAc,EAAE,YAAY,GAAG;AACrD,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,UAAU,cAAc;AAAA,IAC1C,CAAC;AAAA,EACH,WAAW,IAAI,GAAG,QAAQ,cAAc,EAAE,WAAW,GAAG;AACtD,UAAM,IAAI,MAAM;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,eAAe,cAAc;AAAA,IAC/C,CAAC;AAAA,EACH;AACA,MAAI,GAAG;AAAA,IACL,YAAAA,QAAK,KAAK,gBAAgB,iBAAiB;AAAA,IAC3C,KAAK,UAAU,MAAM;AAAA,EACvB;AAEA,aAAW,cAAc,OAAO,SAAS;AACvC,UAAM,cAAc,WAAW,KAAK,SAAS,KAAK,IAC9C,WAAW,KAAK,MAAM,GAAG,WAAW,KAAK,SAAS,MAAM,MAAM,IAC9D,WAAW;AACf,UAAM,iBAAiB,YAAAA,QAAK,KAAK,gBAAgB,WAAW,WAAW;AACvE,QAAI,GAAG,MAAM,YAAAA,QAAK,QAAQ,YAAAA,QAAK,KAAK,gBAAgB,GAAG,WAAW,KAAK,CAAC,GAAG;AAAA,MACzE,eAAe;AAAA,MACf,WAAW;AAAA,IACb,CAAC;AACD,QAAI,GAAG;AAAA,MACL,YAAAA,QAAK,KAAK,gBAAgB,GAAG,WAAW,KAAK;AAAA,MAC7C,WAAW;AAAA,IACb;AACA,QAAI,WAAW,cAAc,QAAW;AACtC,UAAI,GAAG;AAAA,QACL,YAAAA,QAAK,KAAK,gBAAgB,GAAG,WAAW,SAAS;AAAA,QACjD,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;", "names": ["path"]}
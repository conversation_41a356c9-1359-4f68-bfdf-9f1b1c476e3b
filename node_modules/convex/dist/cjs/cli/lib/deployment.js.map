{"version": 3, "sources": ["../../../../src/cli/lib/deployment.ts"], "sourcesContent": ["import * as dotenv from \"dotenv\";\nimport { Context } from \"../../bundler/context.js\";\nimport { changedEnvVarFile, getEnvVarRegex } from \"./envvars.js\";\nimport {\n  CONVEX_DEPLOY_KEY_ENV_VAR_NAME,\n  CONVEX_DEPLOYMENT_ENV_VAR_NAME,\n  ENV_VAR_FILE_PATH,\n} from \"./utils/utils.js\";\nimport { DeploymentType } from \"./api.js\";\n\n// Given a deployment string like \"dev:tall-forest-1234\"\n// returns only the slug \"tall-forest-1234\".\n// If there's no prefix returns the original string.\nexport function stripDeploymentTypePrefix(deployment: string) {\n  return deployment.split(\":\").at(-1)!;\n}\n\n// Handling legacy CONVEX_DEPLOYMENT without type prefix as well\nexport function getDeploymentTypeFromConfiguredDeployment(raw: string) {\n  const typeRaw = raw.split(\":\")[0];\n  const type =\n    typeRaw === \"prod\" ||\n    typeRaw === \"dev\" ||\n    typeRaw === \"preview\" ||\n    typeRaw === \"local\"\n      ? typeRaw\n      : null;\n  return type;\n}\n\nexport function isAnonymousDeployment(deploymentName: string) {\n  return deploymentName.startsWith(\"anonymous-\");\n}\n\nexport function removeAnonymousPrefix(deploymentName: string) {\n  if (isAnonymousDeployment(deploymentName)) {\n    return deploymentName.slice(\"anonymous-\".length);\n  }\n  return deploymentName;\n}\n\nexport async function writeDeploymentEnvVar(\n  ctx: Context,\n  deploymentType: DeploymentType,\n  deployment: {\n    team: string | null;\n    project: string | null;\n    deploymentName: string;\n  },\n  existingValue: string | null,\n): Promise<{ wroteToGitIgnore: boolean; changedDeploymentEnvVar: boolean }> {\n  const existingFile = ctx.fs.exists(ENV_VAR_FILE_PATH)\n    ? ctx.fs.readUtf8File(ENV_VAR_FILE_PATH)\n    : null;\n  const changedFile = changesToEnvVarFile(\n    existingFile,\n    deploymentType,\n    deployment,\n  );\n  const deploymentEnvVarValue =\n    deploymentType + \":\" + deployment.deploymentName;\n\n  if (changedFile !== null) {\n    ctx.fs.writeUtf8File(ENV_VAR_FILE_PATH, changedFile);\n    // Only do this if we're not reinitializing an existing setup\n    return {\n      wroteToGitIgnore: await gitIgnoreEnvVarFile(ctx),\n      changedDeploymentEnvVar: existingValue !== deploymentEnvVarValue,\n    };\n  }\n  return {\n    wroteToGitIgnore: false,\n    changedDeploymentEnvVar: existingValue !== deploymentEnvVarValue,\n  };\n}\n\n// Only used in the internal --url flow\nexport async function eraseDeploymentEnvVar(ctx: Context): Promise<boolean> {\n  const existingFile = ctx.fs.exists(ENV_VAR_FILE_PATH)\n    ? ctx.fs.readUtf8File(ENV_VAR_FILE_PATH)\n    : null;\n  if (existingFile === null) {\n    return false;\n  }\n  const config = dotenv.parse(existingFile);\n  const existing = config[CONVEX_DEPLOYMENT_ENV_VAR_NAME];\n  if (existing === undefined) {\n    return false;\n  }\n  const changedFile = existingFile.replace(\n    getEnvVarRegex(CONVEX_DEPLOYMENT_ENV_VAR_NAME),\n    \"\",\n  );\n  ctx.fs.writeUtf8File(ENV_VAR_FILE_PATH, changedFile);\n  return true;\n}\n\nasync function gitIgnoreEnvVarFile(ctx: Context): Promise<boolean> {\n  const gitIgnorePath = \".gitignore\";\n  const gitIgnoreContents = ctx.fs.exists(gitIgnorePath)\n    ? ctx.fs.readUtf8File(gitIgnorePath)\n    : \"\";\n  const changedGitIgnore = changesToGitIgnore(gitIgnoreContents);\n  if (changedGitIgnore !== null) {\n    ctx.fs.writeUtf8File(gitIgnorePath, changedGitIgnore);\n    return true;\n  }\n  return false;\n}\n\n// exported for tests\nexport function changesToEnvVarFile(\n  existingFile: string | null,\n  deploymentType: DeploymentType,\n  {\n    team,\n    project,\n    deploymentName,\n  }: { team: string | null; project: string | null; deploymentName: string },\n): string | null {\n  const deploymentValue = deploymentType + \":\" + deploymentName;\n  const commentOnPreviousLine = \"# Deployment used by `npx convex dev`\";\n  const commentAfterValue =\n    team !== null && project !== null\n      ? `team: ${team}, project: ${project}`\n      : null;\n  return changedEnvVarFile({\n    existingFileContent: existingFile,\n    envVarName: CONVEX_DEPLOYMENT_ENV_VAR_NAME,\n    envVarValue: deploymentValue,\n    commentAfterValue,\n    commentOnPreviousLine,\n  });\n}\n\n// exported for tests\nexport function changesToGitIgnore(existingFile: string | null): string | null {\n  if (existingFile === null) {\n    return `${ENV_VAR_FILE_PATH}\\n`;\n  }\n  const gitIgnoreLines = existingFile.split(\"\\n\");\n  const envVarFileIgnored = gitIgnoreLines.some((line) => {\n    if (line.startsWith(\"#\")) return false;\n    if (line.startsWith(\"!\")) return false;\n\n    // .gitignore ignores trailing whitespace, and also we need to remove\n    // the trailing `\\r` from Windows-style newline since we split on `\\n`.\n    const trimmedLine = line.trimEnd();\n\n    const envIgnorePatterns = [\n      /^\\.env\\.local$/,\n      /^\\.env\\.\\*$/,\n      /^\\.env\\*$/,\n      /^.*\\.local$/,\n      /^\\.env\\*\\.local$/,\n    ];\n\n    return envIgnorePatterns.some((pattern) => pattern.test(trimmedLine));\n  });\n  if (!envVarFileIgnored) {\n    return `${existingFile}\\n${ENV_VAR_FILE_PATH}\\n`;\n  } else {\n    return null;\n  }\n}\n\nexport async function deploymentNameFromAdminKeyOrCrash(\n  ctx: Context,\n  adminKey: string,\n) {\n  const deploymentName = deploymentNameFromAdminKey(adminKey);\n  if (deploymentName === null) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Please set ${CONVEX_DEPLOY_KEY_ENV_VAR_NAME} to a new key which you can find on your Convex dashboard.`,\n    });\n  }\n  return deploymentName;\n}\n\nfunction deploymentNameFromAdminKey(adminKey: string) {\n  const parts = adminKey.split(\"|\");\n  if (parts.length === 1) {\n    return null;\n  }\n  if (isPreviewDeployKey(adminKey)) {\n    // Preview deploy keys do not contain a deployment name.\n    return null;\n  }\n  return stripDeploymentTypePrefix(parts[0]);\n}\n\n// Needed to differentiate a preview deploy key\n// from a concrete preview deployment's deploy key.\n// preview deploy key: `preview:team:project|key`\n// preview deployment's deploy key: `preview:deploymentName|key`\nexport function isPreviewDeployKey(adminKey: string) {\n  const parts = adminKey.split(\"|\");\n  if (parts.length === 1) {\n    return false;\n  }\n  const [prefix] = parts;\n  const prefixParts = prefix.split(\":\");\n  return prefixParts[0] === \"preview\" && prefixParts.length === 3;\n}\n\nexport function isProjectKey(adminKey: string) {\n  return /^project:.*\\|/.test(adminKey);\n}\n\n// For current keys returns prod|dev|preview,\n// for legacy keys returns \"prod\".\n// Examples:\n//  \"prod:deploymentName|key\" -> \"prod\"\n//  \"preview:deploymentName|key\" -> \"preview\"\n//  \"dev:deploymentName|key\" -> \"dev\"\n//  \"key\" -> \"prod\"\nexport function deploymentTypeFromAdminKey(adminKey: string) {\n  const parts = adminKey.split(\":\");\n  if (parts.length === 1) {\n    return \"prod\";\n  }\n  return parts.at(0)! as DeploymentType;\n}\n\nexport async function getTeamAndProjectFromPreviewAdminKey(\n  ctx: Context,\n  adminKey: string,\n) {\n  const parts = adminKey.split(\"|\")[0].split(\":\");\n  if (parts.length !== 3) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage:\n        \"Malformed preview CONVEX_DEPLOY_KEY, get a new key from Project Settings.\",\n    });\n  }\n  const [_preview, teamSlug, projectSlug] = parts;\n  return { teamSlug, projectSlug };\n}\n\nexport type OnDeploymentActivityFunc = (\n  isOffline: boolean,\n  wasOffline: boolean,\n) => Promise<void>;\nexport type CleanupDeploymentFunc = () => Promise<void>;\nexport type DeploymentDetails = {\n  deploymentName: string;\n  deploymentUrl: string;\n  adminKey: string;\n  onActivity: OnDeploymentActivityFunc | null;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAwB;AAExB,qBAAkD;AAClD,mBAIO;AAMA,SAAS,0BAA0B,YAAoB;AAC5D,SAAO,WAAW,MAAM,GAAG,EAAE,GAAG,EAAE;AACpC;AAGO,SAAS,0CAA0C,KAAa;AACrE,QAAM,UAAU,IAAI,MAAM,GAAG,EAAE,CAAC;AAChC,QAAM,OACJ,YAAY,UACZ,YAAY,SACZ,YAAY,aACZ,YAAY,UACR,UACA;AACN,SAAO;AACT;AAEO,SAAS,sBAAsB,gBAAwB;AAC5D,SAAO,eAAe,WAAW,YAAY;AAC/C;AAEO,SAAS,sBAAsB,gBAAwB;AAC5D,MAAI,sBAAsB,cAAc,GAAG;AACzC,WAAO,eAAe,MAAM,aAAa,MAAM;AAAA,EACjD;AACA,SAAO;AACT;AAEA,eAAsB,sBACpB,KACA,gBACA,YAKA,eAC0E;AAC1E,QAAM,eAAe,IAAI,GAAG,OAAO,8BAAiB,IAChD,IAAI,GAAG,aAAa,8BAAiB,IACrC;AACJ,QAAM,cAAc;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,wBACJ,iBAAiB,MAAM,WAAW;AAEpC,MAAI,gBAAgB,MAAM;AACxB,QAAI,GAAG,cAAc,gCAAmB,WAAW;AAEnD,WAAO;AAAA,MACL,kBAAkB,MAAM,oBAAoB,GAAG;AAAA,MAC/C,yBAAyB,kBAAkB;AAAA,IAC7C;AAAA,EACF;AACA,SAAO;AAAA,IACL,kBAAkB;AAAA,IAClB,yBAAyB,kBAAkB;AAAA,EAC7C;AACF;AAGA,eAAsB,sBAAsB,KAAgC;AAC1E,QAAM,eAAe,IAAI,GAAG,OAAO,8BAAiB,IAChD,IAAI,GAAG,aAAa,8BAAiB,IACrC;AACJ,MAAI,iBAAiB,MAAM;AACzB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,OAAO,MAAM,YAAY;AACxC,QAAM,WAAW,OAAO,2CAA8B;AACtD,MAAI,aAAa,QAAW;AAC1B,WAAO;AAAA,EACT;AACA,QAAM,cAAc,aAAa;AAAA,QAC/B,+BAAe,2CAA8B;AAAA,IAC7C;AAAA,EACF;AACA,MAAI,GAAG,cAAc,gCAAmB,WAAW;AACnD,SAAO;AACT;AAEA,eAAe,oBAAoB,KAAgC;AACjE,QAAM,gBAAgB;AACtB,QAAM,oBAAoB,IAAI,GAAG,OAAO,aAAa,IACjD,IAAI,GAAG,aAAa,aAAa,IACjC;AACJ,QAAM,mBAAmB,mBAAmB,iBAAiB;AAC7D,MAAI,qBAAqB,MAAM;AAC7B,QAAI,GAAG,cAAc,eAAe,gBAAgB;AACpD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGO,SAAS,oBACd,cACA,gBACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AACF,GACe;AACf,QAAM,kBAAkB,iBAAiB,MAAM;AAC/C,QAAM,wBAAwB;AAC9B,QAAM,oBACJ,SAAS,QAAQ,YAAY,OACzB,SAAS,IAAI,cAAc,OAAO,KAClC;AACN,aAAO,kCAAkB;AAAA,IACvB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAGO,SAAS,mBAAmB,cAA4C;AAC7E,MAAI,iBAAiB,MAAM;AACzB,WAAO,GAAG,8BAAiB;AAAA;AAAA,EAC7B;AACA,QAAM,iBAAiB,aAAa,MAAM,IAAI;AAC9C,QAAM,oBAAoB,eAAe,KAAK,CAAC,SAAS;AACtD,QAAI,KAAK,WAAW,GAAG,EAAG,QAAO;AACjC,QAAI,KAAK,WAAW,GAAG,EAAG,QAAO;AAIjC,UAAM,cAAc,KAAK,QAAQ;AAEjC,UAAM,oBAAoB;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,kBAAkB,KAAK,CAAC,YAAY,QAAQ,KAAK,WAAW,CAAC;AAAA,EACtE,CAAC;AACD,MAAI,CAAC,mBAAmB;AACtB,WAAO,GAAG,YAAY;AAAA,EAAK,8BAAiB;AAAA;AAAA,EAC9C,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,eAAsB,kCACpB,KACA,UACA;AACA,QAAM,iBAAiB,2BAA2B,QAAQ;AAC1D,MAAI,mBAAmB,MAAM;AAC3B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,cAAc,2CAA8B;AAAA,IAC9D,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,SAAS,2BAA2B,UAAkB;AACpD,QAAM,QAAQ,SAAS,MAAM,GAAG;AAChC,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,QAAQ,GAAG;AAEhC,WAAO;AAAA,EACT;AACA,SAAO,0BAA0B,MAAM,CAAC,CAAC;AAC3C;AAMO,SAAS,mBAAmB,UAAkB;AACnD,QAAM,QAAQ,SAAS,MAAM,GAAG;AAChC,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,QAAM,CAAC,MAAM,IAAI;AACjB,QAAM,cAAc,OAAO,MAAM,GAAG;AACpC,SAAO,YAAY,CAAC,MAAM,aAAa,YAAY,WAAW;AAChE;AAEO,SAAS,aAAa,UAAkB;AAC7C,SAAO,gBAAgB,KAAK,QAAQ;AACtC;AASO,SAAS,2BAA2B,UAAkB;AAC3D,QAAM,QAAQ,SAAS,MAAM,GAAG;AAChC,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,GAAG,CAAC;AACnB;AAEA,eAAsB,qCACpB,KACA,UACA;AACA,QAAM,QAAQ,SAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG;AAC9C,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AACA,QAAM,CAAC,UAAU,UAAU,WAAW,IAAI;AAC1C,SAAO,EAAE,UAAU,YAAY;AACjC;", "names": []}
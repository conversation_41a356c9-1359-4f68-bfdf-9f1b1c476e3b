{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/checkedComponent.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport {\n  componentDefinitionPath,\n  componentFunctionPath,\n  ComponentDefinitionPath,\n  ComponentPath,\n  componentPath,\n} from \"./paths.js\";\nimport { Identifier, identifier } from \"./types.js\";\nimport { looseObject } from \"./utils.js\";\n\nexport const resource = z.union([\n  looseObject({ type: z.literal(\"value\"), value: z.string() }),\n  looseObject({\n    type: z.literal(\"function\"),\n    path: componentFunctionPath,\n  }),\n]);\nexport type Resource = z.infer<typeof resource>;\n\nexport type CheckedExport =\n  | { type: \"branch\"; children: Record<Identifier, CheckedExport> }\n  | { type: \"leaf\"; resource: Resource };\nexport const checkedExport: z.ZodType<CheckedExport> = z.lazy(() =>\n  z.union([\n    looseObject({\n      type: z.literal(\"branch\"),\n      children: z.record(identifier, checkedExport),\n    }),\n    looseObject({\n      type: z.literal(\"leaf\"),\n      resource,\n    }),\n  ]),\n);\n\nexport const httpActionRoute = looseObject({\n  method: z.string(),\n  path: z.string(),\n});\n\nexport const checkedHttpRoutes = looseObject({\n  httpModuleRoutes: z.nullable(z.array(httpActionRoute)),\n  mounts: z.array(z.string()),\n});\nexport type CheckedHttpRoutes = z.infer<typeof checkedHttpRoutes>;\n\nexport type CheckedComponent = {\n  definitionPath: ComponentDefinitionPath;\n  componentPath: ComponentPath;\n  args: Record<Identifier, Resource>;\n  childComponents: Record<Identifier, CheckedComponent>;\n};\nexport const checkedComponent: z.ZodType<CheckedComponent> = z.lazy(() =>\n  looseObject({\n    definitionPath: componentDefinitionPath,\n    componentPath,\n    args: z.record(identifier, resource),\n    childComponents: z.record(identifier, checkedComponent),\n    httpRoutes: checkedHttpRoutes,\n    exports: z.record(identifier, checkedExport),\n  }),\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAClB,mBAMO;AACP,mBAAuC;AACvC,mBAA4B;AAErB,MAAM,WAAW,aAAE,MAAM;AAAA,MAC9B,0BAAY,EAAE,MAAM,aAAE,QAAQ,OAAO,GAAG,OAAO,aAAE,OAAO,EAAE,CAAC;AAAA,MAC3D,0BAAY;AAAA,IACV,MAAM,aAAE,QAAQ,UAAU;AAAA,IAC1B,MAAM;AAAA,EACR,CAAC;AACH,CAAC;AAMM,MAAM,gBAA0C,aAAE;AAAA,EAAK,MAC5D,aAAE,MAAM;AAAA,QACN,0BAAY;AAAA,MACV,MAAM,aAAE,QAAQ,QAAQ;AAAA,MACxB,UAAU,aAAE,OAAO,yBAAY,aAAa;AAAA,IAC9C,CAAC;AAAA,QACD,0BAAY;AAAA,MACV,MAAM,aAAE,QAAQ,MAAM;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEO,MAAM,sBAAkB,0BAAY;AAAA,EACzC,QAAQ,aAAE,OAAO;AAAA,EACjB,MAAM,aAAE,OAAO;AACjB,CAAC;AAEM,MAAM,wBAAoB,0BAAY;AAAA,EAC3C,kBAAkB,aAAE,SAAS,aAAE,MAAM,eAAe,CAAC;AAAA,EACrD,QAAQ,aAAE,MAAM,aAAE,OAAO,CAAC;AAC5B,CAAC;AASM,MAAM,mBAAgD,aAAE;AAAA,EAAK,UAClE,0BAAY;AAAA,IACV,gBAAgB;AAAA,IAChB;AAAA,IACA,MAAM,aAAE,OAAO,yBAAY,QAAQ;AAAA,IACnC,iBAAiB,aAAE,OAAO,yBAAY,gBAAgB;AAAA,IACtD,YAAY;AAAA,IACZ,SAAS,aAAE,OAAO,yBAAY,aAAa;AAAA,EAC7C,CAAC;AACH;", "names": []}
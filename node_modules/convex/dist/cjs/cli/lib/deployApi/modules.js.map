{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/modules.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { looseObject } from \"./utils.js\";\n\nexport const moduleEnvironment = z.union([\n  z.literal(\"isolate\"),\n  z.literal(\"node\"),\n]);\nexport type ModuleEnvironment = z.infer<typeof moduleEnvironment>;\n\nexport const moduleConfig = looseObject({\n  path: z.string(),\n  source: z.string(),\n  sourceMap: z.optional(z.string()),\n  environment: moduleEnvironment,\n});\nexport type ModuleConfig = z.infer<typeof moduleConfig>;\n\nexport const nodeDependency = looseObject({\n  name: z.string(),\n  version: z.string(),\n});\nexport type NodeDependency = z.infer<typeof nodeDependency>;\n\nexport const udfConfig = looseObject({\n  serverVersion: z.string(),\n  // RNG seed encoded as Convex bytes in JSON.\n  importPhaseRngSeed: z.any(),\n  // Timestamp encoded as a Convex Int64 in JSON.\n  importPhaseUnixTimestamp: z.any(),\n});\nexport type UdfConfig = z.infer<typeof udfConfig>;\n\nexport const sourcePackage = z.any();\nexport type SourcePackage = z.infer<typeof sourcePackage>;\n\nexport const visibility = z.union([\n  looseObject({ kind: z.literal(\"public\") }),\n  looseObject({ kind: z.literal(\"internal\") }),\n]);\nexport type Visibility = z.infer<typeof visibility>;\n\nexport const analyzedFunction = looseObject({\n  name: z.string(),\n  pos: z.any(),\n  udfType: z.union([\n    z.literal(\"Query\"),\n    z.literal(\"Mutation\"),\n    z.literal(\"Action\"),\n  ]),\n  visibility: z.nullable(visibility),\n  args: z.nullable(z.string()),\n  returns: z.nullable(z.string()),\n});\nexport type AnalyzedFunction = z.infer<typeof analyzedFunction>;\n\nexport const analyzedModule = looseObject({\n  functions: z.array(analyzedFunction),\n  httpRoutes: z.any(),\n  cronSpecs: z.any(),\n  sourceMapped: z.any(),\n});\nexport type AnalyzedModule = z.infer<typeof analyzedModule>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAClB,mBAA4B;AAErB,MAAM,oBAAoB,aAAE,MAAM;AAAA,EACvC,aAAE,QAAQ,SAAS;AAAA,EACnB,aAAE,QAAQ,MAAM;AAClB,CAAC;AAGM,MAAM,mBAAe,0BAAY;AAAA,EACtC,MAAM,aAAE,OAAO;AAAA,EACf,QAAQ,aAAE,OAAO;AAAA,EACjB,WAAW,aAAE,SAAS,aAAE,OAAO,CAAC;AAAA,EAChC,aAAa;AACf,CAAC;AAGM,MAAM,qBAAiB,0BAAY;AAAA,EACxC,MAAM,aAAE,OAAO;AAAA,EACf,SAAS,aAAE,OAAO;AACpB,CAAC;AAGM,MAAM,gBAAY,0BAAY;AAAA,EACnC,eAAe,aAAE,OAAO;AAAA;AAAA,EAExB,oBAAoB,aAAE,IAAI;AAAA;AAAA,EAE1B,0BAA0B,aAAE,IAAI;AAClC,CAAC;AAGM,MAAM,gBAAgB,aAAE,IAAI;AAG5B,MAAM,aAAa,aAAE,MAAM;AAAA,MAChC,0BAAY,EAAE,MAAM,aAAE,QAAQ,QAAQ,EAAE,CAAC;AAAA,MACzC,0BAAY,EAAE,MAAM,aAAE,QAAQ,UAAU,EAAE,CAAC;AAC7C,CAAC;AAGM,MAAM,uBAAmB,0BAAY;AAAA,EAC1C,MAAM,aAAE,OAAO;AAAA,EACf,KAAK,aAAE,IAAI;AAAA,EACX,SAAS,aAAE,MAAM;AAAA,IACf,aAAE,QAAQ,OAAO;AAAA,IACjB,aAAE,QAAQ,UAAU;AAAA,IACpB,aAAE,QAAQ,QAAQ;AAAA,EACpB,CAAC;AAAA,EACD,YAAY,aAAE,SAAS,UAAU;AAAA,EACjC,MAAM,aAAE,SAAS,aAAE,OAAO,CAAC;AAAA,EAC3B,SAAS,aAAE,SAAS,aAAE,OAAO,CAAC;AAChC,CAAC;AAGM,MAAM,qBAAiB,0BAAY;AAAA,EACxC,WAAW,aAAE,MAAM,gBAAgB;AAAA,EACnC,YAAY,aAAE,IAAI;AAAA,EAClB,WAAW,aAAE,IAAI;AAAA,EACjB,cAAc,aAAE,IAAI;AACtB,CAAC;", "names": []}
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var paths_exports = {};
__export(paths_exports, {
  canonicalizedModulePath: () => canonicalizedModulePath,
  componentDefinitionPath: () => componentDefinitionPath,
  componentFunctionPath: () => componentFunctionPath,
  componentPath: () => componentPath
});
module.exports = __toCommonJS(paths_exports);
var import_zod = require("zod");
var import_utils = require("./utils.js");
const componentDefinitionPath = import_zod.z.string();
const componentPath = import_zod.z.string();
const canonicalizedModulePath = import_zod.z.string();
const componentFunctionPath = (0, import_utils.looseObject)({
  component: import_zod.z.string(),
  udfPath: import_zod.z.string()
});
//# sourceMappingURL=paths.js.map

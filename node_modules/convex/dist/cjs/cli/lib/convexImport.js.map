{"version": 3, "sources": ["../../../../src/cli/lib/convexImport.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport {\n  formatSize,\n  waitUntilCalled,\n  deploymentFetch,\n  logAndHandleFetchError,\n} from \"./utils/utils.js\";\nimport {\n  logFailure,\n  Context,\n  showSpinner,\n  logFinishedStep,\n  logWarning,\n  logMessage,\n  stopSpinner,\n  changeSpinner,\n} from \"../../bundler/context.js\";\nimport path from \"path\";\nimport { subscribe } from \"./run.js\";\nimport { ConvexHttpClient } from \"../../browser/http_client.js\";\nimport { makeFunctionReference } from \"../../server/index.js\";\nimport { promptYesNo } from \"./utils/prompts.js\";\n\n// Backend has minimum chunk size of 5MiB except for the last chunk,\n// so we use 5MiB as highWaterMark which makes fs.ReadStream[asyncIterator]\n// output 5MiB chunks before the last one. This value can be overridden by\n// setting `CONVEX_IMPORT_CHUNK_SIZE` (bytes) in the environment.\nconst DEFAULT_CHUNK_SIZE = 5 * 1024 * 1024;\nconst ENV_CHUNK_SIZE = process.env.CONVEX_IMPORT_CHUNK_SIZE\n  ? parseInt(process.env.CONVEX_IMPORT_CHUNK_SIZE, 10)\n  : undefined;\n\nexport async function importIntoDeployment(\n  ctx: Context,\n  filePath: string,\n  options: {\n    deploymentUrl: string;\n    adminKey: string;\n    deploymentNotice: string;\n    snapshotImportDashboardLink: string | undefined;\n    table?: string;\n    format?: \"csv\" | \"jsonLines\" | \"jsonArray\" | \"zip\";\n    replace?: boolean;\n    append?: boolean;\n    replaceAll?: boolean;\n    yes?: boolean;\n    component?: string;\n  },\n) {\n  if (!ctx.fs.exists(filePath)) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `Error: Path ${chalk.bold(filePath)} does not exist.`,\n    });\n  }\n\n  const format = await determineFormat(ctx, filePath, options.format ?? null);\n  const tableName = options.table ?? null;\n  if (tableName === null) {\n    if (format !== \"zip\") {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Error: The \\`--table\\` option is required for format ${format}`,\n      });\n    }\n  } else {\n    if (format === \"zip\") {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Error: The \\`--table\\` option is not allowed for format ${format}`,\n      });\n    }\n  }\n\n  const convexClient = new ConvexHttpClient(options.deploymentUrl);\n  convexClient.setAdminAuth(options.adminKey);\n  const existingImports = await convexClient.query(\n    makeFunctionReference<\"query\", Record<string, never>, Array<unknown>>(\n      \"_system/cli/queryImport:list\",\n    ),\n    {},\n  );\n  const ongoingImports = existingImports.filter(\n    (i) => (i as any).state.state === \"in_progress\",\n  );\n  if (ongoingImports.length > 0) {\n    await askToConfirmImportWithExistingImports(\n      ctx,\n      options.snapshotImportDashboardLink,\n      options.yes,\n    );\n  }\n\n  const fileStats = ctx.fs.stat(filePath);\n  showSpinner(ctx, `Importing ${filePath} (${formatSize(fileStats.size)})`);\n\n  let mode = \"requireEmpty\";\n  if (options.append) {\n    mode = \"append\";\n  } else if (options.replace) {\n    mode = \"replace\";\n  } else if (options.replaceAll) {\n    mode = \"replaceAll\";\n  }\n  const importArgs = {\n    tableName: tableName === null ? undefined : tableName,\n    componentPath: options.component,\n    mode,\n    format,\n  };\n  const tableNotice = tableName ? ` to table \"${chalk.bold(tableName)}\"` : \"\";\n  const onFailure = async () => {\n    logFailure(\n      ctx,\n      `Importing data from \"${chalk.bold(\n        filePath,\n      )}\"${tableNotice}${options.deploymentNotice} failed`,\n    );\n  };\n  const importId = await uploadForImport(ctx, {\n    deploymentUrl: options.deploymentUrl,\n    adminKey: options.adminKey,\n    filePath,\n    importArgs,\n    onImportFailed: onFailure,\n  });\n  changeSpinner(ctx, \"Parsing uploaded data\");\n  const onProgress = (\n    ctx: Context,\n    state: InProgressImportState,\n    checkpointCount: number,\n  ) => {\n    stopSpinner(ctx);\n    while ((state.checkpoint_messages?.length ?? 0) > checkpointCount) {\n      logFinishedStep(ctx, state.checkpoint_messages![checkpointCount]);\n      checkpointCount += 1;\n    }\n    showSpinner(ctx, state.progress_message ?? \"Importing\");\n    return checkpointCount;\n  };\n  while (true) {\n    const snapshotImportState = await waitForStableImportState(ctx, {\n      importId,\n      deploymentUrl: options.deploymentUrl,\n      adminKey: options.adminKey,\n      onProgress,\n    });\n    switch (snapshotImportState.state) {\n      case \"completed\":\n        logFinishedStep(\n          ctx,\n          `Added ${snapshotImportState.num_rows_written} documents${tableNotice}${options.deploymentNotice}.`,\n        );\n        return;\n      case \"failed\":\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `Importing data from \"${chalk.bold(\n            filePath,\n          )}\"${tableNotice}${options.deploymentNotice} failed\\n\\n${chalk.red(snapshotImportState.error_message)}`,\n        });\n      case \"waiting_for_confirmation\": {\n        // Clear spinner state so we can log and prompt without clobbering lines.\n        stopSpinner(ctx);\n        await askToConfirmImport(\n          ctx,\n          snapshotImportState.message_to_confirm,\n          snapshotImportState.require_manual_confirmation,\n          options.yes,\n        );\n        showSpinner(ctx, `Importing`);\n        await confirmImport(ctx, {\n          importId,\n          adminKey: options.adminKey,\n          deploymentUrl: options.deploymentUrl,\n          onError: async () => {\n            logFailure(\n              ctx,\n              `Importing data from \"${chalk.bold(\n                filePath,\n              )}\"${tableNotice}${options.deploymentNotice} failed`,\n            );\n          },\n        });\n        // Now we have kicked off the rest of the import, go around the loop again.\n        break;\n      }\n      case \"uploaded\": {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `Import canceled while parsing uploaded file`,\n        });\n      }\n      case \"in_progress\": {\n        const visitDashboardLink = options.snapshotImportDashboardLink\n          ? ` Visit ${options.snapshotImportDashboardLink} to monitor its progress.`\n          : \"\";\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `WARNING: Import is continuing to run on the server.${visitDashboardLink}`,\n        });\n      }\n      default: {\n        const _: never = snapshotImportState;\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `unknown error: unexpected state ${snapshotImportState as any}`,\n          errForSentry: `unexpected snapshot import state ${(snapshotImportState as any).state}`,\n        });\n      }\n    }\n  }\n}\n\nasync function askToConfirmImport(\n  ctx: Context,\n  messageToConfirm: string | undefined,\n  requireManualConfirmation: boolean | undefined,\n  yes: boolean | undefined,\n) {\n  if (!messageToConfirm?.length) {\n    return;\n  }\n  logMessage(ctx, messageToConfirm);\n  if (requireManualConfirmation !== false && !yes) {\n    const confirmed = await promptYesNo(ctx, {\n      message: \"Perform import?\",\n      default: true,\n    });\n    if (!confirmed) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: \"Import canceled\",\n      });\n    }\n  }\n}\n\nasync function askToConfirmImportWithExistingImports(\n  ctx: Context,\n  snapshotImportDashboardLink: string | undefined,\n  yes: boolean | undefined,\n) {\n  const atDashboardLink = snapshotImportDashboardLink\n    ? ` You can view its progress at ${snapshotImportDashboardLink}.`\n    : \"\";\n  logMessage(\n    ctx,\n    `There is already a snapshot import in progress.${atDashboardLink}`,\n  );\n  if (yes) {\n    return;\n  }\n  const confirmed = await promptYesNo(ctx, {\n    message: \"Start another import?\",\n    default: true,\n  });\n  if (!confirmed) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Import canceled\",\n    });\n  }\n}\n\ntype InProgressImportState = {\n  state: \"in_progress\";\n  progress_message?: string | undefined;\n  checkpoint_messages?: string[] | undefined;\n};\n\ntype SnapshotImportState =\n  | { state: \"uploaded\" }\n  | {\n      state: \"waiting_for_confirmation\";\n      message_to_confirm?: string;\n      require_manual_confirmation?: boolean;\n    }\n  | InProgressImportState\n  | { state: \"completed\"; num_rows_written: bigint }\n  | { state: \"failed\"; error_message: string };\n\nexport async function waitForStableImportState(\n  ctx: Context,\n  args: {\n    importId: string;\n    deploymentUrl: string;\n    adminKey: string;\n    onProgress: (\n      ctx: Context,\n      state: InProgressImportState,\n      checkpointCount: number,\n    ) => number;\n  },\n): Promise<SnapshotImportState> {\n  const { importId, deploymentUrl, adminKey, onProgress } = args;\n  const [donePromise, onDone] = waitUntilCalled();\n  let snapshotImportState: SnapshotImportState;\n  let checkpointCount = 0;\n  await subscribe(ctx, {\n    deploymentUrl,\n    adminKey,\n    parsedFunctionName: \"_system/cli/queryImport\",\n    parsedFunctionArgs: { importId },\n    componentPath: undefined,\n    until: donePromise,\n    callbacks: {\n      onChange: (value: any) => {\n        snapshotImportState = value.state;\n        switch (snapshotImportState.state) {\n          case \"waiting_for_confirmation\":\n          case \"completed\":\n          case \"failed\":\n            onDone();\n            break;\n          case \"uploaded\":\n            // Not a stable state. Ignore while the server continues working.\n            return;\n          case \"in_progress\":\n            // Not a stable state. Ignore while the server continues working.\n            checkpointCount = onProgress(\n              ctx,\n              snapshotImportState,\n              checkpointCount,\n            );\n            return;\n        }\n      },\n    },\n  });\n  return snapshotImportState!;\n}\n\nasync function determineFormat(\n  ctx: Context,\n  filePath: string,\n  format: string | null,\n) {\n  const fileExtension = path.extname(filePath);\n  if (fileExtension !== \"\") {\n    const formatToExtension: Record<string, string> = {\n      csv: \".csv\",\n      jsonLines: \".jsonl\",\n      jsonArray: \".json\",\n      zip: \".zip\",\n    };\n    const extensionToFormat = Object.fromEntries(\n      Object.entries(formatToExtension).map((a) => a.reverse()),\n    );\n    if (format !== null && fileExtension !== formatToExtension[format]) {\n      logWarning(\n        ctx,\n        chalk.yellow(\n          `Warning: Extension of file ${filePath} (${fileExtension}) does not match specified format: ${format} (${formatToExtension[format]}).`,\n        ),\n      );\n    }\n    format ??= extensionToFormat[fileExtension] ?? null;\n  }\n  if (format === null) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage:\n        \"No input file format inferred by the filename extension or specified. Specify your input file's format using the `--format` flag.\",\n    });\n  }\n  return format;\n}\n\nexport async function confirmImport(\n  ctx: Context,\n  args: {\n    importId: string;\n    adminKey: string;\n    deploymentUrl: string;\n    onError: (e: any) => Promise<void>;\n  },\n) {\n  const { importId, adminKey, deploymentUrl } = args;\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl,\n    adminKey,\n  });\n  const performUrl = `/api/perform_import`;\n  try {\n    await fetch(performUrl, {\n      method: \"POST\",\n      body: JSON.stringify({ importId }),\n    });\n  } catch (e) {\n    await args.onError(e);\n    return await logAndHandleFetchError(ctx, e);\n  }\n}\n\nexport async function uploadForImport(\n  ctx: Context,\n  args: {\n    deploymentUrl: string;\n    adminKey: string;\n    filePath: string;\n    importArgs: {\n      tableName?: string;\n      componentPath?: string;\n      mode: string;\n      format: string;\n    };\n    onImportFailed: (e: any) => Promise<void>;\n  },\n) {\n  const { deploymentUrl, adminKey, filePath } = args;\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl,\n    adminKey,\n  });\n\n  const fileStats = ctx.fs.stat(filePath);\n  // The backend rejects uploads of 10k or more parts. We use 9999 instead of\n  // 10000 so rounding errors can't push us over the limit.\n  const minChunkSize = Math.ceil(fileStats.size / 9999);\n  let chunkSize = ENV_CHUNK_SIZE ?? DEFAULT_CHUNK_SIZE;\n  if (chunkSize < minChunkSize) {\n    chunkSize = minChunkSize;\n  }\n  const data = ctx.fs.createReadStream(filePath, {\n    highWaterMark: chunkSize,\n  });\n\n  showSpinner(ctx, `Importing ${filePath} (${formatSize(fileStats.size)})`);\n  let importId: string;\n  try {\n    const startResp = await fetch(\"/api/import/start_upload\", {\n      method: \"POST\",\n    });\n    const { uploadToken } = await startResp.json();\n\n    const partTokens = [];\n    let partNumber = 1;\n\n    for await (const chunk of data) {\n      const partUrl = `/api/import/upload_part?uploadToken=${encodeURIComponent(\n        uploadToken,\n      )}&partNumber=${partNumber}`;\n      const partResp = await fetch(partUrl, {\n        headers: {\n          \"Content-Type\": \"application/octet-stream\",\n        },\n        body: chunk,\n        method: \"POST\",\n      });\n      partTokens.push(await partResp.json());\n      partNumber += 1;\n      changeSpinner(\n        ctx,\n        `Uploading ${filePath} (${formatSize(data.bytesRead)}/${formatSize(\n          fileStats.size,\n        )})`,\n      );\n    }\n\n    const finishResp = await fetch(\"/api/import/finish_upload\", {\n      body: JSON.stringify({\n        import: args.importArgs,\n        uploadToken,\n        partTokens,\n      }),\n      method: \"POST\",\n    });\n    const body = await finishResp.json();\n    importId = body.importId;\n  } catch (e) {\n    await args.onImportFailed(e);\n    return await logAndHandleFetchError(ctx, e);\n  }\n  return importId;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,mBAKO;AACP,qBASO;AACP,kBAAiB;AACjB,iBAA0B;AAC1B,yBAAiC;AACjC,oBAAsC;AACtC,qBAA4B;AAM5B,MAAM,qBAAqB,IAAI,OAAO;AACtC,MAAM,iBAAiB,QAAQ,IAAI,2BAC/B,SAAS,QAAQ,IAAI,0BAA0B,EAAE,IACjD;AAEJ,eAAsB,qBACpB,KACA,UACA,SAaA;AACA,MAAI,CAAC,IAAI,GAAG,OAAO,QAAQ,GAAG;AAC5B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,eAAe,aAAAA,QAAM,KAAK,QAAQ,CAAC;AAAA,IACrD,CAAC;AAAA,EACH;AAEA,QAAM,SAAS,MAAM,gBAAgB,KAAK,UAAU,QAAQ,UAAU,IAAI;AAC1E,QAAM,YAAY,QAAQ,SAAS;AACnC,MAAI,cAAc,MAAM;AACtB,QAAI,WAAW,OAAO;AACpB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,wDAAwD,MAAM;AAAA,MAChF,CAAC;AAAA,IACH;AAAA,EACF,OAAO;AACL,QAAI,WAAW,OAAO;AACpB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,2DAA2D,MAAM;AAAA,MACnF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,eAAe,IAAI,oCAAiB,QAAQ,aAAa;AAC/D,eAAa,aAAa,QAAQ,QAAQ;AAC1C,QAAM,kBAAkB,MAAM,aAAa;AAAA,QACzC;AAAA,MACE;AAAA,IACF;AAAA,IACA,CAAC;AAAA,EACH;AACA,QAAM,iBAAiB,gBAAgB;AAAA,IACrC,CAAC,MAAO,EAAU,MAAM,UAAU;AAAA,EACpC;AACA,MAAI,eAAe,SAAS,GAAG;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,QAAM,YAAY,IAAI,GAAG,KAAK,QAAQ;AACtC,kCAAY,KAAK,aAAa,QAAQ,SAAK,yBAAW,UAAU,IAAI,CAAC,GAAG;AAExE,MAAI,OAAO;AACX,MAAI,QAAQ,QAAQ;AAClB,WAAO;AAAA,EACT,WAAW,QAAQ,SAAS;AAC1B,WAAO;AAAA,EACT,WAAW,QAAQ,YAAY;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,aAAa;AAAA,IACjB,WAAW,cAAc,OAAO,SAAY;AAAA,IAC5C,eAAe,QAAQ;AAAA,IACvB;AAAA,IACA;AAAA,EACF;AACA,QAAM,cAAc,YAAY,cAAc,aAAAA,QAAM,KAAK,SAAS,CAAC,MAAM;AACzE,QAAM,YAAY,YAAY;AAC5B;AAAA,MACE;AAAA,MACA,wBAAwB,aAAAA,QAAM;AAAA,QAC5B;AAAA,MACF,CAAC,IAAI,WAAW,GAAG,QAAQ,gBAAgB;AAAA,IAC7C;AAAA,EACF;AACA,QAAM,WAAW,MAAM,gBAAgB,KAAK;AAAA,IAC1C,eAAe,QAAQ;AAAA,IACvB,UAAU,QAAQ;AAAA,IAClB;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,EAClB,CAAC;AACD,oCAAc,KAAK,uBAAuB;AAC1C,QAAM,aAAa,CACjBC,MACA,OACA,oBACG;AACH,oCAAYA,IAAG;AACf,YAAQ,MAAM,qBAAqB,UAAU,KAAK,iBAAiB;AACjE,0CAAgBA,MAAK,MAAM,oBAAqB,eAAe,CAAC;AAChE,yBAAmB;AAAA,IACrB;AACA,oCAAYA,MAAK,MAAM,oBAAoB,WAAW;AACtD,WAAO;AAAA,EACT;AACA,SAAO,MAAM;AACX,UAAM,sBAAsB,MAAM,yBAAyB,KAAK;AAAA,MAC9D;AAAA,MACA,eAAe,QAAQ;AAAA,MACvB,UAAU,QAAQ;AAAA,MAClB;AAAA,IACF,CAAC;AACD,YAAQ,oBAAoB,OAAO;AAAA,MACjC,KAAK;AACH;AAAA,UACE;AAAA,UACA,SAAS,oBAAoB,gBAAgB,aAAa,WAAW,GAAG,QAAQ,gBAAgB;AAAA,QAClG;AACA;AAAA,MACF,KAAK;AACH,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,wBAAwB,aAAAD,QAAM;AAAA,YAC5C;AAAA,UACF,CAAC,IAAI,WAAW,GAAG,QAAQ,gBAAgB;AAAA;AAAA,EAAc,aAAAA,QAAM,IAAI,oBAAoB,aAAa,CAAC;AAAA,QACvG,CAAC;AAAA,MACH,KAAK,4BAA4B;AAE/B,wCAAY,GAAG;AACf,cAAM;AAAA,UACJ;AAAA,UACA,oBAAoB;AAAA,UACpB,oBAAoB;AAAA,UACpB,QAAQ;AAAA,QACV;AACA,wCAAY,KAAK,WAAW;AAC5B,cAAM,cAAc,KAAK;AAAA,UACvB;AAAA,UACA,UAAU,QAAQ;AAAA,UAClB,eAAe,QAAQ;AAAA,UACvB,SAAS,YAAY;AACnB;AAAA,cACE;AAAA,cACA,wBAAwB,aAAAA,QAAM;AAAA,gBAC5B;AAAA,cACF,CAAC,IAAI,WAAW,GAAG,QAAQ,gBAAgB;AAAA,YAC7C;AAAA,UACF;AAAA,QACF,CAAC;AAED;AAAA,MACF;AAAA,MACA,KAAK,YAAY;AACf,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,MACA,KAAK,eAAe;AAClB,cAAM,qBAAqB,QAAQ,8BAC/B,UAAU,QAAQ,2BAA2B,8BAC7C;AACJ,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,sDAAsD,kBAAkB;AAAA,QAC1F,CAAC;AAAA,MACH;AAAA,MACA,SAAS;AACP,cAAM,IAAW;AACjB,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,mCAAmC,mBAA0B;AAAA,UAC7E,cAAc,oCAAqC,oBAA4B,KAAK;AAAA,QACtF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,mBACb,KACA,kBACA,2BACA,KACA;AACA,MAAI,CAAC,kBAAkB,QAAQ;AAC7B;AAAA,EACF;AACA,iCAAW,KAAK,gBAAgB;AAChC,MAAI,8BAA8B,SAAS,CAAC,KAAK;AAC/C,UAAM,YAAY,UAAM,4BAAY,KAAK;AAAA,MACvC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,QAAI,CAAC,WAAW;AACd,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,eAAe,sCACb,KACA,6BACA,KACA;AACA,QAAM,kBAAkB,8BACpB,iCAAiC,2BAA2B,MAC5D;AACJ;AAAA,IACE;AAAA,IACA,kDAAkD,eAAe;AAAA,EACnE;AACA,MAAI,KAAK;AACP;AAAA,EACF;AACA,QAAM,YAAY,UAAM,4BAAY,KAAK;AAAA,IACvC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,CAAC;AACD,MAAI,CAAC,WAAW;AACd,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACF;AAmBA,eAAsB,yBACpB,KACA,MAU8B;AAC9B,QAAM,EAAE,UAAU,eAAe,UAAU,WAAW,IAAI;AAC1D,QAAM,CAAC,aAAa,MAAM,QAAI,8BAAgB;AAC9C,MAAI;AACJ,MAAI,kBAAkB;AACtB,YAAM,sBAAU,KAAK;AAAA,IACnB;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB,oBAAoB,EAAE,SAAS;AAAA,IAC/B,eAAe;AAAA,IACf,OAAO;AAAA,IACP,WAAW;AAAA,MACT,UAAU,CAAC,UAAe;AACxB,8BAAsB,MAAM;AAC5B,gBAAQ,oBAAoB,OAAO;AAAA,UACjC,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AAEH;AAAA,UACF,KAAK;AAEH,8BAAkB;AAAA,cAChB;AAAA,cACA;AAAA,cACA;AAAA,YACF;AACA;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,eAAe,gBACb,KACA,UACA,QACA;AACA,QAAM,gBAAgB,YAAAE,QAAK,QAAQ,QAAQ;AAC3C,MAAI,kBAAkB,IAAI;AACxB,UAAM,oBAA4C;AAAA,MAChD,KAAK;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,MACX,KAAK;AAAA,IACP;AACA,UAAM,oBAAoB,OAAO;AAAA,MAC/B,OAAO,QAAQ,iBAAiB,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC;AAAA,IAC1D;AACA,QAAI,WAAW,QAAQ,kBAAkB,kBAAkB,MAAM,GAAG;AAClE;AAAA,QACE;AAAA,QACA,aAAAF,QAAM;AAAA,UACJ,8BAA8B,QAAQ,KAAK,aAAa,sCAAsC,MAAM,KAAK,kBAAkB,MAAM,CAAC;AAAA,QACpI;AAAA,MACF;AAAA,IACF;AACA,wBAAW,kBAAkB,aAAa,KAAK;AAAA,EACjD;AACA,MAAI,WAAW,MAAM;AACnB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,eAAsB,cACpB,KACA,MAMA;AACA,QAAM,EAAE,UAAU,UAAU,cAAc,IAAI;AAC9C,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,aAAa;AACnB,MAAI;AACF,UAAM,MAAM,YAAY;AAAA,MACtB,QAAQ;AAAA,MACR,MAAM,KAAK,UAAU,EAAE,SAAS,CAAC;AAAA,IACnC,CAAC;AAAA,EACH,SAAS,GAAG;AACV,UAAM,KAAK,QAAQ,CAAC;AACpB,WAAO,UAAM,qCAAuB,KAAK,CAAC;AAAA,EAC5C;AACF;AAEA,eAAsB,gBACpB,KACA,MAYA;AACA,QAAM,EAAE,eAAe,UAAU,SAAS,IAAI;AAC9C,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,YAAY,IAAI,GAAG,KAAK,QAAQ;AAGtC,QAAM,eAAe,KAAK,KAAK,UAAU,OAAO,IAAI;AACpD,MAAI,YAAY,kBAAkB;AAClC,MAAI,YAAY,cAAc;AAC5B,gBAAY;AAAA,EACd;AACA,QAAM,OAAO,IAAI,GAAG,iBAAiB,UAAU;AAAA,IAC7C,eAAe;AAAA,EACjB,CAAC;AAED,kCAAY,KAAK,aAAa,QAAQ,SAAK,yBAAW,UAAU,IAAI,CAAC,GAAG;AACxE,MAAI;AACJ,MAAI;AACF,UAAM,YAAY,MAAM,MAAM,4BAA4B;AAAA,MACxD,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,EAAE,YAAY,IAAI,MAAM,UAAU,KAAK;AAE7C,UAAM,aAAa,CAAC;AACpB,QAAI,aAAa;AAEjB,qBAAiB,SAAS,MAAM;AAC9B,YAAM,UAAU,uCAAuC;AAAA,QACrD;AAAA,MACF,CAAC,eAAe,UAAU;AAC1B,YAAM,WAAW,MAAM,MAAM,SAAS;AAAA,QACpC,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AACD,iBAAW,KAAK,MAAM,SAAS,KAAK,CAAC;AACrC,oBAAc;AACd;AAAA,QACE;AAAA,QACA,aAAa,QAAQ,SAAK,yBAAW,KAAK,SAAS,CAAC,QAAI;AAAA,UACtD,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAEA,UAAM,aAAa,MAAM,MAAM,6BAA6B;AAAA,MAC1D,MAAM,KAAK,UAAU;AAAA,QACnB,QAAQ,KAAK;AAAA,QACb;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,OAAO,MAAM,WAAW,KAAK;AACnC,eAAW,KAAK;AAAA,EAClB,SAAS,GAAG;AACV,UAAM,KAAK,eAAe,CAAC;AAC3B,WAAO,UAAM,qCAAuB,KAAK,CAAC;AAAA,EAC5C;AACA,SAAO;AACT;", "names": ["chalk", "ctx", "path"]}
{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/dashboard.ts"], "sourcesContent": ["import { Context } from \"../../../bundler/context.js\";\nimport {\n  dashboardOutDir,\n  loadDashboardConfig,\n  loadUuidForAnonymousUser,\n  saveDashboardConfig,\n} from \"./filePaths.js\";\nimport { choosePorts } from \"./utils.js\";\nimport { startServer } from \"./serve.js\";\nimport { listExistingAnonymousDeployments } from \"./anonymous.js\";\nimport { localDeploymentUrl, selfHostedEventTag } from \"./run.js\";\nimport serveHandler from \"serve-handler\";\nimport { ensureDashboardDownloaded } from \"./download.js\";\nimport { bigBrainAPIMaybeThrows } from \"../utils/utils.js\";\n\nexport const DEFAULT_LOCAL_DASHBOARD_PORT = 6790;\nexport const DEFAULT_LOCAL_DASHBOARD_API_PORT = 6791;\n\n/**\n * This runs the `dashboard-self-hosted` app locally.\n * It's currently just used for the `anonymous` flow, while everything else\n * uses `dashboard.convex.dev`, and some of the code below is written\n * assuming this is only used for `anonymous`.\n */\nexport async function handleDashboard(ctx: Context, version: string) {\n  const anonymousId = loadUuidForAnonymousUser(ctx) ?? undefined;\n  const isRunning = await checkIfDashboardIsRunning(ctx);\n  if (isRunning) {\n    // It's possible this is running with a different version, but\n    // let's not worry about that for now.\n    return;\n  }\n  await ensureDashboardDownloaded(ctx, version);\n  const [dashboardPort, apiPort] = await choosePorts(ctx, {\n    count: 2,\n    startPort: DEFAULT_LOCAL_DASHBOARD_PORT,\n    requestedPorts: [null, null],\n  });\n  await saveDashboardConfig(ctx, {\n    port: dashboardPort,\n    apiPort,\n    version,\n  });\n\n  let hasReportedSelfHostedEvent = false;\n\n  const { cleanupHandle } = await startServer(\n    ctx,\n    dashboardPort,\n    async (request, response) => {\n      if (!hasReportedSelfHostedEvent) {\n        hasReportedSelfHostedEvent = true;\n        void reportSelfHostedEvent(ctx, {\n          anonymousId,\n          eventName: \"self_host_dashboard_connected\",\n          tag: selfHostedEventTag(\"anonymous\"),\n        });\n      }\n      await serveHandler(request, response, {\n        public: dashboardOutDir(),\n      });\n    },\n    {},\n  );\n  await startServingListDeploymentsApi(ctx, apiPort);\n  return {\n    dashboardPort,\n    cleanupHandle,\n  };\n}\n\nasync function reportSelfHostedEvent(\n  ctx: Context,\n  {\n    anonymousId,\n    eventName,\n    eventFields,\n    tag,\n  }: {\n    anonymousId: string | undefined;\n    eventName: string;\n    eventFields?: Record<string, unknown>;\n    tag: string | undefined;\n  },\n) {\n  try {\n    await bigBrainAPIMaybeThrows({\n      ctx,\n      method: \"POST\",\n      url: \"/api/self_hosted_event\",\n      data: {\n        selfHostedUuid: anonymousId,\n        eventName,\n        eventFields,\n        tag,\n      },\n    });\n  } catch {\n    // ignore\n  }\n}\n\n/**\n * This serves a really basic API that just returns a JSON blob with the deployments\n * and their credentials.\n * The locally running dashboard can hit this API.\n */\nasync function startServingListDeploymentsApi(ctx: Context, port: number) {\n  await startServer(\n    ctx,\n    port,\n    async (request, response) => {\n      const deployments = await listExistingAnonymousDeployments(ctx);\n      const deploymentsJson = deployments.map((d) => ({\n        name: d.deploymentName,\n        url: localDeploymentUrl(d.config.ports.cloud),\n        adminKey: d.config.adminKey,\n      }));\n      response.setHeader(\"Content-Type\", \"application/json\");\n      response.end(JSON.stringify({ deployments: deploymentsJson }));\n    },\n    {\n      cors: true,\n    },\n  );\n}\n\nexport async function checkIfDashboardIsRunning(ctx: Context) {\n  const dashboardConfig = loadDashboardConfig(ctx);\n  if (dashboardConfig === null) {\n    return false;\n  }\n  // We're checking if the mini API server is running and has a response that\n  // looks like a list of deployments, since it's easier than checking the\n  // dashboard UI + won't trigger the event for the developer opening the dashboard.\n  let resp: Response;\n  try {\n    resp = await fetch(`http://127.0.0.1:${dashboardConfig.apiPort}`);\n  } catch {\n    return false;\n  }\n  if (!resp.ok) {\n    return false;\n  }\n  let data: { deployments: { name: string; url: string; adminKey: string }[] };\n  try {\n    data = await resp.json();\n  } catch {\n    return false;\n  }\n  return Array.isArray(data.deployments);\n}\n\nexport function dashboardUrl(ctx: Context, deploymentName: string) {\n  const dashboardConfig = loadDashboardConfig(ctx);\n  if (dashboardConfig === null) {\n    return null;\n  }\n\n  const queryParams = new URLSearchParams();\n  if (dashboardConfig.apiPort !== DEFAULT_LOCAL_DASHBOARD_API_PORT) {\n    queryParams.set(\"a\", dashboardConfig.apiPort.toString());\n  }\n  queryParams.set(\"d\", deploymentName);\n  const queryString = queryParams.toString();\n  const url = new URL(`http://127.0.0.1:${dashboardConfig.port}`);\n  url.search = queryString;\n  return url.href;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,uBAKO;AACP,mBAA4B;AAC5B,mBAA4B;AAC5B,uBAAiD;AACjD,iBAAuD;AACvD,2BAAyB;AACzB,sBAA0C;AAC1C,IAAAA,gBAAuC;AAEhC,MAAM,+BAA+B;AACrC,MAAM,mCAAmC;AAQhD,eAAsB,gBAAgB,KAAc,SAAiB;AACnE,QAAM,kBAAc,2CAAyB,GAAG,KAAK;AACrD,QAAM,YAAY,MAAM,0BAA0B,GAAG;AACrD,MAAI,WAAW;AAGb;AAAA,EACF;AACA,YAAM,2CAA0B,KAAK,OAAO;AAC5C,QAAM,CAAC,eAAe,OAAO,IAAI,UAAM,0BAAY,KAAK;AAAA,IACtD,OAAO;AAAA,IACP,WAAW;AAAA,IACX,gBAAgB,CAAC,MAAM,IAAI;AAAA,EAC7B,CAAC;AACD,YAAM,sCAAoB,KAAK;AAAA,IAC7B,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI,6BAA6B;AAEjC,QAAM,EAAE,cAAc,IAAI,UAAM;AAAA,IAC9B;AAAA,IACA;AAAA,IACA,OAAO,SAAS,aAAa;AAC3B,UAAI,CAAC,4BAA4B;AAC/B,qCAA6B;AAC7B,aAAK,sBAAsB,KAAK;AAAA,UAC9B;AAAA,UACA,WAAW;AAAA,UACX,SAAK,+BAAmB,WAAW;AAAA,QACrC,CAAC;AAAA,MACH;AACA,gBAAM,qBAAAC,SAAa,SAAS,UAAU;AAAA,QACpC,YAAQ,kCAAgB;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,IACA,CAAC;AAAA,EACH;AACA,QAAM,+BAA+B,KAAK,OAAO;AACjD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,eAAe,sBACb,KACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAMA;AACA,MAAI;AACF,cAAM,sCAAuB;AAAA,MAC3B;AAAA,MACA,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,QACJ,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,QAAQ;AAAA,EAER;AACF;AAOA,eAAe,+BAA+B,KAAc,MAAc;AACxE,YAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO,SAAS,aAAa;AAC3B,YAAM,cAAc,UAAM,mDAAiC,GAAG;AAC9D,YAAM,kBAAkB,YAAY,IAAI,CAAC,OAAO;AAAA,QAC9C,MAAM,EAAE;AAAA,QACR,SAAK,+BAAmB,EAAE,OAAO,MAAM,KAAK;AAAA,QAC5C,UAAU,EAAE,OAAO;AAAA,MACrB,EAAE;AACF,eAAS,UAAU,gBAAgB,kBAAkB;AACrD,eAAS,IAAI,KAAK,UAAU,EAAE,aAAa,gBAAgB,CAAC,CAAC;AAAA,IAC/D;AAAA,IACA;AAAA,MACE,MAAM;AAAA,IACR;AAAA,EACF;AACF;AAEA,eAAsB,0BAA0B,KAAc;AAC5D,QAAM,sBAAkB,sCAAoB,GAAG;AAC/C,MAAI,oBAAoB,MAAM;AAC5B,WAAO;AAAA,EACT;AAIA,MAAI;AACJ,MAAI;AACF,WAAO,MAAM,MAAM,oBAAoB,gBAAgB,OAAO,EAAE;AAAA,EAClE,QAAQ;AACN,WAAO;AAAA,EACT;AACA,MAAI,CAAC,KAAK,IAAI;AACZ,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AACF,WAAO,MAAM,KAAK,KAAK;AAAA,EACzB,QAAQ;AACN,WAAO;AAAA,EACT;AACA,SAAO,MAAM,QAAQ,KAAK,WAAW;AACvC;AAEO,SAAS,aAAa,KAAc,gBAAwB;AACjE,QAAM,sBAAkB,sCAAoB,GAAG;AAC/C,MAAI,oBAAoB,MAAM;AAC5B,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,IAAI,gBAAgB;AACxC,MAAI,gBAAgB,YAAY,kCAAkC;AAChE,gBAAY,IAAI,KAAK,gBAAgB,QAAQ,SAAS,CAAC;AAAA,EACzD;AACA,cAAY,IAAI,KAAK,cAAc;AACnC,QAAM,cAAc,YAAY,SAAS;AACzC,QAAM,MAAM,IAAI,IAAI,oBAAoB,gBAAgB,IAAI,EAAE;AAC9D,MAAI,SAAS;AACb,SAAO,IAAI;AACb;", "names": ["import_utils", "<PERSON><PERSON><PERSON><PERSON>"]}
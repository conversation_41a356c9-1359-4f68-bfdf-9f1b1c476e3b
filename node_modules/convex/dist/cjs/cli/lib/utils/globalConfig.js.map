{"version": 3, "sources": ["../../../../../src/cli/lib/utils/globalConfig.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport os from \"os\";\nimport path from \"path\";\nimport { rootDirectory } from \"./utils.js\";\nimport { Context, logError, logVerbose } from \"../../../bundler/context.js\";\nimport { z } from \"zod\";\n\nexport function globalConfigPath(): string {\n  return path.join(rootDirectory(), \"config.json\");\n}\n\n// GlobalConfig is stored in a file that very old versions of Convex also need to access.\n// Everything besides accessToken must be optional forever.\n// GlobalConfig is deleted on logout. It is primarily used for the accessToken.\nexport type GlobalConfig = {\n  accessToken: string;\n  // Means \"Don't use local dev unless CLI version is at least 1.19\" (actual version TBD)\n  optOutOfLocalDevDeploymentsUntilBetaOver?: boolean;\n};\n\nconst schema = z.object({\n  accessToken: z.string().min(1),\n  optOutOfLocalDevDeploymentsUntilBetaOver: z.boolean().optional(),\n});\n\nexport function readGlobalConfig(ctx: Context): GlobalConfig | null {\n  const configPath = globalConfigPath();\n  let configFile;\n  try {\n    configFile = ctx.fs.readUtf8File(configPath);\n  } catch {\n    return null;\n  }\n  try {\n    const storedConfig = JSON.parse(configFile);\n    const config: GlobalConfig = schema.parse(storedConfig);\n    return config;\n  } catch (err) {\n    // Print an error and act as if the file does not exist.\n    logError(\n      ctx,\n      chalk.red(\n        `Failed to parse global config in ${configPath} with error ${\n          err as any\n        }.`,\n      ),\n    );\n    return null;\n  }\n}\n\n/** Write the global config, preserving existing properties we don't understand. */\nexport async function modifyGlobalConfig(ctx: Context, config: GlobalConfig) {\n  const configPath = globalConfigPath();\n  let configFile;\n  try {\n    configFile = ctx.fs.readUtf8File(configPath);\n    // totally fine for it not to exist\n    // eslint-disable-next-line no-empty\n  } catch {}\n  // storedConfig may contain properties this version of the CLI doesn't understand.\n  let storedConfig = {};\n  if (configFile) {\n    try {\n      storedConfig = JSON.parse(configFile);\n      schema.parse(storedConfig);\n    } catch (err) {\n      logError(\n        ctx,\n        chalk.red(\n          `Failed to parse global config in ${configPath} with error ${\n            err as any\n          }.`,\n        ),\n      );\n      storedConfig = {};\n    }\n  }\n  const newConfig: GlobalConfig = { ...storedConfig, ...config };\n  await overrwriteGlobalConfig(ctx, newConfig);\n}\n\n/** Write global config, overwriting any existing settings. */\nasync function overrwriteGlobalConfig(ctx: Context, config: GlobalConfig) {\n  const dirName = rootDirectory();\n  ctx.fs.mkdir(dirName, { allowExisting: true });\n  const path = globalConfigPath();\n  try {\n    ctx.fs.writeUtf8File(path, JSON.stringify(config, null, 2));\n  } catch (err) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      errForSentry: err,\n      printedMessage: chalk.red(\n        `Failed to write auth config to ${path} with error: ${err as any}`,\n      ),\n    });\n  }\n  logVerbose(ctx, `Saved credentials to ${formatPathForPrinting(path)}`);\n}\n\nexport function formatPathForPrinting(path: string) {\n  const homedir = os.homedir();\n  if (process.platform === \"darwin\" && path.startsWith(homedir)) {\n    return path.replace(homedir, \"~\");\n  }\n  return path;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,gBAAe;AACf,kBAAiB;AACjB,mBAA8B;AAC9B,qBAA8C;AAC9C,iBAAkB;AAEX,SAAS,mBAA2B;AACzC,SAAO,YAAAA,QAAK,SAAK,4BAAc,GAAG,aAAa;AACjD;AAWA,MAAM,SAAS,aAAE,OAAO;AAAA,EACtB,aAAa,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAC7B,0CAA0C,aAAE,QAAQ,EAAE,SAAS;AACjE,CAAC;AAEM,SAAS,iBAAiB,KAAmC;AAClE,QAAM,aAAa,iBAAiB;AACpC,MAAI;AACJ,MAAI;AACF,iBAAa,IAAI,GAAG,aAAa,UAAU;AAAA,EAC7C,QAAQ;AACN,WAAO;AAAA,EACT;AACA,MAAI;AACF,UAAM,eAAe,KAAK,MAAM,UAAU;AAC1C,UAAM,SAAuB,OAAO,MAAM,YAAY;AACtD,WAAO;AAAA,EACT,SAAS,KAAK;AAEZ;AAAA,MACE;AAAA,MACA,aAAAC,QAAM;AAAA,QACJ,oCAAoC,UAAU,eAC5C,GACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,eAAsB,mBAAmB,KAAc,QAAsB;AAC3E,QAAM,aAAa,iBAAiB;AACpC,MAAI;AACJ,MAAI;AACF,iBAAa,IAAI,GAAG,aAAa,UAAU;AAAA,EAG7C,QAAQ;AAAA,EAAC;AAET,MAAI,eAAe,CAAC;AACpB,MAAI,YAAY;AACd,QAAI;AACF,qBAAe,KAAK,MAAM,UAAU;AACpC,aAAO,MAAM,YAAY;AAAA,IAC3B,SAAS,KAAK;AACZ;AAAA,QACE;AAAA,QACA,aAAAA,QAAM;AAAA,UACJ,oCAAoC,UAAU,eAC5C,GACF;AAAA,QACF;AAAA,MACF;AACA,qBAAe,CAAC;AAAA,IAClB;AAAA,EACF;AACA,QAAM,YAA0B,EAAE,GAAG,cAAc,GAAG,OAAO;AAC7D,QAAM,uBAAuB,KAAK,SAAS;AAC7C;AAGA,eAAe,uBAAuB,KAAc,QAAsB;AACxE,QAAM,cAAU,4BAAc;AAC9B,MAAI,GAAG,MAAM,SAAS,EAAE,eAAe,KAAK,CAAC;AAC7C,QAAMD,QAAO,iBAAiB;AAC9B,MAAI;AACF,QAAI,GAAG,cAAcA,OAAM,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;AAAA,EAC5D,SAAS,KAAK;AACZ,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB,aAAAC,QAAM;AAAA,QACpB,kCAAkCD,KAAI,gBAAgB,GAAU;AAAA,MAClE;AAAA,IACF,CAAC;AAAA,EACH;AACA,iCAAW,KAAK,wBAAwB,sBAAsBA,KAAI,CAAC,EAAE;AACvE;AAEO,SAAS,sBAAsBA,OAAc;AAClD,QAAM,UAAU,UAAAE,QAAG,QAAQ;AAC3B,MAAI,QAAQ,aAAa,YAAYF,MAAK,WAAW,OAAO,GAAG;AAC7D,WAAOA,MAAK,QAAQ,SAAS,GAAG;AAAA,EAClC;AACA,SAAOA;AACT;", "names": ["path", "chalk", "os"]}
{"version": 3, "sources": ["../../../../../src/cli/lib/utils/sentry.ts"], "sourcesContent": ["import \"@sentry/tracing\";\nimport { productionProvisionHost, provisionHost } from \"../config.js\";\nimport stripAnsi from \"strip-ansi\";\nimport * as Sentry from \"@sentry/node\";\nimport { version } from \"../../../index.js\";\n\nexport const SENTRY_DSN =\n  \"https://<EMAIL>/6390839\";\n\nexport function initSentry() {\n  if (\n    (!process.env.CI || process.env.VERCEL === \"1\") &&\n    provisionHost === productionProvisionHost\n  ) {\n    Sentry.init({\n      dsn: SENTRY_DSN,\n      release: \"cli@\" + version,\n      tracesSampleRate: 0.2,\n      beforeBreadcrumb: (breadcrumb) => {\n        // Strip ANSI color codes from log lines that are sent as breadcrumbs.\n        if (breadcrumb.message) {\n          breadcrumb.message = stripAnsi(breadcrumb.message);\n        }\n        return breadcrumb;\n      },\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAO;AACP,oBAAuD;AACvD,wBAAsB;AACtB,aAAwB;AACxB,eAAwB;AAEjB,MAAM,aACX;AAEK,SAAS,aAAa;AAC3B,OACG,CAAC,QAAQ,IAAI,MAAM,QAAQ,IAAI,WAAW,QAC3C,gCAAkB,uCAClB;AACA,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,SAAS,SAAS;AAAA,MAClB,kBAAkB;AAAA,MAClB,kBAAkB,CAAC,eAAe;AAEhC,YAAI,WAAW,SAAS;AACtB,qBAAW,cAAU,kBAAAA,SAAU,WAAW,OAAO;AAAA,QACnD;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF;", "names": ["stripAnsi"]}
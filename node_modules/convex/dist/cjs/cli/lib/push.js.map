{"version": 3, "sources": ["../../../../src/cli/lib/push.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { Context, changeSpinner, logMessage } from \"../../bundler/context.js\";\nimport { doCodegen } from \"./codegen.js\";\nimport {\n  ProjectConfig,\n  configFromProjectConfig,\n  diffConfig,\n  pullConfig,\n  pushConfig,\n} from \"./config.js\";\nimport { pushSchema } from \"./indexes.js\";\nimport { typeCheckFunctionsInMode } from \"./typecheck.js\";\nimport { ensureHasConvexDependency, functionsDir } from \"./utils/utils.js\";\nimport { handleDebugBundlePath } from \"./debugBundlePath.js\";\n\nimport { LogManager } from \"./logs.js\";\n\nexport type PushOptions = {\n  adminKey: string;\n  verbose: boolean;\n  dryRun: boolean;\n  typecheck: \"enable\" | \"try\" | \"disable\";\n  typecheckComponents: boolean;\n  debug: boolean;\n  debugBundlePath?: string;\n  codegen: boolean;\n  url: string;\n  deploymentName: string | null;\n  writePushRequest?: string;\n  liveComponentSources: boolean;\n  logManager?: LogManager;\n};\n\nexport async function runNonComponentsPush(\n  ctx: Context,\n  options: PushOptions,\n  configPath: string,\n  projectConfig: ProjectConfig,\n) {\n  if (options.writePushRequest) {\n    logMessage(\n      ctx,\n      \"Skipping push because --write-push-request is set, but we are on the non-components path so there is nothing to write.\",\n    );\n    return;\n  }\n  const timeRunPushStarts = performance.now();\n  const origin = options.url;\n  const verbose = options.verbose || options.dryRun;\n  if (verbose) {\n    process.env[\"CONVEX_VERBOSE\"] = \"1\";\n  }\n  await ensureHasConvexDependency(ctx, \"push\");\n\n  if (!options.codegen) {\n    logMessage(\n      ctx,\n      chalk.gray(\"Skipping codegen. Remove --codegen=disable to enable.\"),\n    );\n    // Codegen includes typechecking, so if we're skipping it, run the type\n    // check manually on the query and mutation functions\n    const funcDir = functionsDir(configPath, projectConfig);\n    await typeCheckFunctionsInMode(ctx, options.typecheck, funcDir);\n  } else {\n    await doCodegen(\n      ctx,\n      functionsDir(configPath, projectConfig),\n      options.typecheck,\n      options,\n    );\n    if (verbose) {\n      logMessage(ctx, chalk.green(\"Codegen finished.\"));\n    }\n  }\n\n  const timeBundleStarts = performance.now();\n  const { config: localConfig, bundledModuleInfos } =\n    await configFromProjectConfig(ctx, projectConfig, configPath, verbose);\n\n  if (options.debugBundlePath) {\n    await handleDebugBundlePath(ctx, options.debugBundlePath, localConfig);\n    logMessage(\n      ctx,\n      `Wrote bundle and metadata to ${options.debugBundlePath}. Skipping rest of push.`,\n    );\n    return;\n  }\n\n  const timeSchemaPushStarts = performance.now();\n  const { schemaId, schemaState } = await pushSchema(\n    ctx,\n    origin,\n    options.adminKey,\n    functionsDir(configPath, localConfig.projectConfig),\n    options.dryRun,\n  );\n\n  const timeConfigPullStarts = performance.now();\n  const remoteConfigWithModuleHashes = await pullConfig(\n    ctx,\n    undefined,\n    undefined,\n    origin,\n    options.adminKey,\n  );\n\n  changeSpinner(ctx, \"Diffing local code and deployment state\");\n  const { diffString, stats } = diffConfig(\n    remoteConfigWithModuleHashes,\n    localConfig,\n  );\n  if (diffString === \"\" && schemaState?.state === \"active\") {\n    if (verbose) {\n      const msg =\n        localConfig.modules.length === 0\n          ? `No functions found in ${localConfig.projectConfig.functions}`\n          : \"Config already synced\";\n      logMessage(\n        ctx,\n        chalk.gray(\n          `${\n            options.dryRun\n              ? \"Command would skip function push\"\n              : \"Function push skipped\"\n          }: ${msg}.`,\n        ),\n      );\n    }\n    return;\n  }\n\n  if (verbose) {\n    logMessage(\n      ctx,\n      chalk.bold(\n        `Remote config ${\n          options.dryRun ? \"would\" : \"will\"\n        } be overwritten with the following changes:`,\n      ),\n    );\n    logMessage(ctx, diffString);\n  }\n\n  if (options.dryRun) {\n    return;\n  }\n\n  // Note that this is not quite a user pain metric: we're missing any time\n  // spent making and retrying this network request and receiving the response.\n  const timePushStarts = performance.now();\n  const timing = {\n    typecheck: (timeBundleStarts - timeRunPushStarts) / 1000,\n    bundle: (timeSchemaPushStarts - timeBundleStarts) / 1000,\n    schemaPush: (timeConfigPullStarts - timeSchemaPushStarts) / 1000,\n    codePull: (timePushStarts - timeConfigPullStarts) / 1000,\n    totalBeforePush: (timePushStarts - timeRunPushStarts) / 1000,\n    moduleDiffStats: stats,\n  };\n  await pushConfig(ctx, localConfig, {\n    adminKey: options.adminKey,\n    url: options.url,\n    deploymentName: options.deploymentName,\n    pushMetrics: timing,\n    schemaId,\n    bundledModuleInfos,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,qBAAmD;AACnD,qBAA0B;AAC1B,oBAMO;AACP,qBAA2B;AAC3B,uBAAyC;AACzC,mBAAwD;AACxD,6BAAsC;AAoBtC,eAAsB,qBACpB,KACA,SACA,YACA,eACA;AACA,MAAI,QAAQ,kBAAkB;AAC5B;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA;AAAA,EACF;AACA,QAAM,oBAAoB,YAAY,IAAI;AAC1C,QAAM,SAAS,QAAQ;AACvB,QAAM,UAAU,QAAQ,WAAW,QAAQ;AAC3C,MAAI,SAAS;AACX,YAAQ,IAAI,gBAAgB,IAAI;AAAA,EAClC;AACA,YAAM,wCAA0B,KAAK,MAAM;AAE3C,MAAI,CAAC,QAAQ,SAAS;AACpB;AAAA,MACE;AAAA,MACA,aAAAA,QAAM,KAAK,uDAAuD;AAAA,IACpE;AAGA,UAAM,cAAU,2BAAa,YAAY,aAAa;AACtD,cAAM,2CAAyB,KAAK,QAAQ,WAAW,OAAO;AAAA,EAChE,OAAO;AACL,cAAM;AAAA,MACJ;AAAA,UACA,2BAAa,YAAY,aAAa;AAAA,MACtC,QAAQ;AAAA,MACR;AAAA,IACF;AACA,QAAI,SAAS;AACX,qCAAW,KAAK,aAAAA,QAAM,MAAM,mBAAmB,CAAC;AAAA,IAClD;AAAA,EACF;AAEA,QAAM,mBAAmB,YAAY,IAAI;AACzC,QAAM,EAAE,QAAQ,aAAa,mBAAmB,IAC9C,UAAM,uCAAwB,KAAK,eAAe,YAAY,OAAO;AAEvE,MAAI,QAAQ,iBAAiB;AAC3B,cAAM,8CAAsB,KAAK,QAAQ,iBAAiB,WAAW;AACrE;AAAA,MACE;AAAA,MACA,gCAAgC,QAAQ,eAAe;AAAA,IACzD;AACA;AAAA,EACF;AAEA,QAAM,uBAAuB,YAAY,IAAI;AAC7C,QAAM,EAAE,UAAU,YAAY,IAAI,UAAM;AAAA,IACtC;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,QACR,2BAAa,YAAY,YAAY,aAAa;AAAA,IAClD,QAAQ;AAAA,EACV;AAEA,QAAM,uBAAuB,YAAY,IAAI;AAC7C,QAAM,+BAA+B,UAAM;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV;AAEA,oCAAc,KAAK,yCAAyC;AAC5D,QAAM,EAAE,YAAY,MAAM,QAAI;AAAA,IAC5B;AAAA,IACA;AAAA,EACF;AACA,MAAI,eAAe,MAAM,aAAa,UAAU,UAAU;AACxD,QAAI,SAAS;AACX,YAAM,MACJ,YAAY,QAAQ,WAAW,IAC3B,yBAAyB,YAAY,cAAc,SAAS,KAC5D;AACN;AAAA,QACE;AAAA,QACA,aAAAA,QAAM;AAAA,UACJ,GACE,QAAQ,SACJ,qCACA,uBACN,KAAK,GAAG;AAAA,QACV;AAAA,MACF;AAAA,IACF;AACA;AAAA,EACF;AAEA,MAAI,SAAS;AACX;AAAA,MACE;AAAA,MACA,aAAAA,QAAM;AAAA,QACJ,iBACE,QAAQ,SAAS,UAAU,MAC7B;AAAA,MACF;AAAA,IACF;AACA,mCAAW,KAAK,UAAU;AAAA,EAC5B;AAEA,MAAI,QAAQ,QAAQ;AAClB;AAAA,EACF;AAIA,QAAM,iBAAiB,YAAY,IAAI;AACvC,QAAM,SAAS;AAAA,IACb,YAAY,mBAAmB,qBAAqB;AAAA,IACpD,SAAS,uBAAuB,oBAAoB;AAAA,IACpD,aAAa,uBAAuB,wBAAwB;AAAA,IAC5D,WAAW,iBAAiB,wBAAwB;AAAA,IACpD,kBAAkB,iBAAiB,qBAAqB;AAAA,IACxD,iBAAiB;AAAA,EACnB;AACA,YAAM,0BAAW,KAAK,aAAa;AAAA,IACjC,UAAU,QAAQ;AAAA,IAClB,KAAK,QAAQ;AAAA,IACb,gBAAgB,QAAQ;AAAA,IACxB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACH;", "names": ["chalk"]}
{"version": 3, "sources": ["../../../../src/cli/lib/init.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { Context, logFinishedStep, logMessage } from \"../../bundler/context.js\";\nimport { DeploymentType } from \"./api.js\";\nimport { writeConvexUrlToEnvFile } from \"./envvars.js\";\nimport { getDashboardUrl } from \"./dashboard.js\";\n\nexport async function finalizeConfiguration(\n  ctx: Context,\n  options: {\n    functionsPath: string;\n    deploymentType: DeploymentType;\n    deploymentName: string;\n    url: string;\n    wroteToGitIgnore: boolean;\n    changedDeploymentEnvVar: boolean;\n  },\n) {\n  const envVarWrite = await writeConvexUrlToEnvFile(ctx, options.url);\n  if (envVarWrite !== null) {\n    logFinishedStep(\n      ctx,\n      `${messageForDeploymentType(options.deploymentType, options.url)} and saved its:\\n` +\n        `    name as CONVEX_DEPLOYMENT to .env.local\\n` +\n        `    URL as ${envVarWrite.envVar} to ${envVarWrite.envFile}`,\n    );\n  } else if (options.changedDeploymentEnvVar) {\n    logFinishedStep(\n      ctx,\n      `${messageForDeploymentType(options.deploymentType, options.url)} and saved its name as CONVEX_DEPLOYMENT to .env.local`,\n    );\n  }\n  if (options.wroteToGitIgnore) {\n    logMessage(ctx, chalk.gray(`  Added \".env.local\" to .gitignore`));\n  }\n  if (options.deploymentType === \"anonymous\") {\n    logMessage(\n      ctx,\n      `Run \\`npx convex login\\` at any time to create an account and link this deployment.`,\n    );\n  }\n\n  const anyChanges =\n    options.wroteToGitIgnore ||\n    options.changedDeploymentEnvVar ||\n    envVarWrite !== null;\n  if (anyChanges) {\n    const dashboardUrl = getDashboardUrl(ctx, {\n      deploymentName: options.deploymentName,\n      deploymentType: options.deploymentType,\n    });\n    logMessage(\n      ctx,\n      `\\nWrite your Convex functions in ${chalk.bold(options.functionsPath)}\\n` +\n        \"Give us feedback at https://convex.dev/<NAME_EMAIL>\\n\" +\n        `View the Convex dashboard at ${dashboardUrl}\\n`,\n    );\n  }\n}\n\nfunction messageForDeploymentType(deploymentType: DeploymentType, url: string) {\n  switch (deploymentType) {\n    case \"anonymous\":\n      return `Started running a deployment locally at ${url}`;\n    case \"local\":\n      return `Started running a deployment locally at ${url}`;\n    case \"dev\":\n    case \"prod\":\n    case \"preview\":\n      return `Provisioned a ${deploymentType} deployment`;\n    default: {\n      const _exhaustiveCheck: never = deploymentType;\n      return `Provisioned a ${deploymentType as any} deployment`;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,qBAAqD;AAErD,qBAAwC;AACxC,uBAAgC;AAEhC,eAAsB,sBACpB,KACA,SAQA;AACA,QAAM,cAAc,UAAM,wCAAwB,KAAK,QAAQ,GAAG;AAClE,MAAI,gBAAgB,MAAM;AACxB;AAAA,MACE;AAAA,MACA,GAAG,yBAAyB,QAAQ,gBAAgB,QAAQ,GAAG,CAAC;AAAA;AAAA,aAEhD,YAAY,MAAM,OAAO,YAAY,OAAO;AAAA,IAC9D;AAAA,EACF,WAAW,QAAQ,yBAAyB;AAC1C;AAAA,MACE;AAAA,MACA,GAAG,yBAAyB,QAAQ,gBAAgB,QAAQ,GAAG,CAAC;AAAA,IAClE;AAAA,EACF;AACA,MAAI,QAAQ,kBAAkB;AAC5B,mCAAW,KAAK,aAAAA,QAAM,KAAK,oCAAoC,CAAC;AAAA,EAClE;AACA,MAAI,QAAQ,mBAAmB,aAAa;AAC1C;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aACJ,QAAQ,oBACR,QAAQ,2BACR,gBAAgB;AAClB,MAAI,YAAY;AACd,UAAM,mBAAe,kCAAgB,KAAK;AAAA,MACxC,gBAAgB,QAAQ;AAAA,MACxB,gBAAgB,QAAQ;AAAA,IAC1B,CAAC;AACD;AAAA,MACE;AAAA,MACA;AAAA,iCAAoC,aAAAA,QAAM,KAAK,QAAQ,aAAa,CAAC;AAAA;AAAA,+BAEnC,YAAY;AAAA;AAAA,IAChD;AAAA,EACF;AACF;AAEA,SAAS,yBAAyB,gBAAgC,KAAa;AAC7E,UAAQ,gBAAgB;AAAA,IACtB,KAAK;AACH,aAAO,2CAA2C,GAAG;AAAA,IACvD,KAAK;AACH,aAAO,2CAA2C,GAAG;AAAA,IACvD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,iBAAiB,cAAc;AAAA,IACxC,SAAS;AACP,YAAM,mBAA0B;AAChC,aAAO,iBAAiB,cAAqB;AAAA,IAC/C;AAAA,EACF;AACF;", "names": ["chalk"]}
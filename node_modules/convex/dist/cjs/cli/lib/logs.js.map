{"version": 3, "sources": ["../../../../src/cli/lib/logs.ts"], "sourcesContent": ["import {\n  Context,\n  logMessage,\n  logOutput,\n  logWarning,\n} from \"../../bundler/context.js\";\nimport { nextBackoff } from \"./dev.js\";\nimport chalk from \"chalk\";\nimport { deploymentFetch } from \"./utils/utils.js\";\n\nexport type LogMode = \"always\" | \"pause-on-deploy\" | \"disable\";\n\nexport class LogManager {\n  private paused: boolean = false;\n\n  constructor(private mode: LogMode) {}\n\n  async waitForUnpaused() {\n    while (this.paused) {\n      await new Promise((resolve) => setTimeout(resolve, 100));\n    }\n  }\n\n  beginDeploy() {\n    if (this.mode === \"pause-on-deploy\") {\n      this.paused = true;\n    }\n  }\n\n  endDeploy() {\n    if (this.mode === \"pause-on-deploy\") {\n      this.paused = false;\n    }\n  }\n}\n\nconst MAX_UDF_STREAM_FAILURE_COUNT = 5;\n\ntype LogDestination = \"stdout\" | \"stderr\";\n\nexport async function logsForDeployment(\n  ctx: Context,\n  credentials: {\n    url: string;\n    adminKey: string;\n  },\n  options: {\n    success: boolean;\n    history: number;\n    deploymentNotice: string;\n  },\n) {\n  logMessage(ctx, chalk.yellow(`Watching logs${options.deploymentNotice}...`));\n  await watchLogs(ctx, credentials.url, credentials.adminKey, \"stdout\", {\n    history: options.history,\n    success: options.success,\n  });\n}\n\nexport async function watchLogs(\n  ctx: Context,\n  url: string,\n  adminKey: string,\n  dest: LogDestination,\n  options?: {\n    success: boolean;\n    history?: number | boolean;\n    logManager?: LogManager;\n  },\n) {\n  let numFailures = 0;\n  let isFirst = true;\n  let cursorMs = 0;\n\n  for (;;) {\n    try {\n      const { entries, newCursor } = await pollUdfLog(\n        ctx,\n        cursorMs,\n        url,\n        adminKey,\n      );\n      cursorMs = newCursor;\n      numFailures = 0;\n\n      // Delay printing logs until the log manager is unpaused.\n      await options?.logManager?.waitForUnpaused();\n\n      // The first execution, we just want to fetch the current head cursor so we don't send stale\n      // logs to the client.\n      if (isFirst) {\n        isFirst = false;\n        if (\n          options?.history === true ||\n          (typeof options?.history === \"number\" && options?.history > 0)\n        ) {\n          const entriesSlice =\n            options?.history === true\n              ? entries\n              : entries.slice(entries.length - options?.history);\n          processLogs(ctx, entriesSlice, dest, options?.success);\n        }\n      } else {\n        processLogs(ctx, entries, dest, options?.success === true);\n      }\n    } catch {\n      numFailures += 1;\n    }\n    // Handle backoff\n    if (numFailures > 0) {\n      const backoff = nextBackoff(numFailures);\n\n      // If we exceed a threshold number of failures, warn the user and display backoff.\n      if (numFailures > MAX_UDF_STREAM_FAILURE_COUNT) {\n        logWarning(\n          ctx,\n          `Convex [WARN] Failed to fetch logs. Waiting ${backoff}ms before next retry.`,\n        );\n      }\n      await new Promise((resolve) => {\n        setTimeout(() => resolve(null), backoff);\n      });\n    }\n  }\n}\n\ntype UdfType = \"Query\" | \"Mutation\" | \"Action\" | \"HttpAction\";\n\ntype StructuredLogLine = {\n  messages: string[];\n  level: \"LOG\" | \"DEBUG\" | \"INFO\" | \"WARN\" | \"ERROR\";\n  timestamp: number;\n  isTruncated: boolean;\n};\ntype LogLine = string | StructuredLogLine;\n\ntype UdfExecutionResponse = {\n  identifier: string;\n  udfType: UdfType;\n  logLines: LogLine[];\n  // Unix timestamp (in seconds)\n  timestamp: number;\n  // UDF execution duration (in seconds)\n  executionTime: number;\n  error: string | null;\n  kind: \"Completion\" | \"Progress\";\n};\n\nasync function pollUdfLog(\n  ctx: Context,\n  cursor: number,\n  url: string,\n  adminKey: string,\n): Promise<{ entries: UdfExecutionResponse[]; newCursor: number }> {\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: url,\n    adminKey,\n  });\n  const response = await fetch(`/api/stream_function_logs?cursor=${cursor}`, {\n    method: \"GET\",\n  });\n  return await response.json();\n}\n\nconst prefixForSource = (udfType: UdfType): string => {\n  return udfType.charAt(0);\n};\n\nfunction processLogs(\n  ctx: Context,\n  rawLogs: UdfExecutionResponse[],\n  dest: LogDestination,\n  shouldShowSuccessLogs: boolean,\n) {\n  for (let i = 0; i < rawLogs.length; i++) {\n    const log = rawLogs[i];\n    if (log.logLines) {\n      const id = log.identifier;\n      const udfType = log.udfType;\n      const timestampMs = log.timestamp * 1000;\n      const executionTimeMs = log.executionTime * 1000;\n\n      for (let j = 0; j < log.logLines.length; j++) {\n        logToTerminal(\n          ctx,\n          \"info\",\n          timestampMs,\n          udfType,\n          id,\n          log.logLines[j],\n          dest,\n        );\n      }\n\n      if (log.error) {\n        logToTerminal(ctx, \"error\", timestampMs, udfType, id, log.error!, dest);\n      } else if (log.kind === \"Completion\" && shouldShowSuccessLogs) {\n        logFunctionExecution(\n          ctx,\n          timestampMs,\n          log.udfType,\n          id,\n          executionTimeMs,\n          dest,\n        );\n      }\n    }\n  }\n}\n\nfunction logFunctionExecution(\n  ctx: Context,\n  timestampMs: number,\n  udfType: UdfType,\n  udfPath: string,\n  executionTimeMs: number,\n  dest: LogDestination,\n) {\n  logToDestination(\n    ctx,\n    dest,\n    chalk.green(\n      `${prefixLog(\n        timestampMs,\n        udfType,\n        udfPath,\n      )} Function executed in ${Math.ceil(executionTimeMs)} ms`,\n    ),\n  );\n}\n\nfunction logToTerminal(\n  ctx: Context,\n  type: \"info\" | \"error\",\n  timestampMs: number,\n  udfType: UdfType,\n  udfPath: string,\n  message: LogLine,\n  dest: LogDestination,\n) {\n  const prefix = prefixForSource(udfType);\n  if (typeof message === \"string\") {\n    if (type === \"info\") {\n      const match = message.match(/^\\[.*?\\] /);\n      if (match === null) {\n        logToDestination(\n          ctx,\n          dest,\n          chalk.red(\n            `[CONVEX ${prefix}(${udfPath})] Could not parse console.log`,\n          ),\n        );\n        return;\n      }\n      const level = message.slice(1, match[0].length - 2);\n      const args = message.slice(match[0].length);\n\n      logToDestination(\n        ctx,\n        dest,\n        chalk.cyan(`${prefixLog(timestampMs, udfType, udfPath)} [${level}]`),\n        args,\n      );\n    } else {\n      logToDestination(\n        ctx,\n        dest,\n        chalk.red(`${prefixLog(timestampMs, udfType, udfPath)} ${message}`),\n      );\n    }\n  } else {\n    const level = message.level;\n    const formattedMessage = `${message.messages.join(\" \")}${message.isTruncated ? \" (truncated due to length)\" : \"\"}`;\n    logToDestination(\n      ctx,\n      dest,\n      chalk.cyan(\n        // timestamp is in ms since epoch\n        `${prefixLog(message.timestamp, udfType, udfPath)} [${level}]`,\n      ),\n      formattedMessage,\n    );\n  }\n}\n\nfunction logToDestination(ctx: Context, dest: LogDestination, ...logged: any) {\n  switch (dest) {\n    case \"stdout\":\n      logOutput(ctx, ...logged);\n      break;\n    case \"stderr\":\n      logMessage(ctx, ...logged);\n      break;\n  }\n}\n\nfunction prefixLog(timestampMs: number, udfType: UdfType, udfPath: string) {\n  const prefix = prefixForSource(udfType);\n  const localizedTimestamp = new Date(timestampMs).toLocaleString();\n\n  return `${localizedTimestamp} [CONVEX ${prefix}(${udfPath})]`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAKO;AACP,iBAA4B;AAC5B,mBAAkB;AAClB,mBAAgC;AAIzB,MAAM,WAAW;AAAA,EAGtB,YAAoB,MAAe;AAAf;AAFpB,wBAAQ,UAAkB;AAAA,EAEU;AAAA,EAEpC,MAAM,kBAAkB;AACtB,WAAO,KAAK,QAAQ;AAClB,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC;AAAA,IACzD;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,SAAS,mBAAmB;AACnC,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,YAAY;AACV,QAAI,KAAK,SAAS,mBAAmB;AACnC,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AACF;AAEA,MAAM,+BAA+B;AAIrC,eAAsB,kBACpB,KACA,aAIA,SAKA;AACA,iCAAW,KAAK,aAAAA,QAAM,OAAO,gBAAgB,QAAQ,gBAAgB,KAAK,CAAC;AAC3E,QAAM,UAAU,KAAK,YAAY,KAAK,YAAY,UAAU,UAAU;AAAA,IACpE,SAAS,QAAQ;AAAA,IACjB,SAAS,QAAQ;AAAA,EACnB,CAAC;AACH;AAEA,eAAsB,UACpB,KACA,KACA,UACA,MACA,SAKA;AACA,MAAI,cAAc;AAClB,MAAI,UAAU;AACd,MAAI,WAAW;AAEf,aAAS;AACP,QAAI;AACF,YAAM,EAAE,SAAS,UAAU,IAAI,MAAM;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,iBAAW;AACX,oBAAc;AAGd,YAAM,SAAS,YAAY,gBAAgB;AAI3C,UAAI,SAAS;AACX,kBAAU;AACV,YACE,SAAS,YAAY,QACpB,OAAO,SAAS,YAAY,YAAY,SAAS,UAAU,GAC5D;AACA,gBAAM,eACJ,SAAS,YAAY,OACjB,UACA,QAAQ,MAAM,QAAQ,SAAS,SAAS,OAAO;AACrD,sBAAY,KAAK,cAAc,MAAM,SAAS,OAAO;AAAA,QACvD;AAAA,MACF,OAAO;AACL,oBAAY,KAAK,SAAS,MAAM,SAAS,YAAY,IAAI;AAAA,MAC3D;AAAA,IACF,QAAQ;AACN,qBAAe;AAAA,IACjB;AAEA,QAAI,cAAc,GAAG;AACnB,YAAM,cAAU,wBAAY,WAAW;AAGvC,UAAI,cAAc,8BAA8B;AAC9C;AAAA,UACE;AAAA,UACA,+CAA+C,OAAO;AAAA,QACxD;AAAA,MACF;AACA,YAAM,IAAI,QAAQ,CAAC,YAAY;AAC7B,mBAAW,MAAM,QAAQ,IAAI,GAAG,OAAO;AAAA,MACzC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAwBA,eAAe,WACb,KACA,QACA,KACA,UACiE;AACjE,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC,eAAe;AAAA,IACf;AAAA,EACF,CAAC;AACD,QAAM,WAAW,MAAM,MAAM,oCAAoC,MAAM,IAAI;AAAA,IACzE,QAAQ;AAAA,EACV,CAAC;AACD,SAAO,MAAM,SAAS,KAAK;AAC7B;AAEA,MAAM,kBAAkB,CAAC,YAA6B;AACpD,SAAO,QAAQ,OAAO,CAAC;AACzB;AAEA,SAAS,YACP,KACA,SACA,MACA,uBACA;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,MAAM,QAAQ,CAAC;AACrB,QAAI,IAAI,UAAU;AAChB,YAAM,KAAK,IAAI;AACf,YAAM,UAAU,IAAI;AACpB,YAAM,cAAc,IAAI,YAAY;AACpC,YAAM,kBAAkB,IAAI,gBAAgB;AAE5C,eAAS,IAAI,GAAG,IAAI,IAAI,SAAS,QAAQ,KAAK;AAC5C;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,IAAI,SAAS,CAAC;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAEA,UAAI,IAAI,OAAO;AACb,sBAAc,KAAK,SAAS,aAAa,SAAS,IAAI,IAAI,OAAQ,IAAI;AAAA,MACxE,WAAW,IAAI,SAAS,gBAAgB,uBAAuB;AAC7D;AAAA,UACE;AAAA,UACA;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,qBACP,KACA,aACA,SACA,SACA,iBACA,MACA;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,aAAAA,QAAM;AAAA,MACJ,GAAG;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC,yBAAyB,KAAK,KAAK,eAAe,CAAC;AAAA,IACtD;AAAA,EACF;AACF;AAEA,SAAS,cACP,KACA,MACA,aACA,SACA,SACA,SACA,MACA;AACA,QAAM,SAAS,gBAAgB,OAAO;AACtC,MAAI,OAAO,YAAY,UAAU;AAC/B,QAAI,SAAS,QAAQ;AACnB,YAAM,QAAQ,QAAQ,MAAM,WAAW;AACvC,UAAI,UAAU,MAAM;AAClB;AAAA,UACE;AAAA,UACA;AAAA,UACA,aAAAA,QAAM;AAAA,YACJ,WAAW,MAAM,IAAI,OAAO;AAAA,UAC9B;AAAA,QACF;AACA;AAAA,MACF;AACA,YAAM,QAAQ,QAAQ,MAAM,GAAG,MAAM,CAAC,EAAE,SAAS,CAAC;AAClD,YAAM,OAAO,QAAQ,MAAM,MAAM,CAAC,EAAE,MAAM;AAE1C;AAAA,QACE;AAAA,QACA;AAAA,QACA,aAAAA,QAAM,KAAK,GAAG,UAAU,aAAa,SAAS,OAAO,CAAC,KAAK,KAAK,GAAG;AAAA,QACnE;AAAA,MACF;AAAA,IACF,OAAO;AACL;AAAA,QACE;AAAA,QACA;AAAA,QACA,aAAAA,QAAM,IAAI,GAAG,UAAU,aAAa,SAAS,OAAO,CAAC,IAAI,OAAO,EAAE;AAAA,MACpE;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,QAAQ,QAAQ;AACtB,UAAM,mBAAmB,GAAG,QAAQ,SAAS,KAAK,GAAG,CAAC,GAAG,QAAQ,cAAc,+BAA+B,EAAE;AAChH;AAAA,MACE;AAAA,MACA;AAAA,MACA,aAAAA,QAAM;AAAA;AAAA,QAEJ,GAAG,UAAU,QAAQ,WAAW,SAAS,OAAO,CAAC,KAAK,KAAK;AAAA,MAC7D;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,KAAc,SAAyB,QAAa;AAC5E,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,oCAAU,KAAK,GAAG,MAAM;AACxB;AAAA,IACF,KAAK;AACH,qCAAW,KAAK,GAAG,MAAM;AACzB;AAAA,EACJ;AACF;AAEA,SAAS,UAAU,aAAqB,SAAkB,SAAiB;AACzE,QAAM,SAAS,gBAAgB,OAAO;AACtC,QAAM,qBAAqB,IAAI,KAAK,WAAW,EAAE,eAAe;AAEhE,SAAO,GAAG,kBAAkB,YAAY,MAAM,IAAI,OAAO;AAC3D;", "names": ["chalk"]}
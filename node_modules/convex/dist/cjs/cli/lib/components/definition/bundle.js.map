{"version": 3, "sources": ["../../../../../../src/cli/lib/components/definition/bundle.ts"], "sourcesContent": ["import path from \"path\";\nimport {\n  ComponentDirectory,\n  ComponentDefinitionPath,\n  buildComponentDirectory,\n  isComponentDirectory,\n  qualifiedDefinitionPath,\n  toComponentDefinitionPath,\n} from \"./directoryStructure.js\";\nimport {\n  Context,\n  logMessage,\n  logWarning,\n  showSpinner,\n} from \"../../../../bundler/context.js\";\nimport esbuild, { BuildOptions, Metafile, OutputFile, Plugin } from \"esbuild\";\nimport chalk from \"chalk\";\nimport {\n  AppDefinitionSpecWithoutImpls,\n  ComponentDefinitionSpecWithoutImpls,\n} from \"../../config.js\";\nimport {\n  Bundle,\n  bundle,\n  bundleAuthConfig,\n  bundleSchema,\n  entryPointsByEnvironment,\n} from \"../../../../bundler/index.js\";\nimport { NodeDependency } from \"../../deployApi/modules.js\";\n\n/**\n * An esbuild plugin to mark component definitions external or return a list of\n * all component definitions.\n *\n * By default this plugin runs in \"bundle\" mode and marks all imported component\n * definition files as external, not traversing further.\n *\n * If \"discover\" mode is specified it traverses the entire tree.\n */\nfunction componentPlugin({\n  mode = \"bundle\",\n  rootComponentDirectory,\n  verbose,\n  ctx,\n}: {\n  mode: \"discover\" | \"bundle\";\n  rootComponentDirectory: ComponentDirectory;\n  verbose?: boolean;\n  ctx: Context;\n}): Plugin {\n  const components = new Map<string, ComponentDirectory>();\n  return {\n    name: `convex-${mode === \"discover\" ? \"discover-components\" : \"bundle-components\"}`,\n    async setup(build) {\n      // This regex can't be really precise since developers could import\n      // \"convex.config\", \"convex.config.js\", \"convex.config.ts\", etc.\n      build.onResolve({ filter: /.*convex.config.*/ }, async (args) => {\n        verbose && logMessage(ctx, \"esbuild resolving import:\", args);\n        if (args.namespace !== \"file\") {\n          verbose && logMessage(ctx, \"  Not a file.\");\n          return;\n        }\n        if (args.kind === \"entry-point\") {\n          verbose && logMessage(ctx, \"  -> Top-level entry-point.\");\n          const componentDirectory = await buildComponentDirectory(\n            ctx,\n            path.resolve(args.path),\n          );\n\n          // No attempt to resolve args.path is made for entry points so they\n          // must be relative or absolute file paths, not npm packages.\n          // Whether we're bundling or discovering, we're done.\n          if (components.get(args.path)) {\n            // We always invoke esbuild in a try/catch.\n            // eslint-disable-next-line no-restricted-syntax\n            throw new Error(\n              `Entry point component \"${args.path}\" already registered.`,\n            );\n          }\n          components.set(args.path, componentDirectory);\n          return;\n        }\n\n        const candidates = [args.path];\n        const ext = path.extname(args.path);\n        if (ext === \".js\") {\n          candidates.push(args.path.slice(0, -\".js\".length) + \".ts\");\n        }\n        if (ext !== \".js\" && ext !== \".ts\") {\n          candidates.push(args.path + \".js\");\n          candidates.push(args.path + \".ts\");\n        }\n        let resolvedPath = undefined;\n        for (const candidate of candidates) {\n          const result = await build.resolve(candidate, {\n            // We expect this to be \"import-statement\" but pass 'kind' through\n            // to say honest to normal esbuild behavior.\n            kind: args.kind,\n            resolveDir: args.resolveDir,\n          });\n          if (result.path) {\n            resolvedPath = result.path;\n            break;\n          }\n        }\n        if (resolvedPath === undefined) {\n          verbose && logMessage(ctx, `  -> ${args.path} not found.`);\n          return;\n        }\n\n        const parentDir = path.dirname(resolvedPath);\n        let imported = components.get(resolvedPath);\n        if (!imported) {\n          const isComponent = isComponentDirectory(ctx, parentDir, false);\n          if (isComponent.kind !== \"ok\") {\n            verbose && logMessage(ctx, \"  -> Not a component:\", isComponent);\n            return;\n          }\n          imported = isComponent.component;\n          components.set(resolvedPath, imported);\n        }\n\n        verbose &&\n          logMessage(\n            ctx,\n            \"  -> Component import! Recording it.\",\n            args.path,\n            resolvedPath,\n          );\n\n        if (mode === \"discover\") {\n          return {\n            path: resolvedPath,\n          };\n        } else {\n          // In bundle mode, transform external imports to use componentPaths:\n          // import rateLimiter from \"convex_ratelimiter\";\n          // => import rateLimiter from `_componentDeps/${base64('../node_modules/convex_ratelimiter')}`;\n\n          // A componentPath is path from the root component to the directory\n          // of the this component's definition file.\n          const componentPath = toComponentDefinitionPath(\n            rootComponentDirectory,\n            imported,\n          );\n          const importPath = definitionImportPath(componentPath);\n          return {\n            path: importPath,\n            external: true,\n          };\n        }\n      });\n    },\n  };\n}\n\n/** The path on the deployment that identifier a component definition. */\nfunction definitionImportPath(componentPath: ComponentDefinitionPath): string {\n  return `./_componentDeps/${Buffer.from(componentPath).toString(\"base64url\")}`;\n}\n\n// Share configuration between the component definition discovery and bundling passes.\nfunction sharedEsbuildOptions({\n  liveComponentSources = false,\n}: {\n  liveComponentSources?: boolean;\n}) {\n  const options = {\n    bundle: true,\n    platform: \"browser\",\n    format: \"esm\",\n    target: \"esnext\",\n\n    conditions: [\"convex\", \"module\"] as string[],\n\n    // `false` is the default for splitting. It's simpler to evaluate these on\n    // the server as a single file.\n    // Splitting could be enabled for speed once the server supports it.\n    splitting: false,\n\n    // place output files in memory at their source locations\n    write: false,\n    outdir: path.parse(process.cwd()).root,\n    outbase: path.parse(process.cwd()).root,\n\n    minify: true, // Note that this implies NODE_ENV=\"production\".\n    keepNames: true,\n\n    metafile: true,\n  } as const satisfies BuildOptions;\n\n  // Link directly to component sources (usually .ts) in order to\n  // skip the build step. This also causes codegen to run for components\n  // loaded from npm packages.\n  if (liveComponentSources) {\n    options.conditions.push(\"@convex-dev/component-source\");\n  }\n  return options;\n}\n\n// Use the esbuild metafile to discover the dependency graph in which component\n// definitions are nodes.\nexport async function componentGraph(\n  ctx: Context,\n  absWorkingDir: string,\n  rootComponentDirectory: ComponentDirectory,\n  liveComponentSources: boolean,\n  verbose: boolean = true,\n): Promise<{\n  components: Map<string, ComponentDirectory>;\n  dependencyGraph: [ComponentDirectory, ComponentDirectory][];\n}> {\n  let result;\n  try {\n    result = await esbuild.build({\n      absWorkingDir, // This is mostly useful for formatting error messages.\n      entryPoints: [qualifiedDefinitionPath(rootComponentDirectory)],\n      plugins: [\n        componentPlugin({\n          ctx,\n          mode: \"discover\",\n          verbose,\n          rootComponentDirectory,\n        }),\n      ],\n      sourcemap: \"external\",\n      sourcesContent: false,\n\n      ...sharedEsbuildOptions({ liveComponentSources }),\n    });\n    await registerEsbuildReads(ctx, absWorkingDir, result.metafile);\n  } catch (err: any) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `esbuild failed: ${err}`,\n    });\n  }\n\n  if (result.errors.length) {\n    const message = result.errors.map((error) => error.text).join(\"\\n\");\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: message,\n    });\n  }\n  for (const warning of result.warnings) {\n    // eslint-disable-next-line no-console\n    console.log(chalk.yellow(`esbuild warning: ${warning.text}`));\n  }\n  return await findComponentDependencies(ctx, result.metafile);\n}\n\n/**\n * Get dependencies of a ComponenDirectory as ComponentPaths.\n *\n * Component paths are paths relative to the root component.\n */\nexport function getDeps(\n  rootComponent: ComponentDirectory,\n  dependencyGraph: [ComponentDirectory, ComponentDirectory][],\n  definitionPath: string,\n): ComponentDefinitionPath[] {\n  return dependencyGraph\n    .filter(\n      ([importer, _imported]) => importer.definitionPath === definitionPath,\n    )\n    .map(([_importer, imported]) =>\n      toComponentDefinitionPath(rootComponent, imported),\n    );\n}\n\n/**\n * The returned dependency graph is an array of tuples of [importer, imported]\n *\n * This doesn't work on just any esbuild metafile because it assumes input\n * imports have not been transformed. We run it on the metafile produced by\n * the esbuild invocation that uses the component plugin in \"discover\" mode.\n */\nasync function findComponentDependencies(\n  ctx: Context,\n  metafile: Metafile,\n): Promise<{\n  components: Map<string, ComponentDirectory>;\n  dependencyGraph: [ComponentDirectory, ComponentDirectory][];\n}> {\n  const { inputs } = metafile;\n  // This filter means we only supports *direct imports* of component definitions\n  // from other component definitions.\n  const componentInputs = Object.keys(inputs).filter((path) =>\n    path.includes(\".config.\"),\n  );\n\n  // Absolute path doesn't appear to be necessary here since only inputs marked\n  // external get transformed to an absolute path but it's not clear what's an\n  // esbuild implementation detail in the metafile or which settings change this.\n  const componentsByAbsPath = new Map<string, ComponentDirectory>();\n  for (const inputPath of componentInputs) {\n    const importer = await buildComponentDirectory(ctx, inputPath);\n    componentsByAbsPath.set(path.resolve(inputPath), importer);\n  }\n  const dependencyGraph: [ComponentDirectory, ComponentDirectory][] = [];\n  for (const inputPath of componentInputs) {\n    const importer = componentsByAbsPath.get(path.resolve(inputPath))!;\n    const { imports } = inputs[inputPath];\n    const componentImports = imports.filter((imp) =>\n      imp.path.includes(\".config.\"),\n    );\n    for (const importPath of componentImports.map((dep) => dep.path)) {\n      const imported = componentsByAbsPath.get(path.resolve(importPath));\n      if (!imported) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"invalid filesystem data\",\n          printedMessage: `Didn't find ${path.resolve(importPath)} in ${[...componentsByAbsPath.keys()].toString()}`,\n        });\n      }\n      dependencyGraph.push([importer, imported]);\n    }\n  }\n\n  const components = new Map<string, ComponentDirectory>();\n  for (const directory of componentsByAbsPath.values()) {\n    components.set(directory.path, directory);\n  }\n  return { components, dependencyGraph };\n}\n\n// NB: If a directory linked to is not a member of the passed\n// componentDirectories array then there will be external links\n// with no corresponding definition bundle.\n// That could be made to throw an error but maybe those are already available\n// on the Convex definition filesystem somehow, e.g. builtin components.\n/** Bundle the component definitions listed. */\nexport async function bundleDefinitions(\n  ctx: Context,\n  absWorkingDir: string,\n  dependencyGraph: [ComponentDirectory, ComponentDirectory][],\n  rootComponentDirectory: ComponentDirectory,\n  componentDirectories: ComponentDirectory[],\n  liveComponentSources: boolean,\n  verbose: boolean = false,\n): Promise<{\n  appDefinitionSpecWithoutImpls: AppDefinitionSpecWithoutImpls;\n  componentDefinitionSpecsWithoutImpls: ComponentDefinitionSpecWithoutImpls[];\n}> {\n  let result;\n  try {\n    result = await esbuild.build({\n      absWorkingDir,\n      entryPoints: componentDirectories.map((dir) =>\n        qualifiedDefinitionPath(dir),\n      ),\n      plugins: [\n        componentPlugin({\n          ctx,\n          mode: \"bundle\",\n          verbose,\n          rootComponentDirectory,\n        }),\n      ],\n      sourcemap: true,\n      ...sharedEsbuildOptions({ liveComponentSources }),\n    });\n    await registerEsbuildReads(ctx, absWorkingDir, result.metafile);\n  } catch (err: any) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `esbuild failed: ${err}`,\n    });\n  }\n\n  if (result.errors.length) {\n    const message = result.errors.map((error) => error.text).join(\"\\n\");\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: message,\n    });\n  }\n  for (const warning of result.warnings) {\n    // eslint-disable-next-line no-console\n    console.log(chalk.yellow(`esbuild warning: ${warning.text}`));\n  }\n\n  const outputs: {\n    outputJs: OutputFile;\n    outputJsMap?: OutputFile;\n    directory: ComponentDirectory;\n  }[] = [];\n  for (const directory of componentDirectories) {\n    const absInput = path.resolve(absWorkingDir, directory.definitionPath);\n    const expectedOutputJs =\n      absInput.slice(0, absInput.lastIndexOf(\".\")) + \".js\";\n    const expectedOutputMap =\n      absInput.slice(0, absInput.lastIndexOf(\".\")) + \".js.map\";\n    const outputJs = result.outputFiles.filter(\n      (outputFile) => outputFile.path === expectedOutputJs,\n    )[0];\n    if (!outputJs) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `no JS found matching ${expectedOutputJs} in ${result.outputFiles.map((x) => x.path).toString()}`,\n      });\n    }\n    const outputJsMap = result.outputFiles.filter(\n      (outputFile) => outputFile.path === expectedOutputMap,\n    )[0];\n    outputs.push({\n      outputJs,\n      outputJsMap,\n      directory,\n    });\n  }\n\n  const appBundles = outputs.filter(\n    (out) => out.directory.path === rootComponentDirectory.path,\n  );\n  if (appBundles.length !== 1) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"found wrong number of app bundles\",\n    });\n  }\n  const appBundle = appBundles[0];\n  const componentBundles = outputs.filter(\n    (out) => out.directory.path !== rootComponentDirectory.path,\n  );\n\n  const componentDefinitionSpecsWithoutImpls: ComponentDefinitionSpecWithoutImpls[] =\n    componentBundles.map(({ directory, outputJs, outputJsMap }) => ({\n      definitionPath: toComponentDefinitionPath(\n        rootComponentDirectory,\n        directory,\n      ),\n      definition: {\n        path: path.relative(directory.path, outputJs.path),\n        source: outputJs.text,\n        sourceMap: outputJsMap?.text,\n        environment: \"isolate\" as const,\n      },\n      dependencies: getDeps(\n        rootComponentDirectory,\n        dependencyGraph,\n        directory.definitionPath,\n      ),\n    }));\n  const appDeps = getDeps(\n    rootComponentDirectory,\n    dependencyGraph,\n    appBundle.directory.definitionPath,\n  );\n  const appDefinitionSpecWithoutImpls: AppDefinitionSpecWithoutImpls = {\n    definition: {\n      path: path.relative(rootComponentDirectory.path, appBundle.outputJs.path),\n      source: appBundle.outputJs.text,\n      sourceMap: appBundle.outputJsMap?.text,\n      environment: \"isolate\" as const,\n    },\n    dependencies: appDeps,\n  };\n  return {\n    appDefinitionSpecWithoutImpls,\n    componentDefinitionSpecsWithoutImpls,\n  };\n}\n\nexport async function bundleImplementations(\n  ctx: Context,\n  rootComponentDirectory: ComponentDirectory,\n  componentDirectories: ComponentDirectory[],\n  nodeExternalPackages: string[],\n  extraConditions: string[],\n  verbose: boolean = false,\n): Promise<{\n  appImplementation: {\n    schema: Bundle | null;\n    functions: Bundle[];\n    externalNodeDependencies: NodeDependency[];\n  };\n  componentImplementations: {\n    schema: Bundle | null;\n    functions: Bundle[];\n    definitionPath: ComponentDefinitionPath;\n  }[];\n}> {\n  let appImplementation;\n  const componentImplementations = [];\n\n  let isRoot = true;\n  for (const directory of [rootComponentDirectory, ...componentDirectories]) {\n    const resolvedPath = path.resolve(\n      rootComponentDirectory.path,\n      directory.path,\n    );\n    let schema;\n    if (ctx.fs.exists(path.resolve(resolvedPath, \"schema.ts\"))) {\n      schema =\n        (await bundleSchema(ctx, resolvedPath, extraConditions))[0] || null;\n    } else if (ctx.fs.exists(path.resolve(resolvedPath, \"schema.js\"))) {\n      schema =\n        (await bundleSchema(ctx, resolvedPath, extraConditions))[0] || null;\n    } else {\n      schema = null;\n    }\n\n    const entryPoints = await entryPointsByEnvironment(ctx, resolvedPath);\n    const convexResult: {\n      modules: Bundle[];\n      externalDependencies: Map<string, string>;\n      bundledModuleNames: Set<string>;\n    } = await bundle(\n      ctx,\n      resolvedPath,\n      entryPoints.isolate,\n      true,\n      \"browser\",\n      undefined,\n      undefined,\n      extraConditions,\n    );\n\n    if (convexResult.externalDependencies.size !== 0) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: \"external dependencies not supported\",\n      });\n    }\n    const functions = convexResult.modules;\n    if (isRoot) {\n      if (verbose) {\n        showSpinner(ctx, \"Bundling modules for Node.js runtime...\");\n      }\n      const nodeResult: {\n        modules: Bundle[];\n        externalDependencies: Map<string, string>;\n        bundledModuleNames: Set<string>;\n      } = await bundle(\n        ctx,\n        resolvedPath,\n        entryPoints.node,\n        true,\n        \"node\",\n        path.join(\"_deps\", \"node\"),\n        nodeExternalPackages,\n        extraConditions,\n      );\n\n      const externalNodeDependencies: NodeDependency[] = [];\n      for (const [\n        moduleName,\n        moduleVersion,\n      ] of nodeResult.externalDependencies) {\n        externalNodeDependencies.push({\n          name: moduleName,\n          version: moduleVersion,\n        });\n      }\n      const authBundle = await bundleAuthConfig(ctx, resolvedPath);\n      appImplementation = {\n        schema,\n        functions: functions.concat(nodeResult.modules).concat(authBundle),\n        externalNodeDependencies,\n      };\n    } else {\n      // Reject push if components have node bundles in non-root directories.\n      if (directory.path !== rootComponentDirectory.path) {\n        const nodeResult: {\n          modules: Bundle[];\n          externalDependencies: Map<string, string>;\n          bundledModuleNames: Set<string>;\n        } = await bundle(\n          ctx,\n          resolvedPath,\n          entryPoints.node,\n          true,\n          \"node\",\n          path.join(\"_deps\", \"node\"),\n          nodeExternalPackages,\n          extraConditions,\n        );\n        if (nodeResult.modules.length > 0) {\n          // TODO(ENG-7116) Remove error and bundle the component node actions when we are ready to support them.\n          await ctx.crash({\n            exitCode: 1,\n            errorType: \"invalid filesystem data\",\n            printedMessage: `\"use node\" directive is not supported in components. Remove it from the component at: ${resolvedPath}.`,\n          });\n        }\n      }\n      // definitionPath is the canonical form\n      const definitionPath = toComponentDefinitionPath(\n        rootComponentDirectory,\n        directory,\n      );\n      componentImplementations.push({ definitionPath, schema, functions });\n    }\n    isRoot = false;\n  }\n\n  if (!appImplementation) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"No app implementation found\",\n    });\n  }\n\n  return { appImplementation, componentImplementations };\n}\n\nasync function registerEsbuildReads(\n  ctx: Context,\n  absWorkingDir: string,\n  metafile: Metafile,\n) {\n  for (const [relPath, input] of Object.entries(metafile.inputs)) {\n    if (\n      // We rewrite these files so this integrity check isn't useful.\n      path.basename(relPath).includes(\"convex.config\") ||\n      // TODO: esbuild outputs paths prefixed with \"(disabled)\" when bundling our internal\n      // udf-system package. The files do actually exist locally, though.\n      relPath.indexOf(\"(disabled):\") !== -1 ||\n      relPath.startsWith(\"wasm-binary:\") ||\n      relPath.startsWith(\"wasm-stub:\")\n    ) {\n      continue;\n    }\n    const absPath = path.resolve(absWorkingDir, relPath);\n    const st = ctx.fs.stat(absPath);\n    if (st.size !== input.bytes) {\n      // Consider this a transient error so we'll try again and hopefully\n      // no files change right after esbuild next time.\n      logWarning(\n        ctx,\n        `Bundled file ${absPath} changed right after esbuild invocation`,\n      );\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"transient\",\n        printedMessage: null,\n      });\n    }\n    ctx.fs.registerPath(absPath, st);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AACjB,gCAOO;AACP,qBAKO;AACP,qBAAoE;AACpE,mBAAkB;AAKlB,qBAMO;AAYP,SAAS,gBAAgB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AACF,GAKW;AACT,QAAM,aAAa,oBAAI,IAAgC;AACvD,SAAO;AAAA,IACL,MAAM,UAAU,SAAS,aAAa,wBAAwB,mBAAmB;AAAA,IACjF,MAAM,MAAM,OAAO;AAGjB,YAAM,UAAU,EAAE,QAAQ,oBAAoB,GAAG,OAAO,SAAS;AAC/D,uBAAW,2BAAW,KAAK,6BAA6B,IAAI;AAC5D,YAAI,KAAK,cAAc,QAAQ;AAC7B,yBAAW,2BAAW,KAAK,eAAe;AAC1C;AAAA,QACF;AACA,YAAI,KAAK,SAAS,eAAe;AAC/B,yBAAW,2BAAW,KAAK,6BAA6B;AACxD,gBAAM,qBAAqB,UAAM;AAAA,YAC/B;AAAA,YACA,YAAAA,QAAK,QAAQ,KAAK,IAAI;AAAA,UACxB;AAKA,cAAI,WAAW,IAAI,KAAK,IAAI,GAAG;AAG7B,kBAAM,IAAI;AAAA,cACR,0BAA0B,KAAK,IAAI;AAAA,YACrC;AAAA,UACF;AACA,qBAAW,IAAI,KAAK,MAAM,kBAAkB;AAC5C;AAAA,QACF;AAEA,cAAM,aAAa,CAAC,KAAK,IAAI;AAC7B,cAAM,MAAM,YAAAA,QAAK,QAAQ,KAAK,IAAI;AAClC,YAAI,QAAQ,OAAO;AACjB,qBAAW,KAAK,KAAK,KAAK,MAAM,GAAG,CAAC,MAAM,MAAM,IAAI,KAAK;AAAA,QAC3D;AACA,YAAI,QAAQ,SAAS,QAAQ,OAAO;AAClC,qBAAW,KAAK,KAAK,OAAO,KAAK;AACjC,qBAAW,KAAK,KAAK,OAAO,KAAK;AAAA,QACnC;AACA,YAAI,eAAe;AACnB,mBAAW,aAAa,YAAY;AAClC,gBAAM,SAAS,MAAM,MAAM,QAAQ,WAAW;AAAA;AAAA;AAAA,YAG5C,MAAM,KAAK;AAAA,YACX,YAAY,KAAK;AAAA,UACnB,CAAC;AACD,cAAI,OAAO,MAAM;AACf,2BAAe,OAAO;AACtB;AAAA,UACF;AAAA,QACF;AACA,YAAI,iBAAiB,QAAW;AAC9B,yBAAW,2BAAW,KAAK,QAAQ,KAAK,IAAI,aAAa;AACzD;AAAA,QACF;AAEA,cAAM,YAAY,YAAAA,QAAK,QAAQ,YAAY;AAC3C,YAAI,WAAW,WAAW,IAAI,YAAY;AAC1C,YAAI,CAAC,UAAU;AACb,gBAAM,kBAAc,gDAAqB,KAAK,WAAW,KAAK;AAC9D,cAAI,YAAY,SAAS,MAAM;AAC7B,2BAAW,2BAAW,KAAK,yBAAyB,WAAW;AAC/D;AAAA,UACF;AACA,qBAAW,YAAY;AACvB,qBAAW,IAAI,cAAc,QAAQ;AAAA,QACvC;AAEA,uBACE;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAEF,YAAI,SAAS,YAAY;AACvB,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF,OAAO;AAOL,gBAAM,oBAAgB;AAAA,YACpB;AAAA,YACA;AAAA,UACF;AACA,gBAAM,aAAa,qBAAqB,aAAa;AACrD,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAGA,SAAS,qBAAqB,eAAgD;AAC5E,SAAO,oBAAoB,OAAO,KAAK,aAAa,EAAE,SAAS,WAAW,CAAC;AAC7E;AAGA,SAAS,qBAAqB;AAAA,EAC5B,uBAAuB;AACzB,GAEG;AACD,QAAM,UAAU;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAQ;AAAA,IAER,YAAY,CAAC,UAAU,QAAQ;AAAA;AAAA;AAAA;AAAA,IAK/B,WAAW;AAAA;AAAA,IAGX,OAAO;AAAA,IACP,QAAQ,YAAAA,QAAK,MAAM,QAAQ,IAAI,CAAC,EAAE;AAAA,IAClC,SAAS,YAAAA,QAAK,MAAM,QAAQ,IAAI,CAAC,EAAE;AAAA,IAEnC,QAAQ;AAAA;AAAA,IACR,WAAW;AAAA,IAEX,UAAU;AAAA,EACZ;AAKA,MAAI,sBAAsB;AACxB,YAAQ,WAAW,KAAK,8BAA8B;AAAA,EACxD;AACA,SAAO;AACT;AAIA,eAAsB,eACpB,KACA,eACA,wBACA,sBACA,UAAmB,MAIlB;AACD,MAAI;AACJ,MAAI;AACF,aAAS,MAAM,eAAAC,QAAQ,MAAM;AAAA,MAC3B;AAAA;AAAA,MACA,aAAa,KAAC,mDAAwB,sBAAsB,CAAC;AAAA,MAC7D,SAAS;AAAA,QACP,gBAAgB;AAAA,UACd;AAAA,UACA,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,WAAW;AAAA,MACX,gBAAgB;AAAA,MAEhB,GAAG,qBAAqB,EAAE,qBAAqB,CAAC;AAAA,IAClD,CAAC;AACD,UAAM,qBAAqB,KAAK,eAAe,OAAO,QAAQ;AAAA,EAChE,SAAS,KAAU;AACjB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,mBAAmB,GAAG;AAAA,IACxC,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,OAAO,QAAQ;AACxB,UAAM,UAAU,OAAO,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI,EAAE,KAAK,IAAI;AAClE,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,aAAW,WAAW,OAAO,UAAU;AAErC,YAAQ,IAAI,aAAAC,QAAM,OAAO,oBAAoB,QAAQ,IAAI,EAAE,CAAC;AAAA,EAC9D;AACA,SAAO,MAAM,0BAA0B,KAAK,OAAO,QAAQ;AAC7D;AAOO,SAAS,QACd,eACA,iBACA,gBAC2B;AAC3B,SAAO,gBACJ;AAAA,IACC,CAAC,CAAC,UAAU,SAAS,MAAM,SAAS,mBAAmB;AAAA,EACzD,EACC;AAAA,IAAI,CAAC,CAAC,WAAW,QAAQ,UACxB,qDAA0B,eAAe,QAAQ;AAAA,EACnD;AACJ;AASA,eAAe,0BACb,KACA,UAIC;AACD,QAAM,EAAE,OAAO,IAAI;AAGnB,QAAM,kBAAkB,OAAO,KAAK,MAAM,EAAE;AAAA,IAAO,CAACF,UAClDA,MAAK,SAAS,UAAU;AAAA,EAC1B;AAKA,QAAM,sBAAsB,oBAAI,IAAgC;AAChE,aAAW,aAAa,iBAAiB;AACvC,UAAM,WAAW,UAAM,mDAAwB,KAAK,SAAS;AAC7D,wBAAoB,IAAI,YAAAA,QAAK,QAAQ,SAAS,GAAG,QAAQ;AAAA,EAC3D;AACA,QAAM,kBAA8D,CAAC;AACrE,aAAW,aAAa,iBAAiB;AACvC,UAAM,WAAW,oBAAoB,IAAI,YAAAA,QAAK,QAAQ,SAAS,CAAC;AAChE,UAAM,EAAE,QAAQ,IAAI,OAAO,SAAS;AACpC,UAAM,mBAAmB,QAAQ;AAAA,MAAO,CAAC,QACvC,IAAI,KAAK,SAAS,UAAU;AAAA,IAC9B;AACA,eAAW,cAAc,iBAAiB,IAAI,CAAC,QAAQ,IAAI,IAAI,GAAG;AAChE,YAAM,WAAW,oBAAoB,IAAI,YAAAA,QAAK,QAAQ,UAAU,CAAC;AACjE,UAAI,CAAC,UAAU;AACb,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,eAAe,YAAAA,QAAK,QAAQ,UAAU,CAAC,OAAO,CAAC,GAAG,oBAAoB,KAAK,CAAC,EAAE,SAAS,CAAC;AAAA,QAC1G,CAAC;AAAA,MACH;AACA,sBAAgB,KAAK,CAAC,UAAU,QAAQ,CAAC;AAAA,IAC3C;AAAA,EACF;AAEA,QAAM,aAAa,oBAAI,IAAgC;AACvD,aAAW,aAAa,oBAAoB,OAAO,GAAG;AACpD,eAAW,IAAI,UAAU,MAAM,SAAS;AAAA,EAC1C;AACA,SAAO,EAAE,YAAY,gBAAgB;AACvC;AAQA,eAAsB,kBACpB,KACA,eACA,iBACA,wBACA,sBACA,sBACA,UAAmB,OAIlB;AACD,MAAI;AACJ,MAAI;AACF,aAAS,MAAM,eAAAC,QAAQ,MAAM;AAAA,MAC3B;AAAA,MACA,aAAa,qBAAqB;AAAA,QAAI,CAAC,YACrC,mDAAwB,GAAG;AAAA,MAC7B;AAAA,MACA,SAAS;AAAA,QACP,gBAAgB;AAAA,UACd;AAAA,UACA,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,WAAW;AAAA,MACX,GAAG,qBAAqB,EAAE,qBAAqB,CAAC;AAAA,IAClD,CAAC;AACD,UAAM,qBAAqB,KAAK,eAAe,OAAO,QAAQ;AAAA,EAChE,SAAS,KAAU;AACjB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,mBAAmB,GAAG;AAAA,IACxC,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,OAAO,QAAQ;AACxB,UAAM,UAAU,OAAO,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI,EAAE,KAAK,IAAI;AAClE,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,aAAW,WAAW,OAAO,UAAU;AAErC,YAAQ,IAAI,aAAAC,QAAM,OAAO,oBAAoB,QAAQ,IAAI,EAAE,CAAC;AAAA,EAC9D;AAEA,QAAM,UAIA,CAAC;AACP,aAAW,aAAa,sBAAsB;AAC5C,UAAM,WAAW,YAAAF,QAAK,QAAQ,eAAe,UAAU,cAAc;AACrE,UAAM,mBACJ,SAAS,MAAM,GAAG,SAAS,YAAY,GAAG,CAAC,IAAI;AACjD,UAAM,oBACJ,SAAS,MAAM,GAAG,SAAS,YAAY,GAAG,CAAC,IAAI;AACjD,UAAM,WAAW,OAAO,YAAY;AAAA,MAClC,CAAC,eAAe,WAAW,SAAS;AAAA,IACtC,EAAE,CAAC;AACH,QAAI,CAAC,UAAU;AACb,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,wBAAwB,gBAAgB,OAAO,OAAO,YAAY,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC;AAAA,MACjH,CAAC;AAAA,IACH;AACA,UAAM,cAAc,OAAO,YAAY;AAAA,MACrC,CAAC,eAAe,WAAW,SAAS;AAAA,IACtC,EAAE,CAAC;AACH,YAAQ,KAAK;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,aAAa,QAAQ;AAAA,IACzB,CAAC,QAAQ,IAAI,UAAU,SAAS,uBAAuB;AAAA,EACzD;AACA,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,QAAM,YAAY,WAAW,CAAC;AAC9B,QAAM,mBAAmB,QAAQ;AAAA,IAC/B,CAAC,QAAQ,IAAI,UAAU,SAAS,uBAAuB;AAAA,EACzD;AAEA,QAAM,uCACJ,iBAAiB,IAAI,CAAC,EAAE,WAAW,UAAU,YAAY,OAAO;AAAA,IAC9D,oBAAgB;AAAA,MACd;AAAA,MACA;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV,MAAM,YAAAA,QAAK,SAAS,UAAU,MAAM,SAAS,IAAI;AAAA,MACjD,QAAQ,SAAS;AAAA,MACjB,WAAW,aAAa;AAAA,MACxB,aAAa;AAAA,IACf;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,MACA;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF,EAAE;AACJ,QAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,IACA,UAAU,UAAU;AAAA,EACtB;AACA,QAAM,gCAA+D;AAAA,IACnE,YAAY;AAAA,MACV,MAAM,YAAAA,QAAK,SAAS,uBAAuB,MAAM,UAAU,SAAS,IAAI;AAAA,MACxE,QAAQ,UAAU,SAAS;AAAA,MAC3B,WAAW,UAAU,aAAa;AAAA,MAClC,aAAa;AAAA,IACf;AAAA,IACA,cAAc;AAAA,EAChB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,eAAsB,sBACpB,KACA,wBACA,sBACA,sBACA,iBACA,UAAmB,OAYlB;AACD,MAAI;AACJ,QAAM,2BAA2B,CAAC;AAElC,MAAI,SAAS;AACb,aAAW,aAAa,CAAC,wBAAwB,GAAG,oBAAoB,GAAG;AACzE,UAAM,eAAe,YAAAA,QAAK;AAAA,MACxB,uBAAuB;AAAA,MACvB,UAAU;AAAA,IACZ;AACA,QAAI;AACJ,QAAI,IAAI,GAAG,OAAO,YAAAA,QAAK,QAAQ,cAAc,WAAW,CAAC,GAAG;AAC1D,gBACG,UAAM,6BAAa,KAAK,cAAc,eAAe,GAAG,CAAC,KAAK;AAAA,IACnE,WAAW,IAAI,GAAG,OAAO,YAAAA,QAAK,QAAQ,cAAc,WAAW,CAAC,GAAG;AACjE,gBACG,UAAM,6BAAa,KAAK,cAAc,eAAe,GAAG,CAAC,KAAK;AAAA,IACnE,OAAO;AACL,eAAS;AAAA,IACX;AAEA,UAAM,cAAc,UAAM,yCAAyB,KAAK,YAAY;AACpE,UAAM,eAIF,UAAM;AAAA,MACR;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,aAAa,qBAAqB,SAAS,GAAG;AAChD,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AACA,UAAM,YAAY,aAAa;AAC/B,QAAI,QAAQ;AACV,UAAI,SAAS;AACX,wCAAY,KAAK,yCAAyC;AAAA,MAC5D;AACA,YAAM,aAIF,UAAM;AAAA,QACR;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA,YAAAA,QAAK,KAAK,SAAS,MAAM;AAAA,QACzB;AAAA,QACA;AAAA,MACF;AAEA,YAAM,2BAA6C,CAAC;AACpD,iBAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF,KAAK,WAAW,sBAAsB;AACpC,iCAAyB,KAAK;AAAA,UAC5B,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,aAAa,UAAM,iCAAiB,KAAK,YAAY;AAC3D,0BAAoB;AAAA,QAClB;AAAA,QACA,WAAW,UAAU,OAAO,WAAW,OAAO,EAAE,OAAO,UAAU;AAAA,QACjE;AAAA,MACF;AAAA,IACF,OAAO;AAEL,UAAI,UAAU,SAAS,uBAAuB,MAAM;AAClD,cAAM,aAIF,UAAM;AAAA,UACR;AAAA,UACA;AAAA,UACA,YAAY;AAAA,UACZ;AAAA,UACA;AAAA,UACA,YAAAA,QAAK,KAAK,SAAS,MAAM;AAAA,UACzB;AAAA,UACA;AAAA,QACF;AACA,YAAI,WAAW,QAAQ,SAAS,GAAG;AAEjC,gBAAM,IAAI,MAAM;AAAA,YACd,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBAAgB,yFAAyF,YAAY;AAAA,UACvH,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,qBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,MACF;AACA,+BAAyB,KAAK,EAAE,gBAAgB,QAAQ,UAAU,CAAC;AAAA,IACrE;AACA,aAAS;AAAA,EACX;AAEA,MAAI,CAAC,mBAAmB;AACtB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,SAAO,EAAE,mBAAmB,yBAAyB;AACvD;AAEA,eAAe,qBACb,KACA,eACA,UACA;AACA,aAAW,CAAC,SAAS,KAAK,KAAK,OAAO,QAAQ,SAAS,MAAM,GAAG;AAC9D;AAAA;AAAA,MAEE,YAAAA,QAAK,SAAS,OAAO,EAAE,SAAS,eAAe;AAAA;AAAA,MAG/C,QAAQ,QAAQ,aAAa,MAAM,MACnC,QAAQ,WAAW,cAAc,KACjC,QAAQ,WAAW,YAAY;AAAA,MAC/B;AACA;AAAA,IACF;AACA,UAAM,UAAU,YAAAA,QAAK,QAAQ,eAAe,OAAO;AACnD,UAAM,KAAK,IAAI,GAAG,KAAK,OAAO;AAC9B,QAAI,GAAG,SAAS,MAAM,OAAO;AAG3B;AAAA,QACE;AAAA,QACA,gBAAgB,OAAO;AAAA,MACzB;AACA,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AACA,QAAI,GAAG,aAAa,SAAS,EAAE;AAAA,EACjC;AACF;", "names": ["path", "esbuild", "chalk"]}
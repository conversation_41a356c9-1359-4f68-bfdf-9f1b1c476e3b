{"version": 3, "sources": ["../../../../src/cli/lib/tracing.ts"], "sourcesContent": ["import crypto from \"node:crypto\";\n\ntype TraceId = string; // u128\ntype SpanId = string; // u64\n\ntype Nanoseconds = bigint;\n\n// base64 URL encoded little endian\ntype SerializedNanoseconds = string;\n\nexport class Reporter {\n  spans: CompletedSpan[] = [];\n\n  emit(span: CompletedSpan) {\n    this.spans.push(span);\n  }\n}\n\ntype EventRecord = {\n  name: string;\n  timestampUnixNs: Nanoseconds;\n  properties: Record<string, string>;\n};\n\nexport class Span {\n  private properties: Record<string, string> = {};\n  private events: EventRecord[] = [];\n\n  private constructor(\n    private reporter: Reporter | undefined,\n    private traceId: TraceId,\n    private parentId: SpanId,\n    private spanId: SpanId,\n    private beginTimeUnixNs: Nanoseconds,\n    private name: string,\n  ) {}\n\n  static noop() {\n    return new Span(\n      undefined,\n      randomTraceId(),\n      randomSpanId(),\n      randomSpanId(),\n      unixTimeNs(),\n      \"\",\n    );\n  }\n\n  static root(reporter: Reporter, name: string) {\n    const traceId = randomTraceId();\n    const parentId = emptySpanId();\n    const spanId = randomSpanId();\n    const beginTimeUnixNs = unixTimeNs();\n    return new Span(reporter, traceId, parentId, spanId, beginTimeUnixNs, name);\n  }\n\n  setProperty(key: string, value: string) {\n    this.properties[key] = value;\n  }\n\n  childSpan(name: string): Span {\n    const spanId = randomSpanId();\n    const beginTimeUnixNs = unixTimeNs();\n    return new Span(\n      this.reporter,\n      this.traceId,\n      this.spanId,\n      spanId,\n      beginTimeUnixNs,\n      name,\n    );\n  }\n\n  enter<T>(name: string, f: (span: Span) => T): T {\n    const childSpan = this.childSpan(name);\n    try {\n      const result = f(childSpan);\n      childSpan.end();\n      return result;\n    } finally {\n      childSpan.end();\n    }\n  }\n\n  async enterAsync<T>(name: string, f: (span: Span) => Promise<T>): Promise<T> {\n    const childSpan = this.childSpan(name);\n    try {\n      return await f(childSpan);\n    } finally {\n      childSpan.end();\n    }\n  }\n\n  end() {\n    const endTimeUnixNs = unixTimeNs();\n    const durationNs = endTimeUnixNs - this.beginTimeUnixNs;\n    const span = {\n      traceId: this.traceId,\n      parentId: this.parentId,\n      spanId: this.spanId,\n      beginTimeUnixNs: serializeNanoseconds(this.beginTimeUnixNs),\n      durationNs: serializeNanoseconds(durationNs),\n      name: this.name,\n      properties: this.properties,\n      events: this.events.map((event) => ({\n        name: event.name,\n        timestampUnixNs: serializeNanoseconds(event.timestampUnixNs),\n        properties: event.properties,\n      })),\n    };\n    if (this.reporter) {\n      this.reporter.emit(span);\n    }\n  }\n\n  encodeW3CTraceparent() {\n    // Encode traceId and spanId as a big-endian hex strings.\n    const traceIdBytes = Buffer.from(this.traceId, \"base64url\");\n    const traceIdBigInt =\n      traceIdBytes.readBigUInt64LE(0) |\n      (traceIdBytes.readBigUInt64LE(8) << 64n);\n    const traceIdHex = traceIdBigInt.toString(16).padStart(32, \"0\");\n\n    const spanIdBytes = Buffer.from(this.spanId, \"base64url\");\n    const spanIdBigInt = spanIdBytes.readBigUInt64LE(0);\n    const spanIdHex = spanIdBigInt.toString(16).padStart(16, \"0\");\n\n    return `00-${traceIdHex}-${spanIdHex}-01`;\n  }\n}\n\nfunction randomTraceId() {\n  return Buffer.from(crypto.getRandomValues(new Uint8Array(16))).toString(\n    \"base64url\",\n  );\n}\n\nfunction emptySpanId() {\n  return Buffer.from(new Uint8Array(8)).toString(\"base64url\");\n}\n\nfunction randomSpanId() {\n  return Buffer.from(crypto.getRandomValues(new Uint8Array(8))).toString(\n    \"base64url\",\n  );\n}\n\nfunction unixTimeNs() {\n  // Note that as a unix nanosecond timestamp, performance.timeOrigin * 1000 is less than\n  // Number.MAX_SAFE_INTEGER, so multiply by 1000 to convert to microseconds, round, convert\n  // to bigint, and then multiply again to convert to nanoseconds.\n  return (\n    BigInt(Math.floor(performance.timeOrigin * 1000)) * 1000n +\n    BigInt(Math.floor(performance.now() * 1000)) * 1000n\n  );\n}\n\nfunction serializeNanoseconds(ns: Nanoseconds): SerializedNanoseconds {\n  const buffer = Buffer.alloc(8);\n  buffer.writeBigUInt64LE(ns, 0);\n  return buffer.toString(\"base64url\");\n}\n\ntype CompletedSpan = {\n  traceId: TraceId;\n  parentId: SpanId;\n  spanId: SpanId;\n  beginTimeUnixNs: SerializedNanoseconds;\n  durationNs: SerializedNanoseconds;\n  name: string;\n  properties: Record<string, string>;\n  events: SerializedEventRecord[];\n};\n\ntype SerializedEventRecord = {\n  name: string;\n  timestampUnixNs: SerializedNanoseconds;\n  properties: Record<string, string>;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAmB;AAUZ,MAAM,SAAS;AAAA,EAAf;AACL,iCAAyB,CAAC;AAAA;AAAA,EAE1B,KAAK,MAAqB;AACxB,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AACF;AAQO,MAAM,KAAK;AAAA,EAIR,YACE,UACA,SACA,UACA,QACA,iBACA,MACR;AANQ;AACA;AACA;AACA;AACA;AACA;AATV,wBAAQ,cAAqC,CAAC;AAC9C,wBAAQ,UAAwB,CAAC;AAAA,EAS9B;AAAA,EAEH,OAAO,OAAO;AACZ,WAAO,IAAI;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,WAAW;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,KAAK,UAAoB,MAAc;AAC5C,UAAM,UAAU,cAAc;AAC9B,UAAM,WAAW,YAAY;AAC7B,UAAM,SAAS,aAAa;AAC5B,UAAM,kBAAkB,WAAW;AACnC,WAAO,IAAI,KAAK,UAAU,SAAS,UAAU,QAAQ,iBAAiB,IAAI;AAAA,EAC5E;AAAA,EAEA,YAAY,KAAa,OAAe;AACtC,SAAK,WAAW,GAAG,IAAI;AAAA,EACzB;AAAA,EAEA,UAAU,MAAoB;AAC5B,UAAM,SAAS,aAAa;AAC5B,UAAM,kBAAkB,WAAW;AACnC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAS,MAAc,GAAyB;AAC9C,UAAM,YAAY,KAAK,UAAU,IAAI;AACrC,QAAI;AACF,YAAM,SAAS,EAAE,SAAS;AAC1B,gBAAU,IAAI;AACd,aAAO;AAAA,IACT,UAAE;AACA,gBAAU,IAAI;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,MAAM,WAAc,MAAc,GAA2C;AAC3E,UAAM,YAAY,KAAK,UAAU,IAAI;AACrC,QAAI;AACF,aAAO,MAAM,EAAE,SAAS;AAAA,IAC1B,UAAE;AACA,gBAAU,IAAI;AAAA,IAChB;AAAA,EACF;AAAA,EAEA,MAAM;AACJ,UAAM,gBAAgB,WAAW;AACjC,UAAM,aAAa,gBAAgB,KAAK;AACxC,UAAM,OAAO;AAAA,MACX,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,iBAAiB,qBAAqB,KAAK,eAAe;AAAA,MAC1D,YAAY,qBAAqB,UAAU;AAAA,MAC3C,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK,OAAO,IAAI,CAAC,WAAW;AAAA,QAClC,MAAM,MAAM;AAAA,QACZ,iBAAiB,qBAAqB,MAAM,eAAe;AAAA,QAC3D,YAAY,MAAM;AAAA,MACpB,EAAE;AAAA,IACJ;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAK,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EAEA,uBAAuB;AAErB,UAAM,eAAe,OAAO,KAAK,KAAK,SAAS,WAAW;AAC1D,UAAM,gBACJ,aAAa,gBAAgB,CAAC,IAC7B,aAAa,gBAAgB,CAAC,KAAK;AACtC,UAAM,aAAa,cAAc,SAAS,EAAE,EAAE,SAAS,IAAI,GAAG;AAE9D,UAAM,cAAc,OAAO,KAAK,KAAK,QAAQ,WAAW;AACxD,UAAM,eAAe,YAAY,gBAAgB,CAAC;AAClD,UAAM,YAAY,aAAa,SAAS,EAAE,EAAE,SAAS,IAAI,GAAG;AAE5D,WAAO,MAAM,UAAU,IAAI,SAAS;AAAA,EACtC;AACF;AAEA,SAAS,gBAAgB;AACvB,SAAO,OAAO,KAAK,mBAAAA,QAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC,CAAC,EAAE;AAAA,IAC7D;AAAA,EACF;AACF;AAEA,SAAS,cAAc;AACrB,SAAO,OAAO,KAAK,IAAI,WAAW,CAAC,CAAC,EAAE,SAAS,WAAW;AAC5D;AAEA,SAAS,eAAe;AACtB,SAAO,OAAO,KAAK,mBAAAA,QAAO,gBAAgB,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE;AAAA,IAC5D;AAAA,EACF;AACF;AAEA,SAAS,aAAa;AAIpB,SACE,OAAO,KAAK,MAAM,YAAY,aAAa,GAAI,CAAC,IAAI,QACpD,OAAO,KAAK,MAAM,YAAY,IAAI,IAAI,GAAI,CAAC,IAAI;AAEnD;AAEA,SAAS,qBAAqB,IAAwC;AACpE,QAAM,SAAS,OAAO,MAAM,CAAC;AAC7B,SAAO,iBAAiB,IAAI,CAAC;AAC7B,SAAO,OAAO,SAAS,WAAW;AACpC;", "names": ["crypto"]}
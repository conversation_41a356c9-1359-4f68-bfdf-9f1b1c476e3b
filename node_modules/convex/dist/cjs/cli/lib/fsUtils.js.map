{"version": 3, "sources": ["../../../../src/cli/lib/fsUtils.ts"], "sourcesContent": ["import { Context, logOutput } from \"../../bundler/context.js\";\nimport path from \"path\";\nimport { NodeFs } from \"../../bundler/fs.js\";\n\nexport function recursivelyDelete(\n  ctx: Context,\n  deletePath: string,\n  opts?: { force?: boolean; dryRun?: boolean },\n) {\n  const dryRun = !!opts?.dryRun;\n  let st;\n  try {\n    st = ctx.fs.stat(deletePath);\n  } catch (err: any) {\n    if (err.code === \"ENOENT\" && opts?.force) {\n      return;\n    }\n    // eslint-disable-next-line no-restricted-syntax\n    throw err;\n  }\n  if (st.isDirectory()) {\n    for (const entry of ctx.fs.listDir(deletePath)) {\n      recursivelyDelete(ctx, path.join(deletePath, entry.name), opts);\n    }\n    if (dryRun) {\n      logOutput(ctx, `Command would delete directory: ${deletePath}`);\n      return;\n    }\n    try {\n      ctx.fs.rmdir(deletePath);\n    } catch (err: any) {\n      if (err.code !== \"ENOENT\") {\n        // eslint-disable-next-line no-restricted-syntax\n        throw err;\n      }\n    }\n  } else {\n    if (dryRun) {\n      logOutput(ctx, `Command would delete file: ${deletePath}`);\n      return;\n    }\n    try {\n      ctx.fs.unlink(deletePath);\n    } catch (err: any) {\n      if (err.code !== \"ENOENT\") {\n        // eslint-disable-next-line no-restricted-syntax\n        throw err;\n      }\n    }\n  }\n}\n\nexport async function recursivelyCopy(\n  ctx: Context,\n  nodeFs: NodeFs,\n  src: string,\n  dest: string,\n) {\n  const st = nodeFs.stat(src);\n  if (st.isDirectory()) {\n    nodeFs.mkdir(dest, { recursive: true });\n    for (const entry of nodeFs.listDir(src)) {\n      await recursivelyCopy(\n        ctx,\n        nodeFs,\n        path.join(src, entry.name),\n        path.join(dest, entry.name),\n      );\n    }\n  } else {\n    // Don't use writeUtf8File to allow copying arbitrary files\n    await nodeFs.writeFileStream(dest, nodeFs.createReadStream(src, {}));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAmC;AACnC,kBAAiB;AAGV,SAAS,kBACd,KACA,YACA,MACA;AACA,QAAM,SAAS,CAAC,CAAC,MAAM;AACvB,MAAI;AACJ,MAAI;AACF,SAAK,IAAI,GAAG,KAAK,UAAU;AAAA,EAC7B,SAAS,KAAU;AACjB,QAAI,IAAI,SAAS,YAAY,MAAM,OAAO;AACxC;AAAA,IACF;AAEA,UAAM;AAAA,EACR;AACA,MAAI,GAAG,YAAY,GAAG;AACpB,eAAW,SAAS,IAAI,GAAG,QAAQ,UAAU,GAAG;AAC9C,wBAAkB,KAAK,YAAAA,QAAK,KAAK,YAAY,MAAM,IAAI,GAAG,IAAI;AAAA,IAChE;AACA,QAAI,QAAQ;AACV,oCAAU,KAAK,mCAAmC,UAAU,EAAE;AAC9D;AAAA,IACF;AACA,QAAI;AACF,UAAI,GAAG,MAAM,UAAU;AAAA,IACzB,SAAS,KAAU;AACjB,UAAI,IAAI,SAAS,UAAU;AAEzB,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,QAAQ;AACV,oCAAU,KAAK,8BAA8B,UAAU,EAAE;AACzD;AAAA,IACF;AACA,QAAI;AACF,UAAI,GAAG,OAAO,UAAU;AAAA,IAC1B,SAAS,KAAU;AACjB,UAAI,IAAI,SAAS,UAAU;AAEzB,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAsB,gBACpB,KACA,QACA,KACA,MACA;AACA,QAAM,KAAK,OAAO,KAAK,GAAG;AAC1B,MAAI,GAAG,YAAY,GAAG;AACpB,WAAO,MAAM,MAAM,EAAE,WAAW,KAAK,CAAC;AACtC,eAAW,SAAS,OAAO,QAAQ,GAAG,GAAG;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,YAAAA,QAAK,KAAK,KAAK,MAAM,IAAI;AAAA,QACzB,YAAAA,QAAK,KAAK,MAAM,MAAM,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,EACF,OAAO;AAEL,UAAM,OAAO,gBAAgB,MAAM,OAAO,iBAAiB,KAAK,CAAC,CAAC,CAAC;AAAA,EACrE;AACF;", "names": ["path"]}
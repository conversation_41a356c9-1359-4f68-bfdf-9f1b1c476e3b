{"version": 3, "sources": ["../../../../src/cli/lib/deploy2.ts"], "sourcesContent": ["import {\n  changeSpinner,\n  Context,\n  logError,\n  logFailure,\n  logFinishedStep,\n  logV<PERSON><PERSON>e,\n  showSpinner,\n} from \"../../bundler/context.js\";\nimport { spawnSync } from \"child_process\";\nimport { deploymentFetch, logAndHandleFetchError } from \"./utils/utils.js\";\nimport {\n  schemaStatus,\n  SchemaStatus,\n  StartPushRequest,\n  startPushResponse,\n  StartPushResponse,\n} from \"./deployApi/startPush.js\";\nimport {\n  AppDefinitionConfig,\n  ComponentDefinitionConfig,\n} from \"./deployApi/definitionConfig.js\";\nimport chalk from \"chalk\";\nimport { finishPushDiff, FinishPushDiff } from \"./deployApi/finishPush.js\";\nimport { Reporter, Span } from \"./tracing.js\";\nimport { promisify } from \"node:util\";\nimport zlib from \"node:zlib\";\nimport { PushOptions } from \"./push.js\";\nimport { runPush } from \"./components.js\";\nimport { suggestedEnvVarName } from \"./envvars.js\";\nimport { runSystemQuery } from \"./run.js\";\nimport { handlePushConfigError } from \"./config.js\";\n\nconst brotli = promisify(zlib.brotliCompress);\n\nasync function brotliCompress(ctx: Context, data: string): Promise<Buffer> {\n  const start = performance.now();\n  const result = await brotli(data, {\n    params: {\n      [zlib.constants.BROTLI_PARAM_MODE]: zlib.constants.BROTLI_MODE_TEXT,\n      [zlib.constants.BROTLI_PARAM_QUALITY]: 4,\n    },\n  });\n  const end = performance.now();\n  const duration = end - start;\n  logVerbose(\n    ctx,\n    `Compressed ${(data.length / 1024).toFixed(2)}KiB to ${(result.length / 1024).toFixed(2)}KiB (${((result.length / data.length) * 100).toFixed(2)}%) in ${duration.toFixed(2)}ms`,\n  );\n  return result;\n}\n\n/** Push configuration2 to the given remote origin. */\nexport async function startPush(\n  ctx: Context,\n  span: Span,\n  request: StartPushRequest,\n  options: {\n    url: string;\n    deploymentName: string | null;\n  },\n): Promise<StartPushResponse> {\n  const custom = (_k: string | number, s: any) =>\n    typeof s === \"string\" ? s.slice(0, 40) + (s.length > 40 ? \"...\" : \"\") : s;\n  logVerbose(ctx, JSON.stringify(request, custom, 2));\n  const onError = (err: any) => {\n    if (err.toString() === \"TypeError: fetch failed\") {\n      changeSpinner(\n        ctx,\n        `Fetch failed, is ${options.url} correct? Retrying...`,\n      );\n    }\n  };\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: options.url,\n    adminKey: request.adminKey,\n    onError,\n  });\n  changeSpinner(ctx, \"Analyzing source code...\");\n  try {\n    const response = await fetch(\"/api/deploy2/start_push\", {\n      body: await brotliCompress(ctx, JSON.stringify(request)),\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Content-Encoding\": \"br\",\n        traceparent: span.encodeW3CTraceparent(),\n      },\n    });\n    return startPushResponse.parse(await response.json());\n  } catch (error: unknown) {\n    return await handlePushConfigError(\n      ctx,\n      error,\n      \"Error: Unable to start push to \" + options.url,\n      options.deploymentName,\n    );\n  }\n}\n\n// Long poll every 10s for progress on schema validation.\nconst SCHEMA_TIMEOUT_MS = 10_000;\n\nexport async function waitForSchema(\n  ctx: Context,\n  span: Span,\n  startPush: StartPushResponse,\n  options: {\n    adminKey: string;\n    url: string;\n    dryRun: boolean;\n  },\n) {\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: options.url,\n    adminKey: options.adminKey,\n  });\n\n  changeSpinner(\n    ctx,\n    \"Backfilling indexes and checking that documents match your schema...\",\n  );\n\n  while (true) {\n    let currentStatus: SchemaStatus;\n    try {\n      const response = await fetch(\"/api/deploy2/wait_for_schema\", {\n        body: JSON.stringify({\n          adminKey: options.adminKey,\n          schemaChange: startPush.schemaChange,\n          timeoutMs: SCHEMA_TIMEOUT_MS,\n          dryRun: options.dryRun,\n        }),\n        method: \"POST\",\n        headers: {\n          traceparent: span.encodeW3CTraceparent(),\n        },\n      });\n      currentStatus = schemaStatus.parse(await response.json());\n    } catch (error: unknown) {\n      logFailure(ctx, \"Error: Unable to wait for schema from \" + options.url);\n      return await logAndHandleFetchError(ctx, error);\n    }\n    switch (currentStatus.type) {\n      case \"inProgress\": {\n        let schemaDone = true;\n        let indexesComplete = 0;\n        let indexesTotal = 0;\n        for (const componentStatus of Object.values(currentStatus.components)) {\n          if (!componentStatus.schemaValidationComplete) {\n            schemaDone = false;\n          }\n          indexesComplete += componentStatus.indexesComplete;\n          indexesTotal += componentStatus.indexesTotal;\n        }\n        const indexesDone = indexesComplete === indexesTotal;\n        let msg: string;\n        if (!indexesDone && !schemaDone) {\n          msg = `Backfilling indexes (${indexesComplete}/${indexesTotal} ready) and checking that documents match your schema...`;\n        } else if (!indexesDone) {\n          msg = `Backfilling indexes (${indexesComplete}/${indexesTotal} ready)...`;\n        } else {\n          msg = \"Checking that documents match your schema...\";\n        }\n        changeSpinner(ctx, msg);\n        break;\n      }\n      case \"failed\": {\n        // Schema validation failed. This could be either because the data\n        // is bad or the schema is wrong. Classify this as a filesystem error\n        // because adjusting `schema.ts` is the most normal next step.\n        let msg = \"Schema validation failed\";\n        if (currentStatus.componentPath) {\n          msg += ` in component \"${currentStatus.componentPath}\"`;\n        }\n        msg += \".\";\n        logFailure(ctx, msg);\n        logError(ctx, chalk.red(`${currentStatus.error}`));\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: {\n            \"invalid filesystem or db data\": currentStatus.tableName\n              ? {\n                  tableName: currentStatus.tableName,\n                  componentPath: currentStatus.componentPath,\n                }\n              : null,\n          },\n          printedMessage: null, // TODO - move logging into here\n        });\n      }\n      case \"raceDetected\": {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `Schema was overwritten by another push.`,\n        });\n      }\n      case \"complete\": {\n        changeSpinner(ctx, \"Schema validation complete.\");\n        return;\n      }\n    }\n  }\n}\n\nexport async function finishPush(\n  ctx: Context,\n  span: Span,\n  startPush: StartPushResponse,\n  options: {\n    adminKey: string;\n    url: string;\n    dryRun: boolean;\n    verbose?: boolean;\n  },\n): Promise<FinishPushDiff> {\n  changeSpinner(ctx, \"Finalizing push...\");\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: options.url,\n    adminKey: options.adminKey,\n  });\n  const request = {\n    adminKey: options.adminKey,\n    startPush,\n    dryRun: options.dryRun,\n  };\n  try {\n    const response = await fetch(\"/api/deploy2/finish_push\", {\n      body: await brotliCompress(ctx, JSON.stringify(request)),\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Content-Encoding\": \"br\",\n        traceparent: span.encodeW3CTraceparent(),\n      },\n    });\n    return finishPushDiff.parse(await response.json());\n  } catch (error: unknown) {\n    logFailure(ctx, \"Error: Unable to finish push to \" + options.url);\n    return await logAndHandleFetchError(ctx, error);\n  }\n}\n\nexport type ComponentDefinitionConfigWithoutImpls = Omit<\n  ComponentDefinitionConfig,\n  \"schema\" | \"functions\"\n>;\nexport type AppDefinitionConfigWithoutImpls = Omit<\n  AppDefinitionConfig,\n  \"schema\" | \"functions\" | \"auth\"\n>;\n\nexport async function reportPushCompleted(\n  ctx: Context,\n  adminKey: string,\n  url: string,\n  reporter: Reporter,\n) {\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: url,\n    adminKey,\n  });\n  try {\n    const response = await fetch(\"/api/deploy2/report_push_completed\", {\n      body: JSON.stringify({\n        adminKey,\n        spans: reporter.spans,\n      }),\n      method: \"POST\",\n    });\n    await response.json();\n  } catch (error: unknown) {\n    logFailure(\n      ctx,\n      \"Error: Unable to report push completed to \" + url + \": \" + error,\n    );\n  }\n}\n\nexport async function deployToDeployment(\n  ctx: Context,\n  credentials: {\n    url: string;\n    adminKey: string;\n    deploymentName: string | null;\n  },\n  options: {\n    verbose?: boolean | undefined;\n    dryRun?: boolean | undefined;\n    yes?: boolean | undefined;\n    typecheck: \"enable\" | \"try\" | \"disable\";\n    typecheckComponents: boolean;\n    codegen: \"enable\" | \"disable\";\n    cmd?: string | undefined;\n    cmdUrlEnvVarName?: string | undefined;\n\n    debugBundlePath?: string | undefined;\n    debug?: boolean | undefined;\n    writePushRequest?: string | undefined;\n    liveComponentSources?: boolean | undefined;\n    partitionId?: string | undefined;\n  },\n) {\n  const { url, adminKey } = credentials;\n  await runCommand(ctx, { ...options, url, adminKey });\n\n  const pushOptions: PushOptions = {\n    deploymentName: credentials.deploymentName,\n    adminKey,\n    verbose: !!options.verbose,\n    dryRun: !!options.dryRun,\n    typecheck: options.typecheck,\n    typecheckComponents: options.typecheckComponents,\n    debug: !!options.debug,\n    debugBundlePath: options.debugBundlePath,\n    codegen: options.codegen === \"enable\",\n    url,\n    writePushRequest: options.writePushRequest,\n    liveComponentSources: !!options.liveComponentSources,\n  };\n  showSpinner(\n    ctx,\n    `Deploying to ${url}...${options.dryRun ? \" [dry run]\" : \"\"}`,\n  );\n  await runPush(ctx, pushOptions);\n  logFinishedStep(\n    ctx,\n    `${\n      options.dryRun ? \"Would have deployed\" : \"Deployed\"\n    } Convex functions to ${url}`,\n  );\n}\n\nexport async function runCommand(\n  ctx: Context,\n  options: {\n    cmdUrlEnvVarName?: string | undefined;\n    cmd?: string | undefined;\n    dryRun?: boolean | undefined;\n    url: string;\n    adminKey: string;\n  },\n) {\n  if (options.cmd === undefined) {\n    return;\n  }\n\n  const urlVar =\n    options.cmdUrlEnvVarName ?? (await suggestedEnvVarName(ctx)).envVar;\n  showSpinner(\n    ctx,\n    `Running '${options.cmd}' with environment variable \"${urlVar}\" set...${\n      options.dryRun ? \" [dry run]\" : \"\"\n    }`,\n  );\n  if (!options.dryRun) {\n    const canonicalCloudUrl = await fetchDeploymentCanonicalCloudUrl(ctx, {\n      deploymentUrl: options.url,\n      adminKey: options.adminKey,\n    });\n\n    const env = { ...process.env };\n    env[urlVar] = canonicalCloudUrl;\n    const result = spawnSync(options.cmd, {\n      env,\n      stdio: \"inherit\",\n      shell: true,\n    });\n    if (result.status !== 0) {\n      await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage: `'${options.cmd}' failed`,\n      });\n    }\n  }\n  logFinishedStep(\n    ctx,\n    `${options.dryRun ? \"Would have run\" : \"Ran\"} \"${\n      options.cmd\n    }\" with environment variable \"${urlVar}\" set`,\n  );\n}\n\nexport async function fetchDeploymentCanonicalCloudUrl(\n  ctx: Context,\n  options: { deploymentUrl: string; adminKey: string },\n): Promise<string> {\n  const result = await runSystemQuery(ctx, {\n    ...options,\n    functionName: \"_system/cli/convexUrl:cloudUrl\",\n    componentPath: undefined,\n    args: {},\n  });\n  if (typeof result !== \"string\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem or env vars\",\n      printedMessage: \"Invalid process.env.CONVEX_CLOUD_URL\",\n    });\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAQO;AACP,2BAA0B;AAC1B,mBAAwD;AACxD,uBAMO;AAKP,mBAAkB;AAClB,wBAA+C;AAE/C,uBAA0B;AAC1B,uBAAiB;AAEjB,wBAAwB;AACxB,qBAAoC;AACpC,iBAA+B;AAC/B,oBAAsC;AAEtC,MAAM,aAAS,4BAAU,iBAAAA,QAAK,cAAc;AAE5C,eAAe,eAAe,KAAc,MAA+B;AACzE,QAAM,QAAQ,YAAY,IAAI;AAC9B,QAAM,SAAS,MAAM,OAAO,MAAM;AAAA,IAChC,QAAQ;AAAA,MACN,CAAC,iBAAAA,QAAK,UAAU,iBAAiB,GAAG,iBAAAA,QAAK,UAAU;AAAA,MACnD,CAAC,iBAAAA,QAAK,UAAU,oBAAoB,GAAG;AAAA,IACzC;AAAA,EACF,CAAC;AACD,QAAM,MAAM,YAAY,IAAI;AAC5B,QAAM,WAAW,MAAM;AACvB;AAAA,IACE;AAAA,IACA,eAAe,KAAK,SAAS,MAAM,QAAQ,CAAC,CAAC,WAAW,OAAO,SAAS,MAAM,QAAQ,CAAC,CAAC,SAAU,OAAO,SAAS,KAAK,SAAU,KAAK,QAAQ,CAAC,CAAC,SAAS,SAAS,QAAQ,CAAC,CAAC;AAAA,EAC9K;AACA,SAAO;AACT;AAGA,eAAsB,UACpB,KACA,MACA,SACA,SAI4B;AAC5B,QAAM,SAAS,CAAC,IAAqB,MACnC,OAAO,MAAM,WAAW,EAAE,MAAM,GAAG,EAAE,KAAK,EAAE,SAAS,KAAK,QAAQ,MAAM;AAC1E,iCAAW,KAAK,KAAK,UAAU,SAAS,QAAQ,CAAC,CAAC;AAClD,QAAM,UAAU,CAAC,QAAa;AAC5B,QAAI,IAAI,SAAS,MAAM,2BAA2B;AAChD;AAAA,QACE;AAAA,QACA,oBAAoB,QAAQ,GAAG;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC,eAAe,QAAQ;AAAA,IACvB,UAAU,QAAQ;AAAA,IAClB;AAAA,EACF,CAAC;AACD,oCAAc,KAAK,0BAA0B;AAC7C,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,2BAA2B;AAAA,MACtD,MAAM,MAAM,eAAe,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,MACvD,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,QACpB,aAAa,KAAK,qBAAqB;AAAA,MACzC;AAAA,IACF,CAAC;AACD,WAAO,mCAAkB,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,EACtD,SAAS,OAAgB;AACvB,WAAO,UAAM;AAAA,MACX;AAAA,MACA;AAAA,MACA,oCAAoC,QAAQ;AAAA,MAC5C,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AAGA,MAAM,oBAAoB;AAE1B,eAAsB,cACpB,KACA,MACAC,YACA,SAKA;AACA,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC,eAAe,QAAQ;AAAA,IACvB,UAAU,QAAQ;AAAA,EACpB,CAAC;AAED;AAAA,IACE;AAAA,IACA;AAAA,EACF;AAEA,SAAO,MAAM;AACX,QAAI;AACJ,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,gCAAgC;AAAA,QAC3D,MAAM,KAAK,UAAU;AAAA,UACnB,UAAU,QAAQ;AAAA,UAClB,cAAcA,WAAU;AAAA,UACxB,WAAW;AAAA,UACX,QAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,aAAa,KAAK,qBAAqB;AAAA,QACzC;AAAA,MACF,CAAC;AACD,sBAAgB,8BAAa,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,IAC1D,SAAS,OAAgB;AACvB,qCAAW,KAAK,2CAA2C,QAAQ,GAAG;AACtE,aAAO,UAAM,qCAAuB,KAAK,KAAK;AAAA,IAChD;AACA,YAAQ,cAAc,MAAM;AAAA,MAC1B,KAAK,cAAc;AACjB,YAAI,aAAa;AACjB,YAAI,kBAAkB;AACtB,YAAI,eAAe;AACnB,mBAAW,mBAAmB,OAAO,OAAO,cAAc,UAAU,GAAG;AACrE,cAAI,CAAC,gBAAgB,0BAA0B;AAC7C,yBAAa;AAAA,UACf;AACA,6BAAmB,gBAAgB;AACnC,0BAAgB,gBAAgB;AAAA,QAClC;AACA,cAAM,cAAc,oBAAoB;AACxC,YAAI;AACJ,YAAI,CAAC,eAAe,CAAC,YAAY;AAC/B,gBAAM,wBAAwB,eAAe,IAAI,YAAY;AAAA,QAC/D,WAAW,CAAC,aAAa;AACvB,gBAAM,wBAAwB,eAAe,IAAI,YAAY;AAAA,QAC/D,OAAO;AACL,gBAAM;AAAA,QACR;AACA,0CAAc,KAAK,GAAG;AACtB;AAAA,MACF;AAAA,MACA,KAAK,UAAU;AAIb,YAAI,MAAM;AACV,YAAI,cAAc,eAAe;AAC/B,iBAAO,kBAAkB,cAAc,aAAa;AAAA,QACtD;AACA,eAAO;AACP,uCAAW,KAAK,GAAG;AACnB,qCAAS,KAAK,aAAAC,QAAM,IAAI,GAAG,cAAc,KAAK,EAAE,CAAC;AACjD,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,YACT,iCAAiC,cAAc,YAC3C;AAAA,cACE,WAAW,cAAc;AAAA,cACzB,eAAe,cAAc;AAAA,YAC/B,IACA;AAAA,UACN;AAAA,UACA,gBAAgB;AAAA;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,MACA,KAAK,gBAAgB;AACnB,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,MACA,KAAK,YAAY;AACf,0CAAc,KAAK,6BAA6B;AAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAsB,WACpB,KACA,MACAD,YACA,SAMyB;AACzB,oCAAc,KAAK,oBAAoB;AACvC,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC,eAAe,QAAQ;AAAA,IACvB,UAAU,QAAQ;AAAA,EACpB,CAAC;AACD,QAAM,UAAU;AAAA,IACd,UAAU,QAAQ;AAAA,IAClB,WAAAA;AAAA,IACA,QAAQ,QAAQ;AAAA,EAClB;AACA,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,4BAA4B;AAAA,MACvD,MAAM,MAAM,eAAe,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,MACvD,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,QACpB,aAAa,KAAK,qBAAqB;AAAA,MACzC;AAAA,IACF,CAAC;AACD,WAAO,iCAAe,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,EACnD,SAAS,OAAgB;AACvB,mCAAW,KAAK,qCAAqC,QAAQ,GAAG;AAChE,WAAO,UAAM,qCAAuB,KAAK,KAAK;AAAA,EAChD;AACF;AAWA,eAAsB,oBACpB,KACA,UACA,KACA,UACA;AACA,QAAM,YAAQ,8BAAgB,KAAK;AAAA,IACjC,eAAe;AAAA,IACf;AAAA,EACF,CAAC;AACD,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,sCAAsC;AAAA,MACjE,MAAM,KAAK,UAAU;AAAA,QACnB;AAAA,QACA,OAAO,SAAS;AAAA,MAClB,CAAC;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,SAAS,KAAK;AAAA,EACtB,SAAS,OAAgB;AACvB;AAAA,MACE;AAAA,MACA,+CAA+C,MAAM,OAAO;AAAA,IAC9D;AAAA,EACF;AACF;AAEA,eAAsB,mBACpB,KACA,aAKA,SAgBA;AACA,QAAM,EAAE,KAAK,SAAS,IAAI;AAC1B,QAAM,WAAW,KAAK,EAAE,GAAG,SAAS,KAAK,SAAS,CAAC;AAEnD,QAAM,cAA2B;AAAA,IAC/B,gBAAgB,YAAY;AAAA,IAC5B;AAAA,IACA,SAAS,CAAC,CAAC,QAAQ;AAAA,IACnB,QAAQ,CAAC,CAAC,QAAQ;AAAA,IAClB,WAAW,QAAQ;AAAA,IACnB,qBAAqB,QAAQ;AAAA,IAC7B,OAAO,CAAC,CAAC,QAAQ;AAAA,IACjB,iBAAiB,QAAQ;AAAA,IACzB,SAAS,QAAQ,YAAY;AAAA,IAC7B;AAAA,IACA,kBAAkB,QAAQ;AAAA,IAC1B,sBAAsB,CAAC,CAAC,QAAQ;AAAA,EAClC;AACA;AAAA,IACE;AAAA,IACA,gBAAgB,GAAG,MAAM,QAAQ,SAAS,eAAe,EAAE;AAAA,EAC7D;AACA,YAAM,2BAAQ,KAAK,WAAW;AAC9B;AAAA,IACE;AAAA,IACA,GACE,QAAQ,SAAS,wBAAwB,UAC3C,wBAAwB,GAAG;AAAA,EAC7B;AACF;AAEA,eAAsB,WACpB,KACA,SAOA;AACA,MAAI,QAAQ,QAAQ,QAAW;AAC7B;AAAA,EACF;AAEA,QAAM,SACJ,QAAQ,qBAAqB,UAAM,oCAAoB,GAAG,GAAG;AAC/D;AAAA,IACE;AAAA,IACA,YAAY,QAAQ,GAAG,gCAAgC,MAAM,WAC3D,QAAQ,SAAS,eAAe,EAClC;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,QAAQ;AACnB,UAAM,oBAAoB,MAAM,iCAAiC,KAAK;AAAA,MACpE,eAAe,QAAQ;AAAA,MACvB,UAAU,QAAQ;AAAA,IACpB,CAAC;AAED,UAAM,MAAM,EAAE,GAAG,QAAQ,IAAI;AAC7B,QAAI,MAAM,IAAI;AACd,UAAM,aAAS,gCAAU,QAAQ,KAAK;AAAA,MACpC;AAAA,MACA,OAAO;AAAA,MACP,OAAO;AAAA,IACT,CAAC;AACD,QAAI,OAAO,WAAW,GAAG;AACvB,YAAM,IAAI,MAAM;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,IAAI,QAAQ,GAAG;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA,GAAG,QAAQ,SAAS,mBAAmB,KAAK,KAC1C,QAAQ,GACV,gCAAgC,MAAM;AAAA,EACxC;AACF;AAEA,eAAsB,iCACpB,KACA,SACiB;AACjB,QAAM,SAAS,UAAM,2BAAe,KAAK;AAAA,IACvC,GAAG;AAAA,IACH,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM,CAAC;AAAA,EACT,CAAC;AACD,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AACT;", "names": ["zlib", "startPush", "chalk"]}
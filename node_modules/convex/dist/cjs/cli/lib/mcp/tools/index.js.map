{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/index.ts"], "sourcesContent": ["import { ToolSchema } from \"@modelcontextprotocol/sdk/types\";\nimport { Tool } from \"@modelcontextprotocol/sdk/types\";\nimport { RequestContext } from \"../requestContext.js\";\nimport { ZodTypeAny, z } from \"zod\";\nimport zodToJsonSchema from \"zod-to-json-schema\";\nimport { TablesTool } from \"./tables.js\";\nimport { DataTool } from \"./data.js\";\nimport { StatusTool } from \"./status.js\";\nimport { FunctionSpecTool } from \"./functionSpec.js\";\nimport { RunTool } from \"./run.js\";\nimport { EnvListTool, EnvGetTool, EnvSetTool, EnvRemoveTool } from \"./env.js\";\nimport { RunOneoffQueryTool } from \"./runOneoffQuery.js\";\n\nexport type ConvexTool<Input extends ZodTypeAny, Output extends ZodTypeAny> = {\n  name: string;\n  description: string;\n  inputSchema: Input;\n  outputSchema: Output;\n  handler: (\n    ctx: RequestContext,\n    input: z.infer<Input>,\n  ) => Promise<z.infer<Output>>;\n};\n\ntype ToolInput = z.infer<(typeof ToolSchema)[\"shape\"][\"inputSchema\"]>;\n\nexport function mcpTool(tool: ConvexTool<ZodTypeAny, ZodTypeAny>): Tool {\n  return {\n    name: tool.name,\n    description: tool.description,\n    inputSchema: zodToJsonSchema(tool.inputSchema) as ToolInput,\n  };\n}\n\nexport const convexTools: ConvexTool<any, any>[] = [\n  StatusTool,\n  DataTool,\n  TablesTool,\n  FunctionSpecTool,\n  RunTool,\n  EnvListTool,\n  EnvGetTool,\n  EnvSetTool,\n  EnvRemoveTool,\n  RunOneoffQueryTool,\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,gCAA4B;AAC5B,oBAA2B;AAC3B,kBAAyB;AACzB,oBAA2B;AAC3B,0BAAiC;AACjC,iBAAwB;AACxB,iBAAmE;AACnE,4BAAmC;AAe5B,SAAS,QAAQ,MAAgD;AACtE,SAAO;AAAA,IACL,MAAM,KAAK;AAAA,IACX,aAAa,KAAK;AAAA,IAClB,iBAAa,0BAAAA,SAAgB,KAAK,WAAW;AAAA,EAC/C;AACF;AAEO,MAAM,cAAsC;AAAA,EACjD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["zodToJsonSchema"]}
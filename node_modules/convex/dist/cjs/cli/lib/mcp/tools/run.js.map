{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/run.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { ConvexTool } from \"./index.js\";\nimport { loadSelectedDeploymentCredentials } from \"../../api.js\";\nimport { parseArgs, parseFunctionName } from \"../../run.js\";\nimport { readProjectConfig } from \"../../config.js\";\nimport { ConvexHttpClient } from \"../../../../browser/index.js\";\nimport { Value } from \"../../../../values/index.js\";\nimport { Logger } from \"../../../../browser/logging.js\";\nimport { getDeploymentSelection } from \"../../deploymentSelection.js\";\nconst inputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\n      \"Deployment selector (from the status tool) to run the function on.\",\n    ),\n  functionName: z\n    .string()\n    .describe(\n      \"The name of the function to run (e.g. 'path/to/my/module.js:myFunction').\",\n    ),\n  args: z\n    .string()\n    .describe(\n      \"The argument object to pass to the function, JSON-encoded as a string.\",\n    ),\n});\n\nconst outputSchema = z.object({\n  result: z.any().describe(\"The result returned by the function\"),\n  logLines: z\n    .array(z.string())\n    .describe(\"The log lines generated by the function\"),\n});\n\nconst description = `\nRun a Convex function (query, mutation, or action) on your deployment.\n\nReturns the result and any log lines generated by the function.\n`.trim();\n\nexport const RunTool: ConvexTool<typeof inputSchema, typeof outputSchema> = {\n  name: \"run\",\n  description,\n  inputSchema,\n  outputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const metadata = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      metadata,\n      deployment,\n    );\n    const parsedArgs = await parseArgs(ctx, args.args);\n    const { projectConfig } = await readProjectConfig(ctx);\n    const parsedFunctionName = await parseFunctionName(\n      ctx,\n      args.functionName,\n      projectConfig.functions,\n    );\n    const logger = new Logger({ verbose: true });\n    const logLines: string[] = [];\n    logger.addLogLineListener((level, ...args) => {\n      logLines.push(`${level}: ${args.join(\" \")}`);\n    });\n    const client = new ConvexHttpClient(credentials.url, {\n      logger: logger,\n    });\n    client.setAdminAuth(credentials.adminKey);\n    let result: Value;\n    try {\n      result = await client.function(parsedFunctionName, undefined, parsedArgs);\n    } catch (err) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem or env vars\",\n        printedMessage: `Failed to run function \"${args.functionName}\":\\n${(err as Error).toString().trim()}`,\n      });\n    }\n    return {\n      result,\n      logLines,\n    };\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAkB;AAElB,iBAAkD;AAClD,iBAA6C;AAC7C,oBAAkC;AAClC,qBAAiC;AAEjC,qBAAuB;AACvB,iCAAuC;AACvC,MAAM,cAAc,aAAE,OAAO;AAAA,EAC3B,oBAAoB,aACjB,OAAO,EACP;AAAA,IACC;AAAA,EACF;AAAA,EACF,cAAc,aACX,OAAO,EACP;AAAA,IACC;AAAA,EACF;AAAA,EACF,MAAM,aACH,OAAO,EACP;AAAA,IACC;AAAA,EACF;AACJ,CAAC;AAED,MAAM,eAAe,aAAE,OAAO;AAAA,EAC5B,QAAQ,aAAE,IAAI,EAAE,SAAS,qCAAqC;AAAA,EAC9D,UAAU,aACP,MAAM,aAAE,OAAO,CAAC,EAChB,SAAS,yCAAyC;AACvD,CAAC;AAED,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA,EAIlB,KAAK;AAEA,MAAM,UAA+D;AAAA,EAC1E,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,WAAW,UAAM,mDAAuB,KAAK,IAAI,OAAO;AAC9D,UAAM,cAAc,UAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,aAAa,UAAM,sBAAU,KAAK,KAAK,IAAI;AACjD,UAAM,EAAE,cAAc,IAAI,UAAM,iCAAkB,GAAG;AACrD,UAAM,qBAAqB,UAAM;AAAA,MAC/B;AAAA,MACA,KAAK;AAAA,MACL,cAAc;AAAA,IAChB;AACA,UAAM,SAAS,IAAI,sBAAO,EAAE,SAAS,KAAK,CAAC;AAC3C,UAAM,WAAqB,CAAC;AAC5B,WAAO,mBAAmB,CAAC,UAAUA,UAAS;AAC5C,eAAS,KAAK,GAAG,KAAK,KAAKA,MAAK,KAAK,GAAG,CAAC,EAAE;AAAA,IAC7C,CAAC;AACD,UAAM,SAAS,IAAI,gCAAiB,YAAY,KAAK;AAAA,MACnD;AAAA,IACF,CAAC;AACD,WAAO,aAAa,YAAY,QAAQ;AACxC,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,OAAO,SAAS,oBAAoB,QAAW,UAAU;AAAA,IAC1E,SAAS,KAAK;AACZ,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,2BAA2B,KAAK,YAAY;AAAA,EAAQ,IAAc,SAAS,EAAE,KAAK,CAAC;AAAA,MACrG,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": ["args"]}
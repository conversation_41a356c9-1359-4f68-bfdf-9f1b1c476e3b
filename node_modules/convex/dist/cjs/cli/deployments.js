"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var deployments_exports = {};
__export(deployments_exports, {
  deployments: () => deployments
});
module.exports = __toCommonJS(deployments_exports);
var import_extra_typings = require("@commander-js/extra-typings");
var import_config = require("./lib/config.js");
var import_chalk = __toESM(require("chalk"), 1);
var import_utils = require("./lib/utils/utils.js");
var import_context = require("../bundler/context.js");
const deployments = new import_extra_typings.Command("deployments").description("List deployments associated with a project").allowExcessArguments(false).action(async () => {
  const ctx = await (0, import_context.oneoffContext)({
    url: void 0,
    adminKey: void 0,
    envFile: void 0
  });
  const { projectConfig: config } = await (0, import_config.readProjectConfig)(ctx);
  const url = `teams/${config.team}/projects/${config.project}/deployments`;
  (0, import_context.logMessage)(ctx, `Deployments for project ${config.team}/${config.project}`);
  const deployments2 = await (0, import_utils.bigBrainAPI)({
    ctx,
    method: "GET",
    url
  });
  (0, import_context.logOutput)(ctx, deployments2);
  if (deployments2.length === 0) {
    (0, import_context.logError)(ctx, import_chalk.default.yellow(`No deployments exist for project`));
  }
});
//# sourceMappingURL=deployments.js.map

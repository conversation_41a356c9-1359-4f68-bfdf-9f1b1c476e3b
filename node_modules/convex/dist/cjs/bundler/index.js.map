{"version": 3, "sources": ["../../../src/bundler/index.ts"], "sourcesContent": ["import path from \"path\";\nimport chalk from \"chalk\";\nimport esbuild, { BuildFailure } from \"esbuild\";\nimport { parse as parseAST } from \"@babel/parser\";\nimport { Identifier, ImportSpecifier } from \"@babel/types\";\nimport * as Sentry from \"@sentry/node\";\nimport { Filesystem, consistentPathSort } from \"./fs.js\";\nimport { Context, logVerbose, logWarning } from \"./context.js\";\nimport { wasmPlugin } from \"./wasm.js\";\nimport {\n  ExternalPackage,\n  computeExternalPackages,\n  createExternalPlugin,\n  findExactVersionAndDependencies,\n} from \"./external.js\";\nexport { nodeFs, RecordingFs } from \"./fs.js\";\nexport type { Filesystem } from \"./fs.js\";\n\nexport const actionsDir = \"actions\";\n\n// Returns a generator of { isDir, path, depth } for all paths\n// within dirPath in some topological order (not including\n// dirPath itself).\nexport function* walkDir(\n  fs: Filesystem,\n  dirPath: string,\n  depth?: number,\n): Generator<{ isDir: boolean; path: string; depth: number }, void, void> {\n  depth = depth ?? 0;\n  for (const dirEntry of fs.listDir(dirPath).sort(consistentPathSort)) {\n    const childPath = path.join(dirPath, dirEntry.name);\n    if (dirEntry.isDirectory()) {\n      yield { isDir: true, path: childPath, depth };\n      yield* walkDir(fs, childPath, depth + 1);\n    } else if (dirEntry.isFile()) {\n      yield { isDir: false, path: childPath, depth };\n    }\n  }\n}\n\n// Convex specific module environment.\ntype ModuleEnvironment = \"node\" | \"isolate\";\n\nexport interface Bundle {\n  path: string;\n  source: string;\n  sourceMap?: string;\n  environment: ModuleEnvironment;\n}\n\nexport interface BundleHash {\n  path: string;\n  hash: string;\n  environment: ModuleEnvironment;\n}\n\ntype EsBuildResult = esbuild.BuildResult & {\n  outputFiles: esbuild.OutputFile[];\n  // Set of referenced external modules.\n  externalModuleNames: Set<string>;\n  // Set of bundled modules.\n  bundledModuleNames: Set<string>;\n};\n\nasync function doEsbuild(\n  ctx: Context,\n  dir: string,\n  entryPoints: string[],\n  generateSourceMaps: boolean,\n  platform: esbuild.Platform,\n  chunksFolder: string,\n  externalPackages: Map<string, ExternalPackage>,\n  extraConditions: string[],\n): Promise<EsBuildResult> {\n  const external = createExternalPlugin(ctx, externalPackages);\n  try {\n    const result = await esbuild.build({\n      entryPoints,\n      bundle: true,\n      platform: platform,\n      format: \"esm\",\n      target: \"esnext\",\n      jsx: \"automatic\",\n      outdir: \"out\",\n      outbase: dir,\n      conditions: [\"convex\", \"module\", ...extraConditions],\n      // The wasmPlugin should be last so it doesn't run on external modules.\n      plugins: [external.plugin, wasmPlugin],\n      write: false,\n      sourcemap: generateSourceMaps,\n      splitting: true,\n      chunkNames: path.join(chunksFolder, \"[hash]\"),\n      treeShaking: true,\n      minifySyntax: true,\n      minifyIdentifiers: true,\n      // Enabling minifyWhitespace breaks sourcemaps on convex backends.\n      // The sourcemaps produced are valid on https://evanw.github.io/source-map-visualization\n      // but something we're doing (perhaps involving https://github.com/getsentry/rust-sourcemap)\n      // makes everything map to the same line.\n      minifyWhitespace: false, // false is the default, just showing for clarify.\n      keepNames: true,\n      define: {\n        \"process.env.NODE_ENV\": '\"production\"',\n      },\n      metafile: true,\n    });\n\n    for (const [relPath, input] of Object.entries(result.metafile!.inputs)) {\n      // TODO: esbuild outputs paths prefixed with \"(disabled)\"\" when bundling our internal\n      // udf-runtime package. The files do actually exist locally, though.\n      if (\n        relPath.indexOf(\"(disabled):\") !== -1 ||\n        relPath.startsWith(\"wasm-binary:\") ||\n        relPath.startsWith(\"wasm-stub:\")\n      ) {\n        continue;\n      }\n      const absPath = path.resolve(relPath);\n      const st = ctx.fs.stat(absPath);\n      if (st.size !== input.bytes) {\n        logWarning(\n          ctx,\n          `Bundled file ${absPath} changed right after esbuild invocation`,\n        );\n        // Consider this a transient error so we'll try again and hopefully\n        // no files change right after esbuild next time.\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"transient\",\n          printedMessage: null,\n        });\n      }\n      ctx.fs.registerPath(absPath, st);\n    }\n    return {\n      ...result,\n      externalModuleNames: external.externalModuleNames,\n      bundledModuleNames: external.bundledModuleNames,\n    };\n  } catch (e: unknown) {\n    // esbuild sometimes throws a build error instead of returning a result\n    // containing an array of errors. Syntax errors are one of these cases.\n    let recommendUseNode = false;\n    if (isEsbuildBuildError(e)) {\n      for (const error of e.errors) {\n        if (error.location) {\n          const absPath = path.resolve(error.location.file);\n          const st = ctx.fs.stat(absPath);\n          ctx.fs.registerPath(absPath, st);\n        }\n        if (\n          platform !== \"node\" &&\n          !recommendUseNode &&\n          error.notes.some((note) =>\n            note.text.includes(\"Are you trying to bundle for node?\"),\n          )\n        ) {\n          recommendUseNode = true;\n        }\n      }\n    }\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      // We don't print any error because esbuild already printed\n      // all the relevant information.\n      printedMessage: recommendUseNode\n        ? `\\nIt looks like you are using Node APIs from a file without the \"use node\" directive.\\n` +\n          `Split out actions using Node.js APIs like this into a new file only containing actions that uses \"use node\" ` +\n          `so these actions will run in a Node.js environment.\\n` +\n          `For more information see https://docs.convex.dev/functions/runtimes#nodejs-runtime\\n`\n        : null,\n    });\n  }\n}\n\nfunction isEsbuildBuildError(e: any): e is BuildFailure {\n  return (\n    \"errors\" in e &&\n    \"warnings\" in e &&\n    Array.isArray(e.errors) &&\n    Array.isArray(e.warnings)\n  );\n}\n\nexport async function bundle(\n  ctx: Context,\n  dir: string,\n  entryPoints: string[],\n  generateSourceMaps: boolean,\n  platform: esbuild.Platform,\n  chunksFolder = \"_deps\",\n  externalPackagesAllowList: string[] = [],\n  extraConditions: string[] = [],\n): Promise<{\n  modules: Bundle[];\n  externalDependencies: Map<string, string>;\n  bundledModuleNames: Set<string>;\n}> {\n  const availableExternalPackages = await computeExternalPackages(\n    ctx,\n    externalPackagesAllowList,\n  );\n  const result = await doEsbuild(\n    ctx,\n    dir,\n    entryPoints,\n    generateSourceMaps,\n    platform,\n    chunksFolder,\n    availableExternalPackages,\n    extraConditions,\n  );\n  if (result.errors.length) {\n    const errorMessage = result.errors\n      .map((e) => `esbuild error: ${e.text}`)\n      .join(\"\\n\");\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: errorMessage,\n    });\n  }\n  for (const warning of result.warnings) {\n    logWarning(ctx, chalk.yellow(`esbuild warning: ${warning.text}`));\n  }\n  const sourceMaps = new Map();\n  const modules: Bundle[] = [];\n  const environment = platform === \"node\" ? \"node\" : \"isolate\";\n  for (const outputFile of result.outputFiles) {\n    const relPath = path.relative(path.normalize(\"out\"), outputFile.path);\n    if (path.extname(relPath) === \".map\") {\n      sourceMaps.set(relPath, outputFile.text);\n      continue;\n    }\n    const posixRelPath = relPath.split(path.sep).join(path.posix.sep);\n    modules.push({ path: posixRelPath, source: outputFile.text, environment });\n  }\n  for (const module of modules) {\n    const sourceMapPath = module.path + \".map\";\n    const sourceMap = sourceMaps.get(sourceMapPath);\n    if (sourceMap) {\n      module.sourceMap = sourceMap;\n    }\n  }\n\n  return {\n    modules,\n    externalDependencies: await externalPackageVersions(\n      ctx,\n      availableExternalPackages,\n      result.externalModuleNames,\n    ),\n    bundledModuleNames: result.bundledModuleNames,\n  };\n}\n\n// We could return the full list of availableExternalPackages, but this would be\n// installing more packages that we need. Instead, we collect all external\n// dependencies we found during bundling the /convex function, as well as their\n// respective peer and optional dependencies.\nasync function externalPackageVersions(\n  ctx: Context,\n  availableExternalPackages: Map<string, ExternalPackage>,\n  referencedPackages: Set<string>,\n): Promise<Map<string, string>> {\n  const versions = new Map<string, string>();\n  const referencedPackagesQueue = Array.from(referencedPackages.keys());\n\n  for (let i = 0; i < referencedPackagesQueue.length; i++) {\n    const moduleName = referencedPackagesQueue[i];\n    // This assertion is safe because referencedPackages can only contain\n    // packages in availableExternalPackages.\n    const modulePath = availableExternalPackages.get(moduleName)!.path;\n    // Since we don't support lock files and different install commands yet, we\n    // pick up the exact version installed on the local filesystem.\n    const { version, peerAndOptionalDependencies } =\n      await findExactVersionAndDependencies(ctx, moduleName, modulePath);\n    versions.set(moduleName, version);\n\n    for (const dependency of peerAndOptionalDependencies) {\n      if (\n        availableExternalPackages.has(dependency) &&\n        !referencedPackages.has(dependency)\n      ) {\n        referencedPackagesQueue.push(dependency);\n        referencedPackages.add(dependency);\n      }\n    }\n  }\n\n  return versions;\n}\n\nexport async function bundleSchema(\n  ctx: Context,\n  dir: string,\n  extraConditions: string[],\n) {\n  let target = path.resolve(dir, \"schema.ts\");\n  if (!ctx.fs.exists(target)) {\n    target = path.resolve(dir, \"schema.js\");\n  }\n  const result = await bundle(\n    ctx,\n    dir,\n    [target],\n    true,\n    \"browser\",\n    undefined,\n    extraConditions,\n  );\n  return result.modules;\n}\n\nexport async function bundleAuthConfig(ctx: Context, dir: string) {\n  const authConfigPath = path.resolve(dir, \"auth.config.js\");\n  const authConfigTsPath = path.resolve(dir, \"auth.config.ts\");\n  if (ctx.fs.exists(authConfigPath) && ctx.fs.exists(authConfigTsPath)) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `Found both ${authConfigPath} and ${authConfigTsPath}, choose one.`,\n    });\n  }\n  const chosenPath = ctx.fs.exists(authConfigTsPath)\n    ? authConfigTsPath\n    : authConfigPath;\n  if (!ctx.fs.exists(chosenPath)) {\n    return [];\n  }\n  const result = await bundle(ctx, dir, [chosenPath], true, \"browser\");\n  return result.modules;\n}\n\nexport async function doesImportConvexHttpRouter(source: string) {\n  try {\n    const ast = parseAST(source, {\n      sourceType: \"module\",\n      plugins: [\"typescript\"],\n    });\n    return ast.program.body.some((node) => {\n      if (node.type !== \"ImportDeclaration\") return false;\n      return node.specifiers.some((s) => {\n        const specifier = s as ImportSpecifier;\n        const imported = specifier.imported as Identifier;\n        return imported.name === \"httpRouter\";\n      });\n    });\n  } catch {\n    return (\n      source.match(\n        /import\\s*\\{\\s*httpRouter.*\\}\\s*from\\s*\"\\s*convex\\/server\\s*\"/,\n      ) !== null\n    );\n  }\n}\n\nconst ENTRY_POINT_EXTENSIONS = [\n  // ESBuild js loader\n  \".js\",\n  \".mjs\",\n  \".cjs\",\n  // ESBuild ts loader\n  \".ts\",\n  \".tsx\",\n  \".mts\",\n  \".cts\",\n  // ESBuild jsx loader\n  \".jsx\",\n  // ESBuild supports css, text, json, and more but these file types are not\n  // allowed to define entry points.\n];\n\nexport async function entryPoints(\n  ctx: Context,\n  dir: string,\n): Promise<string[]> {\n  const entryPoints = [];\n\n  for (const { isDir, path: fpath, depth } of walkDir(ctx.fs, dir)) {\n    if (isDir) {\n      continue;\n    }\n    const relPath = path.relative(dir, fpath);\n    const parsedPath = path.parse(fpath);\n    const base = parsedPath.base;\n    const extension = parsedPath.ext.toLowerCase();\n\n    if (relPath.startsWith(\"_deps\" + path.sep)) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage: `The path \"${fpath}\" is within the \"_deps\" directory, which is reserved for dependencies. Please move your code to another directory.`,\n      });\n    }\n\n    if (depth === 0 && base.toLowerCase().startsWith(\"https.\")) {\n      const source = ctx.fs.readUtf8File(fpath);\n      if (await doesImportConvexHttpRouter(source))\n        logWarning(\n          ctx,\n          chalk.yellow(\n            `Found ${fpath}. HTTP action routes will not be imported from this file. Did you mean to include http${extension}?`,\n          ),\n        );\n      Sentry.captureMessage(\n        `User code top level directory contains file ${base} which imports httpRouter.`,\n        \"warning\",\n      );\n    }\n\n    // This should match isEntryPoint in the convex eslint plugin.\n    if (!ENTRY_POINT_EXTENSIONS.some((ext) => relPath.endsWith(ext))) {\n      logVerbose(ctx, chalk.yellow(`Skipping non-JS file ${fpath}`));\n    } else if (relPath.startsWith(\"_generated\" + path.sep)) {\n      logVerbose(ctx, chalk.yellow(`Skipping ${fpath}`));\n    } else if (base.startsWith(\".\")) {\n      logVerbose(ctx, chalk.yellow(`Skipping dotfile ${fpath}`));\n    } else if (base.startsWith(\"#\")) {\n      logVerbose(ctx, chalk.yellow(`Skipping likely emacs tempfile ${fpath}`));\n    } else if (base === \"schema.ts\" || base === \"schema.js\") {\n      logVerbose(ctx, chalk.yellow(`Skipping ${fpath}`));\n    } else if ((base.match(/\\./g) || []).length > 1) {\n      // `auth.config.ts` and `convex.config.ts` are important not to bundle.\n      // `*.test.ts` `*.spec.ts` are common in developer code.\n      logVerbose(\n        ctx,\n        chalk.yellow(`Skipping ${fpath} that contains multiple dots`),\n      );\n    } else if (relPath.includes(\" \")) {\n      logVerbose(\n        ctx,\n        chalk.yellow(`Skipping ${relPath} because it contains a space`),\n      );\n    } else {\n      logVerbose(ctx, chalk.green(`Preparing ${fpath}`));\n      entryPoints.push(fpath);\n    }\n  }\n\n  // If using TypeScript, require that at least one line starts with `export` or `import`,\n  // a TypeScript requirement. This prevents confusing type errors from empty .ts files.\n  const nonEmptyEntryPoints = entryPoints.filter((fpath) => {\n    // This check only makes sense for TypeScript files\n    if (!fpath.endsWith(\".ts\") && !fpath.endsWith(\".tsx\")) {\n      return true;\n    }\n    const contents = ctx.fs.readUtf8File(fpath);\n    if (/^\\s{0,100}(import|export)/m.test(contents)) {\n      return true;\n    }\n    logVerbose(\n      ctx,\n      chalk.yellow(\n        `Skipping ${fpath} because it has no export or import to make it a valid TypeScript module`,\n      ),\n    );\n  });\n\n  return nonEmptyEntryPoints;\n}\n\n// A fallback regex in case we fail to parse the AST.\nexport const useNodeDirectiveRegex = /^\\s*(\"|')use node(\"|');?\\s*$/;\n\nfunction hasUseNodeDirective(ctx: Context, fpath: string): boolean {\n  // Do a quick check for the exact string. If it doesn't exist, don't\n  // bother parsing.\n  const source = ctx.fs.readUtf8File(fpath);\n  if (source.indexOf(\"use node\") === -1) {\n    return false;\n  }\n\n  // We parse the AST here to extract the \"use node\" declaration. This is more\n  // robust than doing a regex. We only use regex as a fallback.\n  try {\n    const ast = parseAST(source, {\n      // parse in strict mode and allow module declarations\n      sourceType: \"module\",\n\n      // esbuild supports jsx and typescript by default. Allow the same plugins\n      // here too.\n      plugins: [\"jsx\", \"typescript\"],\n    });\n    return ast.program.directives\n      .map((d) => d.value.value)\n      .includes(\"use node\");\n  } catch (error: any) {\n    // Given that we have failed to parse, we are most likely going to fail in\n    // the esbuild step, which seem to return better formatted error messages.\n    // We don't throw here and fallback to regex.\n    let lineMatches = false;\n    for (const line of source.split(\"\\n\")) {\n      if (line.match(useNodeDirectiveRegex)) {\n        lineMatches = true;\n        break;\n      }\n    }\n\n    // Log that we failed to parse in verbose node if we need this for debugging.\n    logVerbose(\n      ctx,\n      `Failed to parse ${fpath}. Use node is set to ${lineMatches} based on regex. Parse error: ${error.toString()}.`,\n    );\n\n    return lineMatches;\n  }\n}\n\nexport function mustBeIsolate(relPath: string): boolean {\n  // Check if the path without extension matches any of the static paths.\n  return [\"http\", \"crons\", \"schema\", \"auth.config\"].includes(\n    relPath.replace(/\\.[^/.]+$/, \"\"),\n  );\n}\n\nasync function determineEnvironment(\n  ctx: Context,\n  dir: string,\n  fpath: string,\n): Promise<ModuleEnvironment> {\n  const relPath = path.relative(dir, fpath);\n\n  const useNodeDirectiveFound = hasUseNodeDirective(ctx, fpath);\n  if (useNodeDirectiveFound) {\n    if (mustBeIsolate(relPath)) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage: `\"use node\" directive is not allowed for ${relPath}.`,\n      });\n    }\n    return \"node\";\n  }\n\n  const actionsPrefix = actionsDir + path.sep;\n  if (relPath.startsWith(actionsPrefix)) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `${relPath} is in /actions subfolder but has no \"use node\"; directive. You can now define actions in any folder and indicate they should run in node by adding \"use node\" directive. /actions is a deprecated way to choose Node.js environment, and we require \"use node\" for all files within that folder to avoid unexpected errors during the migration. See https://docs.convex.dev/functions/actions for more details`,\n    });\n  }\n\n  return \"isolate\";\n}\n\nexport async function entryPointsByEnvironment(ctx: Context, dir: string) {\n  const isolate = [];\n  const node = [];\n  for (const entryPoint of await entryPoints(ctx, dir)) {\n    const environment = await determineEnvironment(ctx, dir, entryPoint);\n    if (environment === \"node\") {\n      node.push(entryPoint);\n    } else {\n      isolate.push(entryPoint);\n    }\n  }\n\n  return { isolate, node };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AACjB,mBAAkB;AAClB,qBAAsC;AACtC,oBAAkC;AAElC,aAAwB;AACxB,gBAA+C;AAC/C,qBAAgD;AAChD,kBAA2B;AAC3B,sBAKO;AACP,IAAAA,aAAoC;AAG7B,MAAM,aAAa;AAKnB,UAAU,QACf,IACA,SACA,OACwE;AACxE,UAAQ,SAAS;AACjB,aAAW,YAAY,GAAG,QAAQ,OAAO,EAAE,KAAK,4BAAkB,GAAG;AACnE,UAAM,YAAY,YAAAC,QAAK,KAAK,SAAS,SAAS,IAAI;AAClD,QAAI,SAAS,YAAY,GAAG;AAC1B,YAAM,EAAE,OAAO,MAAM,MAAM,WAAW,MAAM;AAC5C,aAAO,QAAQ,IAAI,WAAW,QAAQ,CAAC;AAAA,IACzC,WAAW,SAAS,OAAO,GAAG;AAC5B,YAAM,EAAE,OAAO,OAAO,MAAM,WAAW,MAAM;AAAA,IAC/C;AAAA,EACF;AACF;AA0BA,eAAe,UACb,KACA,KACAC,cACA,oBACA,UACA,cACA,kBACA,iBACwB;AACxB,QAAM,eAAW,sCAAqB,KAAK,gBAAgB;AAC3D,MAAI;AACF,UAAM,SAAS,MAAM,eAAAC,QAAQ,MAAM;AAAA,MACjC,aAAAD;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,YAAY,CAAC,UAAU,UAAU,GAAG,eAAe;AAAA;AAAA,MAEnD,SAAS,CAAC,SAAS,QAAQ,sBAAU;AAAA,MACrC,OAAO;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY,YAAAD,QAAK,KAAK,cAAc,QAAQ;AAAA,MAC5C,aAAa;AAAA,MACb,cAAc;AAAA,MACd,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnB,kBAAkB;AAAA;AAAA,MAClB,WAAW;AAAA,MACX,QAAQ;AAAA,QACN,wBAAwB;AAAA,MAC1B;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAED,eAAW,CAAC,SAAS,KAAK,KAAK,OAAO,QAAQ,OAAO,SAAU,MAAM,GAAG;AAGtE,UACE,QAAQ,QAAQ,aAAa,MAAM,MACnC,QAAQ,WAAW,cAAc,KACjC,QAAQ,WAAW,YAAY,GAC/B;AACA;AAAA,MACF;AACA,YAAM,UAAU,YAAAA,QAAK,QAAQ,OAAO;AACpC,YAAM,KAAK,IAAI,GAAG,KAAK,OAAO;AAC9B,UAAI,GAAG,SAAS,MAAM,OAAO;AAC3B;AAAA,UACE;AAAA,UACA,gBAAgB,OAAO;AAAA,QACzB;AAGA,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AACA,UAAI,GAAG,aAAa,SAAS,EAAE;AAAA,IACjC;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,qBAAqB,SAAS;AAAA,MAC9B,oBAAoB,SAAS;AAAA,IAC/B;AAAA,EACF,SAAS,GAAY;AAGnB,QAAI,mBAAmB;AACvB,QAAI,oBAAoB,CAAC,GAAG;AAC1B,iBAAW,SAAS,EAAE,QAAQ;AAC5B,YAAI,MAAM,UAAU;AAClB,gBAAM,UAAU,YAAAA,QAAK,QAAQ,MAAM,SAAS,IAAI;AAChD,gBAAM,KAAK,IAAI,GAAG,KAAK,OAAO;AAC9B,cAAI,GAAG,aAAa,SAAS,EAAE;AAAA,QACjC;AACA,YACE,aAAa,UACb,CAAC,oBACD,MAAM,MAAM;AAAA,UAAK,CAAC,SAChB,KAAK,KAAK,SAAS,oCAAoC;AAAA,QACzD,GACA;AACA,6BAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA;AAAA;AAAA,MAGX,gBAAgB,mBACZ;AAAA;AAAA;AAAA;AAAA,IAIA;AAAA,IACN,CAAC;AAAA,EACH;AACF;AAEA,SAAS,oBAAoB,GAA2B;AACtD,SACE,YAAY,KACZ,cAAc,KACd,MAAM,QAAQ,EAAE,MAAM,KACtB,MAAM,QAAQ,EAAE,QAAQ;AAE5B;AAEA,eAAsB,OACpB,KACA,KACAC,cACA,oBACA,UACA,eAAe,SACf,4BAAsC,CAAC,GACvC,kBAA4B,CAAC,GAK5B;AACD,QAAM,4BAA4B,UAAM;AAAA,IACtC;AAAA,IACA;AAAA,EACF;AACA,QAAM,SAAS,MAAM;AAAA,IACnB;AAAA,IACA;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,OAAO,OAAO,QAAQ;AACxB,UAAM,eAAe,OAAO,OACzB,IAAI,CAAC,MAAM,kBAAkB,EAAE,IAAI,EAAE,EACrC,KAAK,IAAI;AACZ,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,aAAW,WAAW,OAAO,UAAU;AACrC,mCAAW,KAAK,aAAAE,QAAM,OAAO,oBAAoB,QAAQ,IAAI,EAAE,CAAC;AAAA,EAClE;AACA,QAAM,aAAa,oBAAI,IAAI;AAC3B,QAAM,UAAoB,CAAC;AAC3B,QAAM,cAAc,aAAa,SAAS,SAAS;AACnD,aAAW,cAAc,OAAO,aAAa;AAC3C,UAAM,UAAU,YAAAH,QAAK,SAAS,YAAAA,QAAK,UAAU,KAAK,GAAG,WAAW,IAAI;AACpE,QAAI,YAAAA,QAAK,QAAQ,OAAO,MAAM,QAAQ;AACpC,iBAAW,IAAI,SAAS,WAAW,IAAI;AACvC;AAAA,IACF;AACA,UAAM,eAAe,QAAQ,MAAM,YAAAA,QAAK,GAAG,EAAE,KAAK,YAAAA,QAAK,MAAM,GAAG;AAChE,YAAQ,KAAK,EAAE,MAAM,cAAc,QAAQ,WAAW,MAAM,YAAY,CAAC;AAAA,EAC3E;AACA,aAAWI,WAAU,SAAS;AAC5B,UAAM,gBAAgBA,QAAO,OAAO;AACpC,UAAM,YAAY,WAAW,IAAI,aAAa;AAC9C,QAAI,WAAW;AACb,MAAAA,QAAO,YAAY;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA,sBAAsB,MAAM;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB,OAAO;AAAA,EAC7B;AACF;AAMA,eAAe,wBACb,KACA,2BACA,oBAC8B;AAC9B,QAAM,WAAW,oBAAI,IAAoB;AACzC,QAAM,0BAA0B,MAAM,KAAK,mBAAmB,KAAK,CAAC;AAEpE,WAAS,IAAI,GAAG,IAAI,wBAAwB,QAAQ,KAAK;AACvD,UAAM,aAAa,wBAAwB,CAAC;AAG5C,UAAM,aAAa,0BAA0B,IAAI,UAAU,EAAG;AAG9D,UAAM,EAAE,SAAS,4BAA4B,IAC3C,UAAM,iDAAgC,KAAK,YAAY,UAAU;AACnE,aAAS,IAAI,YAAY,OAAO;AAEhC,eAAW,cAAc,6BAA6B;AACpD,UACE,0BAA0B,IAAI,UAAU,KACxC,CAAC,mBAAmB,IAAI,UAAU,GAClC;AACA,gCAAwB,KAAK,UAAU;AACvC,2BAAmB,IAAI,UAAU;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,eAAsB,aACpB,KACA,KACA,iBACA;AACA,MAAI,SAAS,YAAAJ,QAAK,QAAQ,KAAK,WAAW;AAC1C,MAAI,CAAC,IAAI,GAAG,OAAO,MAAM,GAAG;AAC1B,aAAS,YAAAA,QAAK,QAAQ,KAAK,WAAW;AAAA,EACxC;AACA,QAAM,SAAS,MAAM;AAAA,IACnB;AAAA,IACA;AAAA,IACA,CAAC,MAAM;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,OAAO;AAChB;AAEA,eAAsB,iBAAiB,KAAc,KAAa;AAChE,QAAM,iBAAiB,YAAAA,QAAK,QAAQ,KAAK,gBAAgB;AACzD,QAAM,mBAAmB,YAAAA,QAAK,QAAQ,KAAK,gBAAgB;AAC3D,MAAI,IAAI,GAAG,OAAO,cAAc,KAAK,IAAI,GAAG,OAAO,gBAAgB,GAAG;AACpE,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,cAAc,cAAc,QAAQ,gBAAgB;AAAA,IACtE,CAAC;AAAA,EACH;AACA,QAAM,aAAa,IAAI,GAAG,OAAO,gBAAgB,IAC7C,mBACA;AACJ,MAAI,CAAC,IAAI,GAAG,OAAO,UAAU,GAAG;AAC9B,WAAO,CAAC;AAAA,EACV;AACA,QAAM,SAAS,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM,SAAS;AACnE,SAAO,OAAO;AAChB;AAEA,eAAsB,2BAA2B,QAAgB;AAC/D,MAAI;AACF,UAAM,UAAM,cAAAK,OAAS,QAAQ;AAAA,MAC3B,YAAY;AAAA,MACZ,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AACD,WAAO,IAAI,QAAQ,KAAK,KAAK,CAAC,SAAS;AACrC,UAAI,KAAK,SAAS,oBAAqB,QAAO;AAC9C,aAAO,KAAK,WAAW,KAAK,CAAC,MAAM;AACjC,cAAM,YAAY;AAClB,cAAM,WAAW,UAAU;AAC3B,eAAO,SAAS,SAAS;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAAA,EACH,QAAQ;AACN,WACE,OAAO;AAAA,MACL;AAAA,IACF,MAAM;AAAA,EAEV;AACF;AAEA,MAAM,yBAAyB;AAAA;AAAA,EAE7B;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA;AAGF;AAEA,eAAsB,YACpB,KACA,KACmB;AACnB,QAAMJ,eAAc,CAAC;AAErB,aAAW,EAAE,OAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,IAAI,IAAI,GAAG,GAAG;AAChE,QAAI,OAAO;AACT;AAAA,IACF;AACA,UAAM,UAAU,YAAAD,QAAK,SAAS,KAAK,KAAK;AACxC,UAAM,aAAa,YAAAA,QAAK,MAAM,KAAK;AACnC,UAAM,OAAO,WAAW;AACxB,UAAM,YAAY,WAAW,IAAI,YAAY;AAE7C,QAAI,QAAQ,WAAW,UAAU,YAAAA,QAAK,GAAG,GAAG;AAC1C,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,aAAa,KAAK;AAAA,MACpC,CAAC;AAAA,IACH;AAEA,QAAI,UAAU,KAAK,KAAK,YAAY,EAAE,WAAW,QAAQ,GAAG;AAC1D,YAAM,SAAS,IAAI,GAAG,aAAa,KAAK;AACxC,UAAI,MAAM,2BAA2B,MAAM;AACzC;AAAA,UACE;AAAA,UACA,aAAAG,QAAM;AAAA,YACJ,SAAS,KAAK,yFAAyF,SAAS;AAAA,UAClH;AAAA,QACF;AACF,aAAO;AAAA,QACL,+CAA+C,IAAI;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAGA,QAAI,CAAC,uBAAuB,KAAK,CAAC,QAAQ,QAAQ,SAAS,GAAG,CAAC,GAAG;AAChE,qCAAW,KAAK,aAAAA,QAAM,OAAO,wBAAwB,KAAK,EAAE,CAAC;AAAA,IAC/D,WAAW,QAAQ,WAAW,eAAe,YAAAH,QAAK,GAAG,GAAG;AACtD,qCAAW,KAAK,aAAAG,QAAM,OAAO,YAAY,KAAK,EAAE,CAAC;AAAA,IACnD,WAAW,KAAK,WAAW,GAAG,GAAG;AAC/B,qCAAW,KAAK,aAAAA,QAAM,OAAO,oBAAoB,KAAK,EAAE,CAAC;AAAA,IAC3D,WAAW,KAAK,WAAW,GAAG,GAAG;AAC/B,qCAAW,KAAK,aAAAA,QAAM,OAAO,kCAAkC,KAAK,EAAE,CAAC;AAAA,IACzE,WAAW,SAAS,eAAe,SAAS,aAAa;AACvD,qCAAW,KAAK,aAAAA,QAAM,OAAO,YAAY,KAAK,EAAE,CAAC;AAAA,IACnD,YAAY,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG;AAG/C;AAAA,QACE;AAAA,QACA,aAAAA,QAAM,OAAO,YAAY,KAAK,8BAA8B;AAAA,MAC9D;AAAA,IACF,WAAW,QAAQ,SAAS,GAAG,GAAG;AAChC;AAAA,QACE;AAAA,QACA,aAAAA,QAAM,OAAO,YAAY,OAAO,8BAA8B;AAAA,MAChE;AAAA,IACF,OAAO;AACL,qCAAW,KAAK,aAAAA,QAAM,MAAM,aAAa,KAAK,EAAE,CAAC;AACjD,MAAAF,aAAY,KAAK,KAAK;AAAA,IACxB;AAAA,EACF;AAIA,QAAM,sBAAsBA,aAAY,OAAO,CAAC,UAAU;AAExD,QAAI,CAAC,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,SAAS,MAAM,GAAG;AACrD,aAAO;AAAA,IACT;AACA,UAAM,WAAW,IAAI,GAAG,aAAa,KAAK;AAC1C,QAAI,6BAA6B,KAAK,QAAQ,GAAG;AAC/C,aAAO;AAAA,IACT;AACA;AAAA,MACE;AAAA,MACA,aAAAE,QAAM;AAAA,QACJ,YAAY,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAGO,MAAM,wBAAwB;AAErC,SAAS,oBAAoB,KAAc,OAAwB;AAGjE,QAAM,SAAS,IAAI,GAAG,aAAa,KAAK;AACxC,MAAI,OAAO,QAAQ,UAAU,MAAM,IAAI;AACrC,WAAO;AAAA,EACT;AAIA,MAAI;AACF,UAAM,UAAM,cAAAE,OAAS,QAAQ;AAAA;AAAA,MAE3B,YAAY;AAAA;AAAA;AAAA,MAIZ,SAAS,CAAC,OAAO,YAAY;AAAA,IAC/B,CAAC;AACD,WAAO,IAAI,QAAQ,WAChB,IAAI,CAAC,MAAM,EAAE,MAAM,KAAK,EACxB,SAAS,UAAU;AAAA,EACxB,SAAS,OAAY;AAInB,QAAI,cAAc;AAClB,eAAW,QAAQ,OAAO,MAAM,IAAI,GAAG;AACrC,UAAI,KAAK,MAAM,qBAAqB,GAAG;AACrC,sBAAc;AACd;AAAA,MACF;AAAA,IACF;AAGA;AAAA,MACE;AAAA,MACA,mBAAmB,KAAK,wBAAwB,WAAW,iCAAiC,MAAM,SAAS,CAAC;AAAA,IAC9G;AAEA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,cAAc,SAA0B;AAEtD,SAAO,CAAC,QAAQ,SAAS,UAAU,aAAa,EAAE;AAAA,IAChD,QAAQ,QAAQ,aAAa,EAAE;AAAA,EACjC;AACF;AAEA,eAAe,qBACb,KACA,KACA,OAC4B;AAC5B,QAAM,UAAU,YAAAL,QAAK,SAAS,KAAK,KAAK;AAExC,QAAM,wBAAwB,oBAAoB,KAAK,KAAK;AAC5D,MAAI,uBAAuB;AACzB,QAAI,cAAc,OAAO,GAAG;AAC1B,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,2CAA2C,OAAO;AAAA,MACpE,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAEA,QAAM,gBAAgB,aAAa,YAAAA,QAAK;AACxC,MAAI,QAAQ,WAAW,aAAa,GAAG;AACrC,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,GAAG,OAAO;AAAA,IAC5B,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,eAAsB,yBAAyB,KAAc,KAAa;AACxE,QAAM,UAAU,CAAC;AACjB,QAAM,OAAO,CAAC;AACd,aAAW,cAAc,MAAM,YAAY,KAAK,GAAG,GAAG;AACpD,UAAM,cAAc,MAAM,qBAAqB,KAAK,KAAK,UAAU;AACnE,QAAI,gBAAgB,QAAQ;AAC1B,WAAK,KAAK,UAAU;AAAA,IACtB,OAAO;AACL,cAAQ,KAAK,UAAU;AAAA,IACzB;AAAA,EACF;AAEA,SAAO,EAAE,SAAS,KAAK;AACzB;", "names": ["import_fs", "path", "entryPoints", "esbuild", "chalk", "module", "parseAST"]}
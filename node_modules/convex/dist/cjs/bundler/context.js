"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
var context_exports = {};
__export(context_exports, {
  changeSpinner: () => changeSpinner,
  logError: () => logError,
  logFailure: () => logFailure,
  logFinishedStep: () => logFinishedStep,
  logMessage: () => logMessage,
  logOutput: () => logOutput,
  logVerbose: () => logVerbose,
  logWarning: () => logWarning,
  oneoffContext: () => oneoffContext,
  showSpinner: () => showSpinner,
  showSpinnerIfSlow: () => showSpinnerIfSlow,
  startLogProgress: () => startLogProgress,
  stopSpinner: () => stopSpinner
});
module.exports = __toCommonJS(context_exports);
var Sentry = __toESM(require("@sentry/node"), 1);
var import_chalk = __toESM(require("chalk"), 1);
var import_ora = __toESM(require("ora"), 1);
var import_fs = require("./fs.js");
var import_util = require("util");
var import_progress = __toESM(require("progress"), 1);
var import_deploymentSelection = require("../cli/lib/deploymentSelection.js");
async function flushAndExit(exitCode, err) {
  if (err) {
    Sentry.captureException(err);
  }
  await Sentry.close();
  return process.exit(exitCode);
}
class OneoffContextImpl {
  constructor() {
    __publicField(this, "_cleanupFns", {});
    __publicField(this, "fs", import_fs.nodeFs);
    __publicField(this, "deprecationMessagePrinted", false);
    __publicField(this, "spinner");
    __publicField(this, "_bigBrainAuth", null);
    __publicField(this, "crash", async (args) => {
      if (args.printedMessage !== null) {
        logFailure(this, args.printedMessage);
      }
      return await this.flushAndExit(args.exitCode, args.errForSentry);
    });
    __publicField(this, "flushAndExit", async (exitCode, err) => {
      logVerbose(this, "Flushing and exiting, error:", err);
      if (err) {
        logVerbose(this, err.stack);
      }
      const cleanupFns = this._cleanupFns;
      this._cleanupFns = {};
      const fns = Object.values(cleanupFns);
      logVerbose(this, `Running ${fns.length} cleanup functions`);
      for (const fn of fns) {
        await fn(exitCode, err);
      }
      logVerbose(this, "All cleanup functions ran");
      return flushAndExit(exitCode, err);
    });
  }
  registerCleanup(fn) {
    const handle = Math.random().toString(36).slice(2);
    this._cleanupFns[handle] = fn;
    return handle;
  }
  removeCleanup(handle) {
    const value = this._cleanupFns[handle];
    delete this._cleanupFns[handle];
    return value ?? null;
  }
  bigBrainAuth() {
    return this._bigBrainAuth;
  }
  _updateBigBrainAuth(auth) {
    logVerbose(this, `Updating big brain auth to ${auth?.kind ?? "null"}`);
    this._bigBrainAuth = auth;
  }
}
const oneoffContext = async (args) => {
  const ctx = new OneoffContextImpl();
  await (0, import_deploymentSelection.initializeBigBrainAuth)(ctx, {
    url: args.url,
    adminKey: args.adminKey,
    envFile: args.envFile
  });
  return ctx;
};
function logToStderr(...args) {
  process.stderr.write(`${(0, import_util.format)(...args)}
`);
}
function logError(ctx, message) {
  ctx.spinner?.clear();
  logToStderr(message);
}
function logWarning(ctx, ...logged) {
  ctx.spinner?.clear();
  logToStderr(...logged);
}
function logMessage(ctx, ...logged) {
  ctx.spinner?.clear();
  logToStderr(...logged);
}
function logOutput(ctx, ...logged) {
  ctx.spinner?.clear();
  console.log(...logged);
}
function logVerbose(ctx, ...logged) {
  if (process.env.CONVEX_VERBOSE) {
    logMessage(ctx, `[verbose] ${(/* @__PURE__ */ new Date()).toISOString()}`, ...logged);
  }
}
function startLogProgress(ctx, format2, progressBarOptions) {
  ctx.spinner?.clear();
  return new import_progress.default(format2, progressBarOptions);
}
function showSpinner(ctx, message) {
  ctx.spinner?.stop();
  ctx.spinner = (0, import_ora.default)({
    // Add newline to prevent clobbering when a message
    // we can't pipe through `logMessage` et al gets printed
    text: message + "\n",
    stream: process.stderr,
    // hideCursor: true doesn't work with `tsx`.
    // see https://github.com/tapjs/signal-exit/issues/49#issuecomment-1459408082
    // See CX-6822 for an issue to bring back cursor hiding, probably by upgrading libraries.
    hideCursor: process.env.CONVEX_RUNNING_LIVE_IN_MONOREPO ? false : true
  }).start();
}
function changeSpinner(ctx, message) {
  if (ctx.spinner) {
    ctx.spinner.text = message + "\n";
  } else {
    logToStderr(message);
  }
}
function logFailure(ctx, message) {
  if (ctx.spinner) {
    ctx.spinner.fail(message);
    ctx.spinner = void 0;
  } else {
    logToStderr(`${import_chalk.default.red(`\u2716`)} ${message}`);
  }
}
function logFinishedStep(ctx, message) {
  if (ctx.spinner) {
    ctx.spinner.succeed(message);
    ctx.spinner = void 0;
  } else {
    logToStderr(`${import_chalk.default.green(`\u2714`)} ${message}`);
  }
}
function stopSpinner(ctx) {
  if (ctx.spinner) {
    ctx.spinner.stop();
    ctx.spinner = void 0;
  }
}
async function showSpinnerIfSlow(ctx, message, delayMs, fn) {
  const timeout = setTimeout(() => {
    showSpinner(ctx, message);
  }, delayMs);
  await fn();
  clearTimeout(timeout);
}
//# sourceMappingURL=context.js.map

{"version": 3, "sources": ["../../../../src/browser/sync/metrics.ts"], "sourcesContent": ["// Marks share a global namespace with other developer code.\nconst markNames = [\n  \"convexClientConstructed\",\n  \"convexWebSocketOpen\",\n  \"convexFirstMessageReceived\",\n] as const;\nexport type MarkName = (typeof markNames)[number];\n\n// Mark details are not reported to the server.\ntype MarkDetail = {\n  sessionId: string;\n};\n\n// `PerformanceMark`s are efficient and show up in browser's performance\n// timeline. They can be cleared with `performance.clearMarks()`.\n// This is a memory leak, but a worthwhile one: automatic\n// cleanup would make in-browser debugging more difficult.\nexport function mark(name: MarkName, sessionId: string) {\n  const detail: MarkDetail = { sessionId };\n  // `performance` APIs exists in browsers, Node.js, Deno, and more but it\n  // is not required by the Convex client.\n  if (typeof performance === \"undefined\" || !performance.mark) return;\n  performance.mark(name, { detail });\n}\n\n// `PerfomanceMark` has a built-in toJSON() but the return type varies\n// between implementations, e.g. Node.js returns details but Chrome does not.\nfunction performanceMarkToJson(mark: PerformanceMark): Mark<PERSON><PERSON> {\n  // Remove \"convex\" prefix\n  let name = mark.name.slice(\"convex\".length);\n  // lowercase the first letter\n  name = name.charAt(0).toLowerCase() + name.slice(1);\n  return {\n    name,\n    startTime: mark.startTime,\n  };\n}\n\n// Similar to the return type of `PerformanceMark.toJson()`.\nexport type MarkJson = {\n  name: string;\n  // `startTime` is in milliseconds since the time origin like `performance.now()`.\n  // https://developer.mozilla.org/en-US/docs/Web/API/DOMHighResTimeStamp#the_time_origin\n  startTime: number;\n};\n\nexport function getMarksReport(sessionId: string): MarkJson[] {\n  if (typeof performance === \"undefined\" || !performance.getEntriesByName) {\n    return [];\n  }\n  const allMarks: PerformanceMark[] = [];\n  for (const name of markNames) {\n    const marks = (\n      performance\n        .getEntriesByName(name)\n        .filter((entry) => entry.entryType === \"mark\") as PerformanceMark[]\n    ).filter((mark) => mark.detail.sessionId === sessionId);\n    allMarks.push(...marks);\n  }\n  return allMarks.map(performanceMarkToJson);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,MAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AACF;AAYO,SAAS,KAAK,MAAgB,WAAmB;AACtD,QAAM,SAAqB,EAAE,UAAU;AAGvC,MAAI,OAAO,gBAAgB,eAAe,CAAC,YAAY,KAAM;AAC7D,cAAY,KAAK,MAAM,EAAE,OAAO,CAAC;AACnC;AAIA,SAAS,sBAAsBA,OAAiC;AAE9D,MAAI,OAAOA,MAAK,KAAK,MAAM,SAAS,MAAM;AAE1C,SAAO,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAClD,SAAO;AAAA,IACL;AAAA,IACA,WAAWA,MAAK;AAAA,EAClB;AACF;AAUO,SAAS,eAAe,WAA+B;AAC5D,MAAI,OAAO,gBAAgB,eAAe,CAAC,YAAY,kBAAkB;AACvE,WAAO,CAAC;AAAA,EACV;AACA,QAAM,WAA8B,CAAC;AACrC,aAAW,QAAQ,WAAW;AAC5B,UAAM,QACJ,YACG,iBAAiB,IAAI,EACrB,OAAO,CAAC,UAAU,MAAM,cAAc,MAAM,EAC/C,OAAO,CAACA,UAASA,MAAK,OAAO,cAAc,SAAS;AACtD,aAAS,KAAK,GAAG,KAAK;AAAA,EACxB;AACA,SAAO,SAAS,IAAI,qBAAqB;AAC3C;", "names": ["mark"]}
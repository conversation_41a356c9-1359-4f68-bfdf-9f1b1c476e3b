{"version": 3, "sources": ["../../../../src/browser/sync/protocol.ts"], "sourcesContent": ["import type { UserIdentityAttributes } from \"../../server/authentication.js\";\nexport type { UserIdentityAttributes } from \"../../server/authentication.js\";\nimport { JSONValue, Base64 } from \"../../values/index.js\";\nimport { Long } from \"../long.js\";\n\n/**\n * Shared schema\n */\n\nexport function u64ToLong(encoded: EncodedU64): U64 {\n  const integerBytes = Base64.toByteArray(encoded);\n  return Long.fromBytesLE(Array.from(integerBytes));\n}\n\nexport function longToU64(raw: U64): EncodedU64 {\n  const integerBytes = new Uint8Array(raw.toBytesLE());\n  return Base64.fromByteArray(integerBytes);\n}\n\nexport function parseServerMessage(\n  encoded: EncodedServerMessage,\n): ServerMessage {\n  switch (encoded.type) {\n    case \"FatalError\":\n    case \"AuthError\":\n    case \"ActionResponse\":\n    case \"Ping\": {\n      return { ...encoded };\n    }\n    case \"MutationResponse\": {\n      if (encoded.success) {\n        return { ...encoded, ts: u64ToLong(encoded.ts) };\n      } else {\n        return { ...encoded };\n      }\n    }\n    case \"Transition\": {\n      return {\n        ...encoded,\n        startVersion: {\n          ...encoded.startVersion,\n          ts: u64ToLong(encoded.startVersion.ts),\n        },\n        endVersion: {\n          ...encoded.endVersion,\n          ts: u64ToLong(encoded.endVersion.ts),\n        },\n      };\n    }\n    default: {\n      const _exhaustivenessCheck: never = encoded;\n    }\n  }\n  return undefined as never;\n}\n\nexport function encodeClientMessage(\n  message: ClientMessage,\n): EncodedClientMessage {\n  switch (message.type) {\n    case \"Authenticate\":\n    case \"ModifyQuerySet\":\n    case \"Mutation\":\n    case \"Action\":\n    case \"Event\": {\n      return { ...message };\n    }\n    case \"Connect\": {\n      if (message.maxObservedTimestamp !== undefined) {\n        return {\n          ...message,\n          maxObservedTimestamp: longToU64(message.maxObservedTimestamp),\n        };\n      } else {\n        return { ...message, maxObservedTimestamp: undefined };\n      }\n    }\n    default: {\n      const _exhaustivenessCheck: never = message;\n    }\n  }\n  return undefined as never;\n}\n\ntype U64 = Long;\ntype EncodedU64 = string;\n\n/**\n * Unique nonnegative integer identifying a single query.\n */\nexport type QueryId = number; // nonnegative int\n\nexport type QuerySetVersion = number; // nonnegative int\n\nexport type RequestId = number; // nonnegative int\n\nexport type IdentityVersion = number; // nonnegative int\n\n/**\n * A serialized representation of decisions made during a query's execution.\n *\n * A journal is produced when a query function first executes and is re-used\n * when a query is re-executed.\n *\n * Currently this is used to store pagination end cursors to ensure\n * that pages of paginated queries will always end at the same cursor. This\n * enables gapless, reactive pagination.\n *\n * `null` is used to represent empty journals.\n * @public\n */\nexport type QueryJournal = string | null;\n\n/**\n * Client message schema\n */\n\ntype Connect = {\n  type: \"Connect\";\n  sessionId: string;\n  connectionCount: number;\n  lastCloseReason: string | null;\n  maxObservedTimestamp?: TS;\n};\n\nexport type AddQuery = {\n  type: \"Add\";\n  queryId: QueryId;\n  udfPath: string;\n  args: JSONValue[];\n  journal?: QueryJournal;\n  /**\n   * @internal\n   */\n  componentPath?: string;\n};\n\nexport type RemoveQuery = {\n  type: \"Remove\";\n  queryId: QueryId;\n};\n\nexport type QuerySetModification = {\n  type: \"ModifyQuerySet\";\n  baseVersion: QuerySetVersion;\n  newVersion: QuerySetVersion;\n  modifications: (AddQuery | RemoveQuery)[];\n};\n\nexport type MutationRequest = {\n  type: \"Mutation\";\n  requestId: RequestId;\n  udfPath: string;\n  args: JSONValue[];\n  // Execute the mutation on a specific component.\n  // Only admin auth is allowed to run mutations on non-root components.\n  componentPath?: string;\n};\n\nexport type ActionRequest = {\n  type: \"Action\";\n  requestId: RequestId;\n  udfPath: string;\n  args: JSONValue[];\n  // Execute the action on a specific component.\n  // Only admin auth is allowed to run actions on non-root components.\n  componentPath?: string;\n};\n\nexport type AdminAuthentication = {\n  type: \"Authenticate\";\n  tokenType: \"Admin\";\n  value: string;\n  baseVersion: IdentityVersion;\n  impersonating?: UserIdentityAttributes;\n};\n\nexport type Authenticate =\n  | AdminAuthentication\n  | {\n      type: \"Authenticate\";\n      tokenType: \"User\";\n      value: string;\n      baseVersion: IdentityVersion;\n    }\n  | {\n      type: \"Authenticate\";\n      tokenType: \"None\";\n      baseVersion: IdentityVersion;\n    };\n\nexport type Event = {\n  type: \"Event\";\n  eventType: string;\n  event: any;\n};\nexport type ClientMessage =\n  | Connect\n  | Authenticate\n  | QuerySetModification\n  | MutationRequest\n  | ActionRequest\n  | Event;\n\ntype EncodedConnect = Omit<Connect, \"maxObservedTimestamp\"> & {\n  maxObservedTimestamp?: EncodedTS;\n};\n\ntype EncodedClientMessage =\n  | EncodedConnect\n  | Authenticate\n  | QuerySetModification\n  | MutationRequest\n  | ActionRequest\n  | Event;\n\n/**\n * Server message schema\n */\nexport type TS = U64;\ntype EncodedTS = EncodedU64;\ntype LogLines = string[];\n\nexport type StateVersion = {\n  querySet: QuerySetVersion;\n  ts: TS;\n  identity: IdentityVersion;\n};\ntype EncodedStateVersion = Omit<StateVersion, \"ts\"> & { ts: EncodedTS };\n\ntype StateModification =\n  | {\n      type: \"QueryUpdated\";\n      queryId: QueryId;\n      value: JSONValue;\n      logLines: LogLines;\n      journal: QueryJournal;\n    }\n  | {\n      type: \"QueryFailed\";\n      queryId: QueryId;\n      errorMessage: string;\n      logLines: LogLines;\n      errorData: JSONValue;\n      journal: QueryJournal;\n    }\n  | {\n      type: \"QueryRemoved\";\n      queryId: QueryId;\n    };\n\nexport type Transition = {\n  type: \"Transition\";\n  startVersion: StateVersion;\n  endVersion: StateVersion;\n  modifications: StateModification[];\n};\n\ntype MutationSuccess = {\n  type: \"MutationResponse\";\n  requestId: RequestId;\n  success: true;\n  result: JSONValue;\n  ts: TS;\n  logLines: LogLines;\n};\ntype MutationFailed = {\n  type: \"MutationResponse\";\n  requestId: RequestId;\n  success: false;\n  result: string;\n  logLines: LogLines;\n  errorData?: JSONValue;\n};\nexport type MutationResponse = MutationSuccess | MutationFailed;\ntype ActionSuccess = {\n  type: \"ActionResponse\";\n  requestId: RequestId;\n  success: true;\n  result: JSONValue;\n  logLines: LogLines;\n};\ntype ActionFailed = {\n  type: \"ActionResponse\";\n  requestId: RequestId;\n  success: false;\n  result: string;\n  logLines: LogLines;\n  errorData?: JSONValue;\n};\nexport type ActionResponse = ActionSuccess | ActionFailed;\nexport type AuthError = {\n  type: \"AuthError\";\n  error: string;\n  baseVersion: IdentityVersion;\n  // True if this error is in response to processing a new `Authenticate` message.\n  // Other AuthErrors may occur due to executing a function with expired auth and\n  // should be handled differently.\n  authUpdateAttempted: boolean;\n};\ntype FatalError = {\n  type: \"FatalError\";\n  error: string;\n};\ntype Ping = {\n  type: \"Ping\";\n};\n\nexport type ServerMessage =\n  | Transition\n  | MutationResponse\n  | ActionResponse\n  | FatalError\n  | AuthError\n  | Ping;\n\ntype EncodedTransition = Omit<Transition, \"startVersion\" | \"endVersion\"> & {\n  startVersion: EncodedStateVersion;\n  endVersion: EncodedStateVersion;\n};\ntype EncodedMutationSuccess = Omit<MutationSuccess, \"ts\"> & { ts: EncodedTS };\ntype EncodedMutationResponse = MutationFailed | EncodedMutationSuccess;\n\ntype EncodedServerMessage =\n  | EncodedTransition\n  | EncodedMutationResponse\n  | ActionResponse\n  | FatalError\n  | AuthError\n  | Ping;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAAkC;AAClC,kBAAqB;AAMd,SAAS,UAAU,SAA0B;AAClD,QAAM,eAAe,qBAAO,YAAY,OAAO;AAC/C,SAAO,iBAAK,YAAY,MAAM,KAAK,YAAY,CAAC;AAClD;AAEO,SAAS,UAAU,KAAsB;AAC9C,QAAM,eAAe,IAAI,WAAW,IAAI,UAAU,CAAC;AACnD,SAAO,qBAAO,cAAc,YAAY;AAC1C;AAEO,SAAS,mBACd,SACe;AACf,UAAQ,QAAQ,MAAM;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,QAAQ;AACX,aAAO,EAAE,GAAG,QAAQ;AAAA,IACtB;AAAA,IACA,KAAK,oBAAoB;AACvB,UAAI,QAAQ,SAAS;AACnB,eAAO,EAAE,GAAG,SAAS,IAAI,UAAU,QAAQ,EAAE,EAAE;AAAA,MACjD,OAAO;AACL,eAAO,EAAE,GAAG,QAAQ;AAAA,MACtB;AAAA,IACF;AAAA,IACA,KAAK,cAAc;AACjB,aAAO;AAAA,QACL,GAAG;AAAA,QACH,cAAc;AAAA,UACZ,GAAG,QAAQ;AAAA,UACX,IAAI,UAAU,QAAQ,aAAa,EAAE;AAAA,QACvC;AAAA,QACA,YAAY;AAAA,UACV,GAAG,QAAQ;AAAA,UACX,IAAI,UAAU,QAAQ,WAAW,EAAE;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,uBAA8B;AAAA,IACtC;AAAA,EACF;AACA,SAAO;AACT;AAEO,SAAS,oBACd,SACsB;AACtB,UAAQ,QAAQ,MAAM;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACZ,aAAO,EAAE,GAAG,QAAQ;AAAA,IACtB;AAAA,IACA,KAAK,WAAW;AACd,UAAI,QAAQ,yBAAyB,QAAW;AAC9C,eAAO;AAAA,UACL,GAAG;AAAA,UACH,sBAAsB,UAAU,QAAQ,oBAAoB;AAAA,QAC9D;AAAA,MACF,OAAO;AACL,eAAO,EAAE,GAAG,SAAS,sBAAsB,OAAU;AAAA,MACvD;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,uBAA8B;AAAA,IACtC;AAAA,EACF;AACA,SAAO;AACT;", "names": []}
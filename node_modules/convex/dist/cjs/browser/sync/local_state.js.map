{"version": 3, "sources": ["../../../../src/browser/sync/local_state.ts"], "sourcesContent": ["import { convexToJson, Value } from \"../../values/index.js\";\nimport {\n  AddQuery,\n  RemoveQuery,\n  QueryId,\n  QuerySetModification,\n  QuerySetVersion,\n  IdentityVersion,\n  Authenticate,\n  QueryJournal,\n  Transition,\n  AdminAuthentication,\n  UserIdentityAttributes,\n} from \"./protocol.js\";\nimport {\n  canonicalizeUdfPath,\n  QueryToken,\n  serializePathAndArgs,\n} from \"./udf_path_utils.js\";\n\ntype LocalQuery = {\n  id: QueryId;\n  canonicalizedUdfPath: string;\n  args: Record<string, Value>;\n  numSubscribers: number;\n  journal?: QueryJournal;\n  componentPath?: string;\n};\n\nexport class LocalSyncState {\n  private nextQueryId: QueryId;\n  private querySetVersion: QuerySetVersion;\n  private readonly querySet: Map<QueryToken, LocalQuery>;\n  private readonly queryIdToToken: Map<QueryId, QueryToken>;\n  private identityVersion: IdentityVersion;\n  private auth?: {\n    tokenType: \"Admin\" | \"User\";\n    value: string;\n    impersonating?: UserIdentityAttributes;\n  };\n  private readonly outstandingQueriesOlderThanRestart: Set<QueryId>;\n  private outstandingAuthOlderThanRestart: boolean;\n  private paused: boolean;\n  private pendingQuerySetModifications: Map<QueryId, AddQuery | RemoveQuery>;\n\n  constructor() {\n    this.nextQueryId = 0;\n    this.querySetVersion = 0;\n    this.identityVersion = 0;\n    this.querySet = new Map();\n    this.queryIdToToken = new Map();\n    this.outstandingQueriesOlderThanRestart = new Set();\n    this.outstandingAuthOlderThanRestart = false;\n    this.paused = false;\n    this.pendingQuerySetModifications = new Map();\n  }\n\n  hasSyncedPastLastReconnect(): boolean {\n    return (\n      this.outstandingQueriesOlderThanRestart.size === 0 &&\n      !this.outstandingAuthOlderThanRestart\n    );\n  }\n\n  markAuthCompletion() {\n    this.outstandingAuthOlderThanRestart = false;\n  }\n\n  subscribe(\n    udfPath: string,\n    args: Record<string, Value>,\n    journal?: QueryJournal,\n    componentPath?: string,\n  ): {\n    queryToken: QueryToken;\n    modification: QuerySetModification | null;\n    unsubscribe: () => QuerySetModification | null;\n  } {\n    const canonicalizedUdfPath = canonicalizeUdfPath(udfPath);\n    const queryToken = serializePathAndArgs(canonicalizedUdfPath, args);\n\n    const existingEntry = this.querySet.get(queryToken);\n\n    if (existingEntry !== undefined) {\n      existingEntry.numSubscribers += 1;\n      return {\n        queryToken,\n        modification: null,\n        unsubscribe: () => this.removeSubscriber(queryToken),\n      };\n    } else {\n      const queryId = this.nextQueryId++;\n      const query: LocalQuery = {\n        id: queryId,\n        canonicalizedUdfPath,\n        args,\n        numSubscribers: 1,\n        journal,\n        componentPath,\n      };\n      this.querySet.set(queryToken, query);\n      this.queryIdToToken.set(queryId, queryToken);\n\n      const baseVersion = this.querySetVersion;\n      const newVersion = this.querySetVersion + 1;\n\n      const add: AddQuery = {\n        type: \"Add\",\n        queryId,\n        udfPath: canonicalizedUdfPath,\n        args: [convexToJson(args)],\n        journal,\n        componentPath,\n      };\n\n      if (this.paused) {\n        this.pendingQuerySetModifications.set(queryId, add);\n      } else {\n        this.querySetVersion = newVersion;\n      }\n\n      const modification: QuerySetModification = {\n        type: \"ModifyQuerySet\",\n        baseVersion,\n        newVersion,\n        modifications: [add],\n      };\n      return {\n        queryToken,\n        modification,\n        unsubscribe: () => this.removeSubscriber(queryToken),\n      };\n    }\n  }\n\n  transition(transition: Transition) {\n    for (const modification of transition.modifications) {\n      switch (modification.type) {\n        case \"QueryUpdated\":\n        case \"QueryFailed\": {\n          this.outstandingQueriesOlderThanRestart.delete(modification.queryId);\n          const journal = modification.journal;\n          if (journal !== undefined) {\n            const queryToken = this.queryIdToToken.get(modification.queryId);\n            // We may have already unsubscribed to this query by the time the server\n            // sends us the journal. If so, just ignore it.\n            if (queryToken !== undefined) {\n              this.querySet.get(queryToken)!.journal = journal;\n            }\n          }\n\n          break;\n        }\n        case \"QueryRemoved\": {\n          this.outstandingQueriesOlderThanRestart.delete(modification.queryId);\n          break;\n        }\n        default: {\n          // Enforce that the switch-case is exhaustive.\n          const _: never = modification;\n          throw new Error(`Invalid modification ${(modification as any).type}`);\n        }\n      }\n    }\n  }\n\n  queryId(udfPath: string, args: Record<string, Value>): QueryId | null {\n    const canonicalizedUdfPath = canonicalizeUdfPath(udfPath);\n    const queryToken = serializePathAndArgs(canonicalizedUdfPath, args);\n    const existingEntry = this.querySet.get(queryToken);\n    if (existingEntry !== undefined) {\n      return existingEntry.id;\n    }\n    return null;\n  }\n\n  isCurrentOrNewerAuthVersion(version: IdentityVersion): boolean {\n    return version >= this.identityVersion;\n  }\n\n  setAuth(value: string): Authenticate {\n    this.auth = {\n      tokenType: \"User\",\n      value: value,\n    };\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      baseVersion: baseVersion,\n      ...this.auth,\n    };\n  }\n\n  setAdminAuth(\n    value: string,\n    actingAs?: UserIdentityAttributes,\n  ): AdminAuthentication {\n    const auth: typeof this.auth & {\n      tokenType: \"Admin\";\n    } = {\n      tokenType: \"Admin\",\n      value,\n      impersonating: actingAs,\n    };\n    this.auth = auth;\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      baseVersion: baseVersion,\n      ...auth,\n    };\n  }\n\n  clearAuth(): Authenticate {\n    this.auth = undefined;\n    this.markAuthCompletion();\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      tokenType: \"None\",\n      baseVersion: baseVersion,\n    };\n  }\n\n  hasAuth(): boolean {\n    return !!this.auth;\n  }\n\n  isNewAuth(value: string): boolean {\n    return this.auth?.value !== value;\n  }\n\n  queryPath(queryId: QueryId): string | null {\n    const pathAndArgs = this.queryIdToToken.get(queryId);\n    if (pathAndArgs) {\n      return this.querySet.get(pathAndArgs)!.canonicalizedUdfPath;\n    }\n    return null;\n  }\n\n  queryArgs(queryId: QueryId): Record<string, Value> | null {\n    const pathAndArgs = this.queryIdToToken.get(queryId);\n    if (pathAndArgs) {\n      return this.querySet.get(pathAndArgs)!.args;\n    }\n    return null;\n  }\n\n  queryToken(queryId: QueryId): string | null {\n    return this.queryIdToToken.get(queryId) ?? null;\n  }\n\n  queryJournal(queryToken: QueryToken): QueryJournal | undefined {\n    return this.querySet.get(queryToken)?.journal;\n  }\n\n  restart(\n    oldRemoteQueryResults: Set<QueryId>,\n  ): [QuerySetModification, Authenticate?] {\n    // Restart works whether we are paused or unpaused.\n    // The `this.pendingQuerySetModifications` is not used\n    // when restarting as the AddQuery and RemoveQuery are computed\n    // from scratch, based on the old remote query results, here.\n    this.unpause();\n\n    this.outstandingQueriesOlderThanRestart.clear();\n    const modifications = [];\n    for (const localQuery of this.querySet.values()) {\n      const add: AddQuery = {\n        type: \"Add\",\n        queryId: localQuery.id,\n        udfPath: localQuery.canonicalizedUdfPath,\n        args: [convexToJson(localQuery.args)],\n        journal: localQuery.journal,\n        componentPath: localQuery.componentPath,\n      };\n      modifications.push(add);\n\n      if (!oldRemoteQueryResults.has(localQuery.id)) {\n        this.outstandingQueriesOlderThanRestart.add(localQuery.id);\n      }\n    }\n    this.querySetVersion = 1;\n    const querySet: QuerySetModification = {\n      type: \"ModifyQuerySet\",\n      baseVersion: 0,\n      newVersion: 1,\n      modifications,\n    };\n    // If there's no auth, no need to send an update as the server will also start with an unknown identity.\n    if (!this.auth) {\n      this.identityVersion = 0;\n      return [querySet, undefined];\n    }\n    this.outstandingAuthOlderThanRestart = true;\n    const authenticate: Authenticate = {\n      type: \"Authenticate\",\n      baseVersion: 0,\n      ...this.auth,\n    };\n    this.identityVersion = 1;\n    return [querySet, authenticate];\n  }\n\n  pause() {\n    this.paused = true;\n  }\n\n  resume(): [QuerySetModification?, Authenticate?] {\n    const querySet: QuerySetModification | undefined =\n      this.pendingQuerySetModifications.size > 0\n        ? {\n            type: \"ModifyQuerySet\",\n            baseVersion: this.querySetVersion,\n            newVersion: ++this.querySetVersion,\n            modifications: Array.from(\n              this.pendingQuerySetModifications.values(),\n            ),\n          }\n        : undefined;\n    const authenticate: Authenticate | undefined =\n      this.auth !== undefined\n        ? {\n            type: \"Authenticate\",\n            baseVersion: this.identityVersion++,\n            ...this.auth,\n          }\n        : undefined;\n\n    this.unpause();\n\n    return [querySet, authenticate];\n  }\n\n  private unpause() {\n    this.paused = false;\n    this.pendingQuerySetModifications.clear();\n  }\n\n  private removeSubscriber(\n    queryToken: QueryToken,\n  ): QuerySetModification | null {\n    const localQuery = this.querySet.get(queryToken)!;\n\n    if (localQuery.numSubscribers > 1) {\n      localQuery.numSubscribers -= 1;\n      return null;\n    } else {\n      this.querySet.delete(queryToken);\n      this.queryIdToToken.delete(localQuery.id);\n      this.outstandingQueriesOlderThanRestart.delete(localQuery.id);\n      const baseVersion = this.querySetVersion;\n      const newVersion = this.querySetVersion + 1;\n      const remove: RemoveQuery = {\n        type: \"Remove\",\n        queryId: localQuery.id,\n      };\n      if (this.paused) {\n        if (this.pendingQuerySetModifications.has(localQuery.id)) {\n          this.pendingQuerySetModifications.delete(localQuery.id);\n        } else {\n          this.pendingQuerySetModifications.set(localQuery.id, remove);\n        }\n      } else {\n        this.querySetVersion = newVersion;\n      }\n      return {\n        type: \"ModifyQuerySet\",\n        baseVersion,\n        newVersion,\n        modifications: [remove],\n      };\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAoC;AAcpC,4BAIO;AAWA,MAAM,eAAe;AAAA,EAgB1B,cAAc;AAfd,wBAAQ;AACR,wBAAQ;AACR,wBAAiB;AACjB,wBAAiB;AACjB,wBAAQ;AACR,wBAAQ;AAKR,wBAAiB;AACjB,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AAGN,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,qCAAqC,oBAAI,IAAI;AAClD,SAAK,kCAAkC;AACvC,SAAK,SAAS;AACd,SAAK,+BAA+B,oBAAI,IAAI;AAAA,EAC9C;AAAA,EAEA,6BAAsC;AACpC,WACE,KAAK,mCAAmC,SAAS,KACjD,CAAC,KAAK;AAAA,EAEV;AAAA,EAEA,qBAAqB;AACnB,SAAK,kCAAkC;AAAA,EACzC;AAAA,EAEA,UACE,SACA,MACA,SACA,eAKA;AACA,UAAM,2BAAuB,2CAAoB,OAAO;AACxD,UAAM,iBAAa,4CAAqB,sBAAsB,IAAI;AAElE,UAAM,gBAAgB,KAAK,SAAS,IAAI,UAAU;AAElD,QAAI,kBAAkB,QAAW;AAC/B,oBAAc,kBAAkB;AAChC,aAAO;AAAA,QACL;AAAA,QACA,cAAc;AAAA,QACd,aAAa,MAAM,KAAK,iBAAiB,UAAU;AAAA,MACrD;AAAA,IACF,OAAO;AACL,YAAM,UAAU,KAAK;AACrB,YAAM,QAAoB;AAAA,QACxB,IAAI;AAAA,QACJ;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AACA,WAAK,SAAS,IAAI,YAAY,KAAK;AACnC,WAAK,eAAe,IAAI,SAAS,UAAU;AAE3C,YAAM,cAAc,KAAK;AACzB,YAAM,aAAa,KAAK,kBAAkB;AAE1C,YAAM,MAAgB;AAAA,QACpB,MAAM;AAAA,QACN;AAAA,QACA,SAAS;AAAA,QACT,MAAM,KAAC,4BAAa,IAAI,CAAC;AAAA,QACzB;AAAA,QACA;AAAA,MACF;AAEA,UAAI,KAAK,QAAQ;AACf,aAAK,6BAA6B,IAAI,SAAS,GAAG;AAAA,MACpD,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AAEA,YAAM,eAAqC;AAAA,QACzC,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,eAAe,CAAC,GAAG;AAAA,MACrB;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,aAAa,MAAM,KAAK,iBAAiB,UAAU;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,YAAwB;AACjC,eAAW,gBAAgB,WAAW,eAAe;AACnD,cAAQ,aAAa,MAAM;AAAA,QACzB,KAAK;AAAA,QACL,KAAK,eAAe;AAClB,eAAK,mCAAmC,OAAO,aAAa,OAAO;AACnE,gBAAM,UAAU,aAAa;AAC7B,cAAI,YAAY,QAAW;AACzB,kBAAM,aAAa,KAAK,eAAe,IAAI,aAAa,OAAO;AAG/D,gBAAI,eAAe,QAAW;AAC5B,mBAAK,SAAS,IAAI,UAAU,EAAG,UAAU;AAAA,YAC3C;AAAA,UACF;AAEA;AAAA,QACF;AAAA,QACA,KAAK,gBAAgB;AACnB,eAAK,mCAAmC,OAAO,aAAa,OAAO;AACnE;AAAA,QACF;AAAA,QACA,SAAS;AAEP,gBAAM,IAAW;AACjB,gBAAM,IAAI,MAAM,wBAAyB,aAAqB,IAAI,EAAE;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,QAAQ,SAAiB,MAA6C;AACpE,UAAM,2BAAuB,2CAAoB,OAAO;AACxD,UAAM,iBAAa,4CAAqB,sBAAsB,IAAI;AAClE,UAAM,gBAAgB,KAAK,SAAS,IAAI,UAAU;AAClD,QAAI,kBAAkB,QAAW;AAC/B,aAAO,cAAc;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,4BAA4B,SAAmC;AAC7D,WAAO,WAAW,KAAK;AAAA,EACzB;AAAA,EAEA,QAAQ,OAA6B;AACnC,SAAK,OAAO;AAAA,MACV,WAAW;AAAA,MACX;AAAA,IACF;AACA,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,kBAAkB,cAAc;AAAA,IACvC;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EAEA,aACE,OACA,UACqB;AACrB,UAAM,OAEF;AAAA,MACF,WAAW;AAAA,MACX;AAAA,MACA,eAAe;AAAA,IACjB;AACA,SAAK,OAAO;AACZ,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,kBAAkB,cAAc;AAAA,IACvC;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF;AAAA,EAEA,YAA0B;AACxB,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,kBAAkB,cAAc;AAAA,IACvC;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,WAAW;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAmB;AACjB,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EAEA,UAAU,OAAwB;AAChC,WAAO,KAAK,MAAM,UAAU;AAAA,EAC9B;AAAA,EAEA,UAAU,SAAiC;AACzC,UAAM,cAAc,KAAK,eAAe,IAAI,OAAO;AACnD,QAAI,aAAa;AACf,aAAO,KAAK,SAAS,IAAI,WAAW,EAAG;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,UAAU,SAAgD;AACxD,UAAM,cAAc,KAAK,eAAe,IAAI,OAAO;AACnD,QAAI,aAAa;AACf,aAAO,KAAK,SAAS,IAAI,WAAW,EAAG;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,WAAW,SAAiC;AAC1C,WAAO,KAAK,eAAe,IAAI,OAAO,KAAK;AAAA,EAC7C;AAAA,EAEA,aAAa,YAAkD;AAC7D,WAAO,KAAK,SAAS,IAAI,UAAU,GAAG;AAAA,EACxC;AAAA,EAEA,QACE,uBACuC;AAKvC,SAAK,QAAQ;AAEb,SAAK,mCAAmC,MAAM;AAC9C,UAAM,gBAAgB,CAAC;AACvB,eAAW,cAAc,KAAK,SAAS,OAAO,GAAG;AAC/C,YAAM,MAAgB;AAAA,QACpB,MAAM;AAAA,QACN,SAAS,WAAW;AAAA,QACpB,SAAS,WAAW;AAAA,QACpB,MAAM,KAAC,4BAAa,WAAW,IAAI,CAAC;AAAA,QACpC,SAAS,WAAW;AAAA,QACpB,eAAe,WAAW;AAAA,MAC5B;AACA,oBAAc,KAAK,GAAG;AAEtB,UAAI,CAAC,sBAAsB,IAAI,WAAW,EAAE,GAAG;AAC7C,aAAK,mCAAmC,IAAI,WAAW,EAAE;AAAA,MAC3D;AAAA,IACF;AACA,SAAK,kBAAkB;AACvB,UAAM,WAAiC;AAAA,MACrC,MAAM;AAAA,MACN,aAAa;AAAA,MACb,YAAY;AAAA,MACZ;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,MAAM;AACd,WAAK,kBAAkB;AACvB,aAAO,CAAC,UAAU,MAAS;AAAA,IAC7B;AACA,SAAK,kCAAkC;AACvC,UAAM,eAA6B;AAAA,MACjC,MAAM;AAAA,MACN,aAAa;AAAA,MACb,GAAG,KAAK;AAAA,IACV;AACA,SAAK,kBAAkB;AACvB,WAAO,CAAC,UAAU,YAAY;AAAA,EAChC;AAAA,EAEA,QAAQ;AACN,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,SAAiD;AAC/C,UAAM,WACJ,KAAK,6BAA6B,OAAO,IACrC;AAAA,MACE,MAAM;AAAA,MACN,aAAa,KAAK;AAAA,MAClB,YAAY,EAAE,KAAK;AAAA,MACnB,eAAe,MAAM;AAAA,QACnB,KAAK,6BAA6B,OAAO;AAAA,MAC3C;AAAA,IACF,IACA;AACN,UAAM,eACJ,KAAK,SAAS,SACV;AAAA,MACE,MAAM;AAAA,MACN,aAAa,KAAK;AAAA,MAClB,GAAG,KAAK;AAAA,IACV,IACA;AAEN,SAAK,QAAQ;AAEb,WAAO,CAAC,UAAU,YAAY;AAAA,EAChC;AAAA,EAEQ,UAAU;AAChB,SAAK,SAAS;AACd,SAAK,6BAA6B,MAAM;AAAA,EAC1C;AAAA,EAEQ,iBACN,YAC6B;AAC7B,UAAM,aAAa,KAAK,SAAS,IAAI,UAAU;AAE/C,QAAI,WAAW,iBAAiB,GAAG;AACjC,iBAAW,kBAAkB;AAC7B,aAAO;AAAA,IACT,OAAO;AACL,WAAK,SAAS,OAAO,UAAU;AAC/B,WAAK,eAAe,OAAO,WAAW,EAAE;AACxC,WAAK,mCAAmC,OAAO,WAAW,EAAE;AAC5D,YAAM,cAAc,KAAK;AACzB,YAAM,aAAa,KAAK,kBAAkB;AAC1C,YAAM,SAAsB;AAAA,QAC1B,MAAM;AAAA,QACN,SAAS,WAAW;AAAA,MACtB;AACA,UAAI,KAAK,QAAQ;AACf,YAAI,KAAK,6BAA6B,IAAI,WAAW,EAAE,GAAG;AACxD,eAAK,6BAA6B,OAAO,WAAW,EAAE;AAAA,QACxD,OAAO;AACL,eAAK,6BAA6B,IAAI,WAAW,IAAI,MAAM;AAAA,QAC7D;AAAA,MACF,OAAO;AACL,aAAK,kBAAkB;AAAA,MACzB;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,eAAe,CAAC,MAAM;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACF;", "names": []}
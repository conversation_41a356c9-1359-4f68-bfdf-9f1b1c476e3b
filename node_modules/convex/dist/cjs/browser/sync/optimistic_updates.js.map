{"version": 3, "sources": ["../../../../src/browser/sync/optimistic_updates.ts"], "sourcesContent": ["import { Value } from \"../../values/index.js\";\nimport {\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n} from \"../../server/api.js\";\n\n/**\n * A view of the query results currently in the Convex client for use within\n * optimistic updates.\n *\n * @public\n */\nexport interface OptimisticLocalStore {\n  /**\n   * Retrieve the result of a query from the client.\n   *\n   * Important: Query results should be treated as immutable!\n   * Always make new copies of structures within query results to avoid\n   * corrupting data within the client.\n   *\n   * @param query - A {@link FunctionReference} for the query to get.\n   * @param args - The arguments object for this query.\n   * @returns The query result or `undefined` if the query is not currently\n   * in the client.\n   */\n  getQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): undefined | FunctionReturnType<Query>;\n\n  /**\n   * Retrieve the results and arguments of all queries with a given name.\n   *\n   * This is useful for complex optimistic updates that need to inspect and\n   * update many query results (for example updating a paginated list).\n   *\n   * Important: Query results should be treated as immutable!\n   * Always make new copies of structures within query results to avoid\n   * corrupting data within the client.\n   *\n   * @param query - A {@link FunctionReference} for the query to get.\n   * @returns An array of objects, one for each query of the given name.\n   * Each object includes:\n   *   - `args` - The arguments object for the query.\n   *   - `value` The query result or `undefined` if the query is loading.\n   */\n  getAllQueries<Query extends FunctionReference<\"query\">>(\n    query: Query,\n  ): {\n    args: FunctionArgs<Query>;\n    value: undefined | FunctionReturnType<Query>;\n  }[];\n\n  /**\n   * Optimistically update the result of a query.\n   *\n   * This can either be a new value (perhaps derived from the old value from\n   * {@link OptimisticLocalStore.getQuery}) or `undefined` to remove the query.\n   * Removing a query is useful to create loading states while Convex recomputes\n   * the query results.\n   *\n   * @param query - A {@link FunctionReference} for the query to set.\n   * @param args - The arguments object for this query.\n   * @param value - The new value to set the query to or `undefined` to remove\n   * it from the client.\n   */\n  setQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    args: FunctionArgs<Query>,\n    value: undefined | FunctionReturnType<Query>,\n  ): void;\n}\n/**\n * A temporary, local update to query results within this client.\n *\n * This update will always be executed when a mutation is synced to the Convex\n * server and rolled back when the mutation completes.\n *\n * Note that optimistic updates can be called multiple times! If the client\n * loads new data while the mutation is in progress, the update will be replayed\n * again.\n *\n * @param localQueryStore - An interface to read and edit local query results.\n * @param args - The arguments to the mutation.\n *\n * @public\n */\nexport type OptimisticUpdate<Args extends Record<string, Value>> = (\n  localQueryStore: OptimisticLocalStore,\n  args: Args,\n) => void;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}
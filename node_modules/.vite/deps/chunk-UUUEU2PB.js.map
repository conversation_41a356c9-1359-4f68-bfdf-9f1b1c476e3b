{"version": 3, "sources": ["../../convex/src/server/functionName.ts", "../../convex/src/server/components/paths.ts", "../../convex/src/server/api.ts", "../../convex/src/common/index.ts", "../../convex/src/values/base64.ts", "../../convex/src/values/value.ts", "../../convex/src/values/validators.ts", "../../convex/src/values/validator.ts", "../../convex/src/values/errors.ts", "../../convex/src/values/compare_utf8.ts", "../../convex/src/values/compare.ts", "../../convex/src/index.ts"], "sourcesContent": ["/**\n * A symbol for accessing the name of a {@link FunctionReference} at runtime.\n */\nexport const functionName = Symbol.for(\"functionName\");\n", "import { functionName } from \"../functionName.js\";\n\nexport const toReferencePath = Symbol.for(\"toReferencePath\");\n\n// Multiple instances of the same Symbol.for() are equal at runtime but not\n// at type-time, so `[toReferencePath]` properties aren't used in types.\n// Use this function to set the property invisibly.\nexport function setReferencePath<T>(obj: T, value: string) {\n  (obj as any)[toReferencePath] = value;\n}\n\nexport function extractReferencePath(reference: any): string | null {\n  return reference[toReferencePath] ?? null;\n}\n\nexport function isFunctionHandle(s: string): boolean {\n  return s.startsWith(\"function://\");\n}\n\nexport function getFunctionAddress(functionReference: any) {\n  // The `run*` syscalls expect either a UDF path at \"name\" or a serialized\n  // reference at \"reference\". Dispatch on `functionReference` to coerce\n  // it to one or the other.\n  let functionAddress;\n\n  // Legacy path for passing in UDF paths directly as function references.\n  if (typeof functionReference === \"string\") {\n    if (isFunctionHandle(functionReference)) {\n      functionAddress = { functionHandle: functionReference };\n    } else {\n      functionAddress = { name: functionReference };\n    }\n  }\n  // Path for passing in a `FunctionReference`, either from `api` or directly\n  // created from a UDF path with `makeFunctionReference`.\n  else if (functionReference[functionName]) {\n    functionAddress = { name: functionReference[functionName] };\n  }\n  // Reference to a component's function derived from `app` or `component`.\n  else {\n    const referencePath = extractReferencePath(functionReference);\n    if (!referencePath) {\n      throw new Error(`${functionReference} is not a functionReference`);\n    }\n    functionAddress = { reference: referencePath };\n  }\n  return functionAddress;\n}\n", "import {\n  EmptyObject,\n  DefaultFunctionArgs,\n  FunctionVisibility,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"./registration.js\";\nimport { Expand, UnionToIntersection } from \"../type_utils.js\";\nimport { PaginationOptions, PaginationResult } from \"./pagination.js\";\nimport { functionName } from \"./functionName.js\";\nimport { getFunctionAddress } from \"./components/paths.js\";\n\n/**\n * The type of a Convex function.\n *\n * @public\n */\nexport type FunctionType = \"query\" | \"mutation\" | \"action\";\n\n/**\n * A reference to a registered Convex function.\n *\n * You can create a {@link FunctionReference} using the generated `api` utility:\n * ```js\n * import { api } from \"../convex/_generated/api\";\n *\n * const reference = api.myModule.myFunction;\n * ```\n *\n * If you aren't using code generation, you can create references using\n * {@link anyApi}:\n * ```js\n * import { anyApi } from \"convex/server\";\n *\n * const reference = anyApi.myModule.myFunction;\n * ```\n *\n * Function references can be used to invoke functions from the client. For\n * example, in React you can pass references to the {@link react.useQuery} hook:\n * ```js\n * const result = useQuery(api.myModule.myFunction);\n * ```\n *\n * @typeParam Type - The type of the function (\"query\", \"mutation\", or \"action\").\n * @typeParam Visibility - The visibility of the function (\"public\" or \"internal\").\n * @typeParam Args - The arguments to this function. This is an object mapping\n * argument names to their types.\n * @typeParam ReturnType - The return type of this function.\n * @public\n */\nexport type FunctionReference<\n  Type extends FunctionType,\n  Visibility extends FunctionVisibility = \"public\",\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n  ComponentPath = string | undefined,\n> = {\n  _type: Type;\n  _visibility: Visibility;\n  _args: Args;\n  _returnType: ReturnType;\n  _componentPath: ComponentPath;\n};\n\n/**\n * Get the name of a function from a {@link FunctionReference}.\n *\n * The name is a string like \"myDir/myModule:myFunction\". If the exported name\n * of the function is `\"default\"`, the function name is omitted\n * (e.g. \"myDir/myModule\").\n *\n * @param functionReference - A {@link FunctionReference} to get the name of.\n * @returns A string of the function's name.\n *\n * @public\n */\nexport function getFunctionName(\n  functionReference: AnyFunctionReference,\n): string {\n  const address = getFunctionAddress(functionReference);\n\n  if (address.name === undefined) {\n    if (address.functionHandle !== undefined) {\n      throw new Error(\n        `Expected function reference like \"api.file.func\" or \"internal.file.func\", but received function handle ${address.functionHandle}`,\n      );\n    } else if (address.reference !== undefined) {\n      throw new Error(\n        `Expected function reference in the current component like \"api.file.func\" or \"internal.file.func\", but received reference ${address.reference}`,\n      );\n    }\n    throw new Error(\n      `Expected function reference like \"api.file.func\" or \"internal.file.func\", but received ${JSON.stringify(address)}`,\n    );\n  }\n  // Both a legacy thing and also a convenience for interactive use:\n  // the types won't check but a string is always allowed at runtime.\n  if (typeof functionReference === \"string\") return functionReference;\n\n  // Two different runtime values for FunctionReference implement this\n  // interface: api objects returned from `createApi()` and standalone\n  // function reference objects returned from makeFunctionReference.\n  const name = (functionReference as any)[functionName];\n  if (!name) {\n    throw new Error(`${functionReference as any} is not a functionReference`);\n  }\n  return name;\n}\n\n/**\n * FunctionReferences generally come from generated code, but in custom clients\n * it may be useful to be able to build one manually.\n *\n * Real function references are empty objects at runtime, but the same interface\n * can be implemented with an object for tests and clients which don't use\n * code generation.\n *\n * @param name - The identifier of the function. E.g. `path/to/file:functionName`\n * @public\n */\nexport function makeFunctionReference<\n  type extends FunctionType,\n  args extends DefaultFunctionArgs = any,\n  ret = any,\n>(name: string): FunctionReference<type, \"public\", args, ret> {\n  return { [functionName]: name } as unknown as FunctionReference<\n    type,\n    \"public\",\n    args,\n    ret\n  >;\n}\n\n/**\n * Create a runtime API object that implements {@link AnyApi}.\n *\n * This allows accessing any path regardless of what directories, modules,\n * or functions are defined.\n *\n * @param pathParts - The path to the current node in the API.\n * @returns An {@link AnyApi}\n * @public\n */\nfunction createApi(pathParts: string[] = []): AnyApi {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createApi(newParts);\n      } else if (prop === functionName) {\n        if (pathParts.length < 2) {\n          const found = [\"api\", ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`api.moduleName.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        const path = pathParts.slice(0, -1).join(\"/\");\n        const exportName = pathParts[pathParts.length - 1];\n        if (exportName === \"default\") {\n          return path;\n        } else {\n          return path + \":\" + exportName;\n        }\n      } else if (prop === Symbol.toStringTag) {\n        return \"FunctionReference\";\n      } else {\n        return undefined;\n      }\n    },\n  };\n\n  return new Proxy({}, handler);\n}\n\n/**\n * Given an export from a module, convert it to a {@link FunctionReference}\n * if it is a Convex function.\n */\nexport type FunctionReferenceFromExport<Export> =\n  Export extends RegisteredQuery<\n    infer Visibility,\n    infer Args,\n    infer ReturnValue\n  >\n    ? FunctionReference<\n        \"query\",\n        Visibility,\n        Args,\n        ConvertReturnType<ReturnValue>\n      >\n    : Export extends RegisteredMutation<\n          infer Visibility,\n          infer Args,\n          infer ReturnValue\n        >\n      ? FunctionReference<\n          \"mutation\",\n          Visibility,\n          Args,\n          ConvertReturnType<ReturnValue>\n        >\n      : Export extends RegisteredAction<\n            infer Visibility,\n            infer Args,\n            infer ReturnValue\n          >\n        ? FunctionReference<\n            \"action\",\n            Visibility,\n            Args,\n            ConvertReturnType<ReturnValue>\n          >\n        : never;\n\n/**\n * Given a module, convert all the Convex functions into\n * {@link FunctionReference}s and remove the other exports.\n *\n * BE CAREFUL WHEN EDITING THIS!\n *\n * This is written carefully to preserve jumping to function definitions using\n * cmd+click. If you edit it, please test that cmd+click still works.\n */\ntype FunctionReferencesInModule<Module extends Record<string, any>> = {\n  -readonly [ExportName in keyof Module as Module[ExportName][\"isConvexFunction\"] extends true\n    ? ExportName\n    : never]: FunctionReferenceFromExport<Module[ExportName]>;\n};\n\n/**\n * Given a path to a module and it's type, generate an API type for this module.\n *\n * This is a nested object according to the module's path.\n */\ntype ApiForModule<\n  ModulePath extends string,\n  Module extends object,\n> = ModulePath extends `${infer First}/${infer Second}`\n  ? {\n      [_ in First]: ApiForModule<Second, Module>;\n    }\n  : { [_ in ModulePath]: FunctionReferencesInModule<Module> };\n\n/**\n * Given the types of all modules in the `convex/` directory, construct the type\n * of `api`.\n *\n * `api` is a utility for constructing {@link FunctionReference}s.\n *\n * @typeParam AllModules - A type mapping module paths (like `\"dir/myModule\"`) to\n * the types of the modules.\n * @public\n */\nexport type ApiFromModules<AllModules extends Record<string, object>> =\n  FilterApi<\n    ApiFromModulesAllowEmptyNodes<AllModules>,\n    FunctionReference<any, any, any, any>\n  >;\n\ntype ApiFromModulesAllowEmptyNodes<AllModules extends Record<string, object>> =\n  ExpandModulesAndDirs<\n    UnionToIntersection<\n      {\n        [ModulePath in keyof AllModules]: ApiForModule<\n          ModulePath & string,\n          AllModules[ModulePath]\n        >;\n      }[keyof AllModules]\n    >\n  >;\n\n/**\n * @public\n *\n * Filter a Convex deployment api object for functions which meet criteria,\n * for example all public queries.\n */\nexport type FilterApi<API, Predicate> = Expand<{\n  [mod in keyof API as API[mod] extends Predicate\n    ? mod\n    : API[mod] extends FunctionReference<any, any, any, any>\n      ? never\n      : FilterApi<API[mod], Predicate> extends Record<string, never>\n        ? never\n        : mod]: API[mod] extends Predicate\n    ? API[mod]\n    : FilterApi<API[mod], Predicate>;\n}>;\n\n/**\n * Given an api of type API and a FunctionReference subtype, return an api object\n * containing only the function references that match.\n *\n * ```ts\n * const q = filterApi<typeof api, FunctionReference<\"query\">>(api)\n * ```\n *\n * @public\n */\nexport function filterApi<API, Predicate>(api: API): FilterApi<API, Predicate> {\n  return api as any;\n}\n\n// These just* API filter helpers require no type parameters so are useable from JavaScript.\n/** @public */\nexport function justInternal<API>(\n  api: API,\n): FilterApi<API, FunctionReference<any, \"internal\", any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justPublic<API>(\n  api: API,\n): FilterApi<API, FunctionReference<any, \"public\", any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justQueries<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"query\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justMutations<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"mutation\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justActions<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"action\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justPaginatedQueries<API>(\n  api: API,\n): FilterApi<\n  API,\n  FunctionReference<\n    \"query\",\n    any,\n    { paginationOpts: PaginationOptions },\n    PaginationResult<any>\n  >\n> {\n  return api as any;\n}\n\n/** @public */\nexport function justSchedulable<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"mutation\" | \"action\", any, any, any>> {\n  return api as any;\n}\n\n/**\n * Like {@link Expand}, this simplifies how TypeScript displays object types.\n * The differences are:\n * 1. This version is recursive.\n * 2. This stops recursing when it hits a {@link FunctionReference}.\n */\ntype ExpandModulesAndDirs<ObjectType> = ObjectType extends AnyFunctionReference\n  ? ObjectType\n  : {\n      [Key in keyof ObjectType]: ExpandModulesAndDirs<ObjectType[Key]>;\n    };\n\n/**\n * A {@link FunctionReference} of any type and any visibility with any\n * arguments and any return type.\n *\n * @public\n */\nexport type AnyFunctionReference = FunctionReference<any, any>;\n\ntype AnyModuleDirOrFunc = {\n  [key: string]: AnyModuleDirOrFunc;\n} & AnyFunctionReference;\n\n/**\n * The type that Convex api objects extend. If you were writing an api from\n * scratch it should extend this type.\n *\n * @public\n */\nexport type AnyApi = Record<string, Record<string, AnyModuleDirOrFunc>>;\n\n/**\n * Recursive partial API, useful for defining a subset of an API when mocking\n * or building custom api objects.\n *\n * @public\n */\nexport type PartialApi<API> = {\n  [mod in keyof API]?: API[mod] extends FunctionReference<any, any, any, any>\n    ? API[mod]\n    : PartialApi<API[mod]>;\n};\n\n/**\n * A utility for constructing {@link FunctionReference}s in projects that\n * are not using code generation.\n *\n * You can create a reference to a function like:\n * ```js\n * const reference = anyApi.myModule.myFunction;\n * ```\n *\n * This supports accessing any path regardless of what directories and modules\n * are in your project. All function references are typed as\n * {@link AnyFunctionReference}.\n *\n *\n * If you're using code generation, use `api` from `convex/_generated/api`\n * instead. It will be more type-safe and produce better auto-complete\n * in your editor.\n *\n * @public\n */\nexport const anyApi: AnyApi = createApi() as any;\n\n/**\n * Given a {@link FunctionReference}, get the return type of the function.\n *\n * This is represented as an object mapping argument names to values.\n * @public\n */\nexport type FunctionArgs<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_args\"];\n\n/**\n * A tuple type of the (maybe optional) arguments to `FuncRef`.\n *\n * This type is used to make methods involving arguments type safe while allowing\n * skipping the arguments for functions that don't require arguments.\n *\n * @public\n */\nexport type OptionalRestArgs<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_args\"] extends EmptyObject\n    ? [args?: EmptyObject]\n    : [args: FuncRef[\"_args\"]];\n\n/**\n * A tuple type of the (maybe optional) arguments to `FuncRef`, followed by an options\n * object of type `Options`.\n *\n * This type is used to make methods like `useQuery` type-safe while allowing\n * 1. Skipping arguments for functions that don't require arguments.\n * 2. Skipping the options object.\n * @public\n */\nexport type ArgsAndOptions<\n  FuncRef extends AnyFunctionReference,\n  Options,\n> = FuncRef[\"_args\"] extends EmptyObject\n  ? [args?: EmptyObject, options?: Options]\n  : [args: FuncRef[\"_args\"], options?: Options];\n\n/**\n * Given a {@link FunctionReference}, get the return type of the function.\n *\n * @public\n */\nexport type FunctionReturnType<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_returnType\"];\n\ntype UndefinedToNull<T> = T extends void ? null : T;\n\ntype NullToUndefinedOrNull<T> = T extends null ? T | undefined | void : T;\n\n/**\n * Convert the return type of a function to it's client-facing format.\n *\n * This means:\n * - Converting `undefined` and `void` to `null`\n * - Removing all `Promise` wrappers\n */\nexport type ConvertReturnType<T> = UndefinedToNull<Awaited<T>>;\n\nexport type ValidatorTypeToReturnType<T> =\n  | Promise<NullToUndefinedOrNull<T>>\n  | NullToUndefinedOrNull<T>;\n", "import type { Value } from \"../values/value.js\";\n\n/**\n * Validate that the arguments to a Convex function are an object, defaulting\n * `undefined` to `{}`.\n */\nexport function parseArgs(\n  args: Record<string, Value> | undefined,\n): Record<string, Value> {\n  if (args === undefined) {\n    return {};\n  }\n  if (!isSimpleObject(args)) {\n    throw new Error(\n      `The arguments to a Convex function must be an object. Received: ${\n        args as any\n      }`,\n    );\n  }\n  return args;\n}\n\nexport function validateDeploymentUrl(deploymentUrl: string) {\n  // Don't use things like `new URL(deploymentUrl).hostname` since these aren't\n  // supported by React Native's JS environment\n  if (typeof deploymentUrl === \"undefined\") {\n    throw new Error(\n      `Client created with undefined deployment address. If you used an environment variable, check that it's set.`,\n    );\n  }\n  if (typeof deploymentUrl !== \"string\") {\n    throw new Error(\n      `Invalid deployment address: found ${deploymentUrl as any}\".`,\n    );\n  }\n  if (\n    !(deploymentUrl.startsWith(\"http:\") || deploymentUrl.startsWith(\"https:\"))\n  ) {\n    throw new Error(\n      `Invalid deployment address: Must start with \"https://\" or \"http://\". Found \"${deploymentUrl}\".`,\n    );\n  }\n\n  // Most clients should connect to \".convex.cloud\". But we also support localhost and\n  // custom custom. We validate the deployment url is a valid url, which is the most\n  // common failure pattern.\n  try {\n    new URL(deploymentUrl);\n  } catch {\n    throw new Error(\n      `Invalid deployment address: \"${deploymentUrl}\" is not a valid URL. If you believe this URL is correct, use the \\`skipConvexDeploymentUrlCheck\\` option to bypass this.`,\n    );\n  }\n\n  // If a user uses .convex.site, this is very likely incorrect.\n  if (deploymentUrl.endsWith(\".convex.site\")) {\n    throw new Error(\n      `Invalid deployment address: \"${deploymentUrl}\" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \\`skipConvexDeploymentUrlCheck\\` option to bypass this.`,\n    );\n  }\n}\n\n/**\n * Check whether a value is a plain old JavaScript object.\n */\nexport function isSimpleObject(value: unknown) {\n  const isObject = typeof value === \"object\";\n  const prototype = Object.getPrototypeOf(value);\n  const isSimple =\n    prototype === null ||\n    prototype === Object.prototype ||\n    // Objects generated from other contexts (e.g. across Node.js `vm` modules) will not satisfy the previous\n    // conditions but are still simple objects.\n    prototype?.constructor?.name === \"Object\";\n  return isObject && isSimple;\n}\n", "/*\nhttps://github.com/beatgammit/base64-js/blob/88957c9943c7e2a0f03cdf73e71d579e433627d3/index.js\nCopyright (c) 2014 Jameson Little\nThe MIT License (MIT)\n*/\n\n// Vendored because this library has no ESM build, and some environments\n// (SvelteKit) are happiest when all dependencies are ESM.\n\nvar lookup: string[] = [];\nvar revLookup: number[] = [];\nvar Arr = Uint8Array;\n\nvar code = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup[\"-\".charCodeAt(0)] = 62;\nrevLookup[\"_\".charCodeAt(0)] = 63;\n\nfunction getLens(b64: string) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error(\"Invalid string. Length must be a multiple of 4\");\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf(\"=\");\n  if (validLen === -1) validLen = len;\n\n  var placeHoldersLen = validLen === len ? 0 : 4 - (validLen % 4);\n\n  return [validLen, placeHoldersLen];\n}\n\n// base64 is 4/3 + up to two characters of the original data\n/** @public */\nexport function byteLength(b64: string): number {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return ((validLen + placeHoldersLen) * 3) / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(_b64: string, validLen: number, placeHoldersLen: number) {\n  return ((validLen + placeHoldersLen) * 3) / 4 - placeHoldersLen;\n}\n\n/** @public */\nexport function toByteArray(b64: string): Uint8Array {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n\n  var curByte = 0;\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n\n  var i;\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = (tmp >> 16) & 0xff;\n    arr[curByte++] = (tmp >> 8) & 0xff;\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4);\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2);\n    arr[curByte++] = (tmp >> 8) & 0xff;\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num: number) {\n  return (\n    lookup[(num >> 18) & 0x3f] +\n    lookup[(num >> 12) & 0x3f] +\n    lookup[(num >> 6) & 0x3f] +\n    lookup[num & 0x3f]\n  );\n}\n\nfunction encodeChunk(uint8: Uint8Array, start: number, end: number) {\n  var tmp;\n  var output = [];\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xff0000) +\n      ((uint8[i + 1] << 8) & 0xff00) +\n      (uint8[i + 2] & 0xff);\n    output.push(tripletToBase64(tmp));\n  }\n  return output.join(\"\");\n}\n\n/** @public */\nexport function fromByteArray(uint8: Uint8Array): string {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(\n      encodeChunk(\n        uint8,\n        i,\n        i + maxChunkLength > len2 ? len2 : i + maxChunkLength,\n      ),\n    );\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[(tmp << 4) & 0x3f] + \"==\");\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(\n      lookup[tmp >> 10] +\n        lookup[(tmp >> 4) & 0x3f] +\n        lookup[(tmp << 2) & 0x3f] +\n        \"=\",\n    );\n  }\n\n  return parts.join(\"\");\n}\n", "/**\n * Utilities for working with values stored in Convex.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n * @module\n */\nimport * as Base64 from \"./base64.js\";\nimport { isSimpleObject } from \"../common/index.js\";\n\nconst LITTLE_ENDIAN = true;\n// This code is used by code that may not have bigint literals.\nconst MIN_INT64 = BigInt(\"-9223372036854775808\");\nconst MAX_INT64 = BigInt(\"9223372036854775807\");\nconst ZERO = BigInt(\"0\");\nconst EIGHT = BigInt(\"8\");\nconst TWOFIFTYSIX = BigInt(\"256\");\n\n/**\n * The type of JavaScript values serializable to JSON.\n *\n * @public\n */\nexport type JSONValue =\n  | null\n  | boolean\n  | number\n  | string\n  | JSONValue[]\n  | { [key: string]: JSONValue };\n\n/**\n * An identifier for a document in Convex.\n *\n * Convex documents are uniquely identified by their `Id`, which is accessible\n * on the `_id` field. To learn more, see [Document IDs](https://docs.convex.dev/database/document-ids).\n *\n * Documents can be loaded using `db.get(id)` in query and mutation functions.\n *\n * IDs are base 32 encoded strings which are URL safe.\n *\n * IDs are just strings at runtime, but this type can be used to distinguish them from other\n * strings at compile time.\n *\n * If you're using code generation, use the `Id` type generated for your data model in\n * `convex/_generated/dataModel.d.ts`.\n *\n * @typeParam TableName - A string literal type of the table name (like \"users\").\n *\n * @public\n */\nexport type Id<TableName extends string> = string & { __tableName: TableName };\n\n/**\n * A value supported by Convex.\n *\n * Values can be:\n * - stored inside of documents.\n * - used as arguments and return types to queries and mutation functions.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n *\n * @public\n */\nexport type Value =\n  | null\n  | bigint\n  | number\n  | boolean\n  | string\n  | ArrayBuffer\n  | Value[]\n  | { [key: string]: undefined | Value };\n\n/**\n * The types of {@link Value} that can be used to represent numbers.\n *\n * @public\n */\nexport type NumericValue = bigint | number;\n\nfunction isSpecial(n: number) {\n  return Number.isNaN(n) || !Number.isFinite(n) || Object.is(n, -0);\n}\n\nexport function slowBigIntToBase64(value: bigint): string {\n  // the conversion is easy if we pretend it's unsigned\n  if (value < ZERO) {\n    value -= MIN_INT64 + MIN_INT64;\n  }\n  let hex = value.toString(16);\n  if (hex.length % 2 === 1) hex = \"0\" + hex;\n\n  const bytes = new Uint8Array(new ArrayBuffer(8));\n  let i = 0;\n  for (const hexByte of hex.match(/.{2}/g)!.reverse()) {\n    bytes.set([parseInt(hexByte, 16)], i++);\n    value >>= EIGHT;\n  }\n  return Base64.fromByteArray(bytes);\n}\n\nexport function slowBase64ToBigInt(encoded: string): bigint {\n  const integerBytes = Base64.toByteArray(encoded);\n  if (integerBytes.byteLength !== 8) {\n    throw new Error(\n      `Received ${integerBytes.byteLength} bytes, expected 8 for $integer`,\n    );\n  }\n  let value = ZERO;\n  let power = ZERO;\n  for (const byte of integerBytes) {\n    value += BigInt(byte) * TWOFIFTYSIX ** power;\n    power++;\n  }\n  if (value > MAX_INT64) {\n    value += MIN_INT64 + MIN_INT64;\n  }\n  return value;\n}\n\nexport function modernBigIntToBase64(value: bigint): string {\n  if (value < MIN_INT64 || MAX_INT64 < value) {\n    throw new Error(\n      `BigInt ${value} does not fit into a 64-bit signed integer.`,\n    );\n  }\n  const buffer = new ArrayBuffer(8);\n  new DataView(buffer).setBigInt64(0, value, true);\n  return Base64.fromByteArray(new Uint8Array(buffer));\n}\n\nexport function modernBase64ToBigInt(encoded: string): bigint {\n  const integerBytes = Base64.toByteArray(encoded);\n  if (integerBytes.byteLength !== 8) {\n    throw new Error(\n      `Received ${integerBytes.byteLength} bytes, expected 8 for $integer`,\n    );\n  }\n  const intBytesView = new DataView(integerBytes.buffer);\n  return intBytesView.getBigInt64(0, true);\n}\n\n// Fall back to a slower version on Safari 14 which lacks these APIs.\nexport const bigIntToBase64 = (DataView.prototype as any).setBigInt64\n  ? modernBigIntToBase64\n  : slowBigIntToBase64;\nexport const base64ToBigInt = (DataView.prototype as any).getBigInt64\n  ? modernBase64ToBigInt\n  : slowBase64ToBigInt;\n\nconst MAX_IDENTIFIER_LEN = 1024;\n\nfunction validateObjectField(k: string) {\n  if (k.length > MAX_IDENTIFIER_LEN) {\n    throw new Error(\n      `Field name ${k} exceeds maximum field name length ${MAX_IDENTIFIER_LEN}.`,\n    );\n  }\n  if (k.startsWith(\"$\")) {\n    throw new Error(`Field name ${k} starts with a '$', which is reserved.`);\n  }\n  for (let i = 0; i < k.length; i += 1) {\n    const charCode = k.charCodeAt(i);\n    // Non-control ASCII characters\n    if (charCode < 32 || charCode >= 127) {\n      throw new Error(\n        `Field name ${k} has invalid character '${k[i]}': Field names can only contain non-control ASCII characters`,\n      );\n    }\n  }\n}\n\n/**\n * Parse a Convex value from its JSON representation.\n *\n * This function will deserialize serialized Int64s to `BigInt`s, Bytes to `ArrayBuffer`s etc.\n *\n * To learn more about Convex values, see [Types](https://docs.convex.dev/using/types).\n *\n * @param value - The JSON representation of a Convex value previously created with {@link convexToJson}.\n * @returns The JavaScript representation of the Convex value.\n *\n * @public\n */\nexport function jsonToConvex(value: JSONValue): Value {\n  if (value === null) {\n    return value;\n  }\n  if (typeof value === \"boolean\") {\n    return value;\n  }\n  if (typeof value === \"number\") {\n    return value;\n  }\n  if (typeof value === \"string\") {\n    return value;\n  }\n  if (Array.isArray(value)) {\n    return value.map((value) => jsonToConvex(value));\n  }\n  if (typeof value !== \"object\") {\n    throw new Error(`Unexpected type of ${value as any}`);\n  }\n  const entries = Object.entries(value);\n  if (entries.length === 1) {\n    const key = entries[0][0];\n    if (key === \"$bytes\") {\n      if (typeof value.$bytes !== \"string\") {\n        throw new Error(`Malformed $bytes field on ${value as any}`);\n      }\n      return Base64.toByteArray(value.$bytes).buffer;\n    }\n    if (key === \"$integer\") {\n      if (typeof value.$integer !== \"string\") {\n        throw new Error(`Malformed $integer field on ${value as any}`);\n      }\n      return base64ToBigInt(value.$integer);\n    }\n    if (key === \"$float\") {\n      if (typeof value.$float !== \"string\") {\n        throw new Error(`Malformed $float field on ${value as any}`);\n      }\n      const floatBytes = Base64.toByteArray(value.$float);\n      if (floatBytes.byteLength !== 8) {\n        throw new Error(\n          `Received ${floatBytes.byteLength} bytes, expected 8 for $float`,\n        );\n      }\n      const floatBytesView = new DataView(floatBytes.buffer);\n      const float = floatBytesView.getFloat64(0, LITTLE_ENDIAN);\n      if (!isSpecial(float)) {\n        throw new Error(`Float ${float} should be encoded as a number`);\n      }\n      return float;\n    }\n    if (key === \"$set\") {\n      throw new Error(\n        `Received a Set which is no longer supported as a Convex type.`,\n      );\n    }\n    if (key === \"$map\") {\n      throw new Error(\n        `Received a Map which is no longer supported as a Convex type.`,\n      );\n    }\n  }\n  const out: { [key: string]: Value } = {};\n  for (const [k, v] of Object.entries(value)) {\n    validateObjectField(k);\n    out[k] = jsonToConvex(v);\n  }\n  return out;\n}\n\nexport function stringifyValueForError(value: any) {\n  return JSON.stringify(value, (_key, value) => {\n    if (value === undefined) {\n      // By default `JSON.stringify` converts undefined, functions, symbols,\n      // Infinity, and NaN to null which produces a confusing error message.\n      // We deal with `undefined` specifically because it's the most common.\n      // Ideally we'd use a pretty-printing library that prints `undefined`\n      // (no quotes), but it might not be worth the bundle size cost.\n      return \"undefined\";\n    }\n    if (typeof value === \"bigint\") {\n      // `JSON.stringify` throws on bigints by default.\n      return `${value.toString()}n`;\n    }\n    return value;\n  });\n}\n\nfunction convexToJsonInternal(\n  value: Value,\n  originalValue: Value,\n  context: string,\n  includeTopLevelUndefined: boolean,\n): JSONValue {\n  if (value === undefined) {\n    const contextText =\n      context &&\n      ` (present at path ${context} in original object ${stringifyValueForError(\n        originalValue,\n      )})`;\n    throw new Error(\n      `undefined is not a valid Convex value${contextText}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`,\n    );\n  }\n  if (value === null) {\n    return value;\n  }\n  if (typeof value === \"bigint\") {\n    if (value < MIN_INT64 || MAX_INT64 < value) {\n      throw new Error(\n        `BigInt ${value} does not fit into a 64-bit signed integer.`,\n      );\n    }\n    return { $integer: bigIntToBase64(value) };\n  }\n  if (typeof value === \"number\") {\n    if (isSpecial(value)) {\n      const buffer = new ArrayBuffer(8);\n      new DataView(buffer).setFloat64(0, value, LITTLE_ENDIAN);\n      return { $float: Base64.fromByteArray(new Uint8Array(buffer)) };\n    } else {\n      return value;\n    }\n  }\n  if (typeof value === \"boolean\") {\n    return value;\n  }\n  if (typeof value === \"string\") {\n    return value;\n  }\n  if (value instanceof ArrayBuffer) {\n    return { $bytes: Base64.fromByteArray(new Uint8Array(value)) };\n  }\n  if (Array.isArray(value)) {\n    return value.map((value, i) =>\n      convexToJsonInternal(value, originalValue, context + `[${i}]`, false),\n    );\n  }\n  if (value instanceof Set) {\n    throw new Error(\n      errorMessageForUnsupportedType(context, \"Set\", [...value], originalValue),\n    );\n  }\n  if (value instanceof Map) {\n    throw new Error(\n      errorMessageForUnsupportedType(context, \"Map\", [...value], originalValue),\n    );\n  }\n\n  if (!isSimpleObject(value)) {\n    const theType = value?.constructor?.name;\n    const typeName = theType ? `${theType} ` : \"\";\n    throw new Error(\n      errorMessageForUnsupportedType(context, typeName, value, originalValue),\n    );\n  }\n\n  const out: { [key: string]: JSONValue } = {};\n  const entries = Object.entries(value);\n  entries.sort(([k1, _v1], [k2, _v2]) => (k1 === k2 ? 0 : k1 < k2 ? -1 : 1));\n  for (const [k, v] of entries) {\n    if (v !== undefined) {\n      validateObjectField(k);\n      out[k] = convexToJsonInternal(v, originalValue, context + `.${k}`, false);\n    } else if (includeTopLevelUndefined) {\n      validateObjectField(k);\n      out[k] = convexOrUndefinedToJsonInternal(\n        v,\n        originalValue,\n        context + `.${k}`,\n      );\n    }\n  }\n  return out;\n}\n\nfunction errorMessageForUnsupportedType(\n  context: string,\n  typeName: string,\n  value: any,\n  originalValue: any,\n) {\n  if (context) {\n    return `${typeName}${stringifyValueForError(\n      value,\n    )} is not a supported Convex type (present at path ${context} in original object ${stringifyValueForError(\n      originalValue,\n    )}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`;\n  } else {\n    return `${typeName}${stringifyValueForError(\n      value,\n    )} is not a supported Convex type.`;\n  }\n}\n\n// convexOrUndefinedToJsonInternal wrapper exists so we can pipe through the\n// `originalValue` and `context` through for better error messaging.\nfunction convexOrUndefinedToJsonInternal(\n  value: Value | undefined,\n  originalValue: Value | undefined,\n  context: string,\n): JSONValue {\n  if (value === undefined) {\n    return { $undefined: null };\n  } else {\n    if (originalValue === undefined) {\n      // This should not happen.\n      throw new Error(\n        `Programming error. Current value is ${stringifyValueForError(\n          value,\n        )} but original value is undefined`,\n      );\n    }\n    return convexToJsonInternal(value, originalValue, context, false);\n  }\n}\n\n/**\n * Convert a Convex value to its JSON representation.\n *\n * Use {@link jsonToConvex} to recreate the original value.\n *\n * To learn more about Convex values, see [Types](https://docs.convex.dev/using/types).\n *\n * @param value - A Convex value to convert into JSON.\n * @returns The JSON representation of `value`.\n *\n * @public\n */\nexport function convexToJson(value: Value): JSONValue {\n  return convexToJsonInternal(value, value, \"\", false);\n}\n\n// Convert a Convex value or `undefined` into its JSON representation.\n// `undefined` is used in filters to represent a missing object field.\nexport function convexOrUndefinedToJson(value: Value | undefined): JSONValue {\n  return convexOrUndefinedToJsonInternal(value, value, \"\");\n}\n\n/**\n * Similar to convexToJson but also serializes top level undefined fields\n * using convexOrUndefinedToJson().\n *\n * @param value - A Convex value to convert into JSON.\n * @returns The JSON representation of `value`.\n */\nexport function patchValueToJson(value: Value): JSONValue {\n  return convexToJsonInternal(value, value, \"\", true);\n}\n", "import { GenericId } from \"./index.js\";\nimport { GenericValidator } from \"./validator.js\";\nimport { JSONValue, convexToJson } from \"./value.js\";\n\ntype TableNameFromType<T> =\n  T extends GenericId<infer TableName> ? TableName : string;\n\n/**\n * Avoid using `instanceof BaseValidator`; this is inheritence for code reuse\n * not type heirarchy.\n */\nabstract class BaseValidator<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = never,\n> {\n  /**\n   * Only for TypeScript, the TS type of the JS values validated\n   * by this validator.\n   */\n  readonly type!: Type;\n  /**\n   * Only for TypeScript, if this an Object validator, then\n   * this is the TS type of its property names.\n   */\n  readonly fieldPaths!: FieldPaths;\n\n  /**\n   * Whether this is an optional Object property value validator.\n   */\n  readonly isOptional: IsOptional;\n\n  /**\n   * Always `\"true\"`.\n   */\n  readonly isConvexValidator: true;\n\n  constructor({ isOptional }: { isOptional: IsOptional }) {\n    this.isOptional = isOptional;\n    this.isConvexValidator = true;\n  }\n  /** @deprecated - use isOptional instead */\n  get optional(): boolean {\n    return this.isOptional === \"optional\" ? true : false;\n  }\n  /** @internal */\n  abstract get json(): ValidatorJSON;\n  /** @internal */\n  abstract asOptional(): Validator<Type | undefined, \"optional\", FieldPaths>;\n}\n\n/**\n * The type of the `v.id(tableName)` validator.\n */\nexport class VId<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The name of the table that the validated IDs must belong to.\n   */\n  readonly tableName: TableNameFromType<Type>;\n\n  /**\n   * The kind of validator, `\"id\"`.\n   */\n  readonly kind = \"id\" as const;\n\n  /**\n   * Usually you'd use `v.id(tableName)` instead.\n   */\n  constructor({\n    isOptional,\n    tableName,\n  }: {\n    isOptional: IsOptional;\n    tableName: TableNameFromType<Type>;\n  }) {\n    super({ isOptional });\n    this.tableName = tableName;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: \"id\", tableName: this.tableName };\n  }\n  /** @internal */\n  asOptional() {\n    return new VId<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n      tableName: this.tableName,\n    });\n  }\n}\n\n/**\n * The type of the `v.float64()` validator.\n */\nexport class VFloat64<\n  Type = number,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"float64\"`.\n   */\n  readonly kind = \"float64\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    // Server expects the old name `number` string instead of `float64`.\n    return { type: \"number\" };\n  }\n  /** @internal */\n  asOptional() {\n    return new VFloat64<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.int64()` validator.\n */\nexport class VInt64<\n  Type = bigint,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"int64\"`.\n   */\n  readonly kind = \"int64\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    // Server expects the old name `bigint`.\n    return { type: \"bigint\" };\n  }\n  /** @internal */\n  asOptional() {\n    return new VInt64<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.boolean()` validator.\n */\nexport class VBoolean<\n  Type = boolean,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"boolean\"`.\n   */\n  readonly kind = \"boolean\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VBoolean<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.bytes()` validator.\n */\nexport class VBytes<\n  Type = ArrayBuffer,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"bytes\"`.\n   */\n  readonly kind = \"bytes\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VBytes<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.string()` validator.\n */\nexport class VString<\n  Type = string,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"string\"`.\n   */\n  readonly kind = \"string\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VString<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.null()` validator.\n */\nexport class VNull<\n  Type = null,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The kind of validator, `\"null\"`.\n   */\n  readonly kind = \"null\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return { type: this.kind };\n  }\n  /** @internal */\n  asOptional() {\n    return new VNull<Type | undefined, \"optional\">({ isOptional: \"optional\" });\n  }\n}\n\n/**\n * The type of the `v.any()` validator.\n */\nexport class VAny<\n  Type = any,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The kind of validator, `\"any\"`.\n   */\n  readonly kind = \"any\" as const;\n\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VAny<Type | undefined, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n    });\n  }\n}\n\n/**\n * The type of the `v.object()` validator.\n */\nexport class VObject<\n  Type,\n  Fields extends Record<string, GenericValidator>,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = {\n    [Property in keyof Fields]:\n      | JoinFieldPaths<Property & string, Fields[Property][\"fieldPaths\"]>\n      | Property;\n  }[keyof Fields] &\n    string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * An object with the validator for each property.\n   */\n  readonly fields: Fields;\n\n  /**\n   * The kind of validator, `\"object\"`.\n   */\n  readonly kind = \"object\" as const;\n\n  /**\n   * Usually you'd use `v.object({ ... })` instead.\n   */\n  constructor({\n    isOptional,\n    fields,\n  }: {\n    isOptional: IsOptional;\n    fields: Fields;\n  }) {\n    super({ isOptional });\n    this.fields = fields;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: globalThis.Object.fromEntries(\n        globalThis.Object.entries(this.fields).map(([k, v]) => [\n          k,\n          {\n            fieldType: v.json,\n            optional: v.isOptional === \"optional\" ? true : false,\n          },\n        ]),\n      ),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VObject<Type | undefined, Fields, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n      fields: this.fields,\n    });\n  }\n}\n\n/**\n * The type of the `v.literal()` validator.\n */\nexport class VLiteral<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The value that the validated values must be equal to.\n   */\n  readonly value: Type;\n\n  /**\n   * The kind of validator, `\"literal\"`.\n   */\n  readonly kind = \"literal\" as const;\n\n  /**\n   * Usually you'd use `v.literal(value)` instead.\n   */\n  constructor({ isOptional, value }: { isOptional: IsOptional; value: Type }) {\n    super({ isOptional });\n    this.value = value;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: convexToJson(this.value as string | boolean | number | bigint),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VLiteral<Type | undefined, \"optional\">({\n      isOptional: \"optional\",\n      value: this.value,\n    });\n  }\n}\n\n/**\n * The type of the `v.array()` validator.\n */\nexport class VArray<\n  Type,\n  Element extends Validator<any, \"required\", any>,\n  IsOptional extends OptionalProperty = \"required\",\n> extends BaseValidator<Type, IsOptional> {\n  /**\n   * The validator for the elements of the array.\n   */\n  readonly element: Element;\n\n  /**\n   * The kind of validator, `\"array\"`.\n   */\n  readonly kind = \"array\" as const;\n\n  /**\n   * Usually you'd use `v.array(element)` instead.\n   */\n  constructor({\n    isOptional,\n    element,\n  }: {\n    isOptional: IsOptional;\n    element: Element;\n  }) {\n    super({ isOptional });\n    this.element = element;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: this.element.json,\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VArray<Type | undefined, Element, \"optional\">({\n      isOptional: \"optional\",\n      element: this.element,\n    });\n  }\n}\n\n/**\n * The type of the `v.record()` validator.\n */\nexport class VRecord<\n  Type,\n  Key extends Validator<string, \"required\", any>,\n  Value extends Validator<any, \"required\", any>,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = string,\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The validator for the keys of the record.\n   */\n  readonly key: Key;\n\n  /**\n   * The validator for the values of the record.\n   */\n  readonly value: Value;\n\n  /**\n   * The kind of validator, `\"record\"`.\n   */\n  readonly kind = \"record\" as const;\n\n  /**\n   * Usually you'd use `v.record(key, value)` instead.\n   */\n  constructor({\n    isOptional,\n    key,\n    value,\n  }: {\n    isOptional: IsOptional;\n    key: Key;\n    value: Value;\n  }) {\n    super({ isOptional });\n    if ((key.isOptional as OptionalProperty) === \"optional\") {\n      throw new Error(\"Record validator cannot have optional keys\");\n    }\n    if ((value.isOptional as OptionalProperty) === \"optional\") {\n      throw new Error(\"Record validator cannot have optional values\");\n    }\n    this.key = key;\n    this.value = value;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      // This cast is needed because TypeScript thinks the key type is too wide\n      keys: this.key.json as RecordKeyValidatorJSON,\n      values: {\n        fieldType: this.value.json,\n        optional: false,\n      },\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VRecord<Type | undefined, Key, Value, \"optional\", FieldPaths>({\n      isOptional: \"optional\",\n      key: this.key,\n      value: this.value,\n    });\n  }\n}\n\n/**\n * The type of the `v.union()` validator.\n */\nexport class VUnion<\n  Type,\n  T extends Validator<any, \"required\", any>[],\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = T[number][\"fieldPaths\"],\n> extends BaseValidator<Type, IsOptional, FieldPaths> {\n  /**\n   * The array of validators, one of which must match the value.\n   */\n  readonly members: T;\n\n  /**\n   * The kind of validator, `\"union\"`.\n   */\n  readonly kind = \"union\" as const;\n\n  /**\n   * Usually you'd use `v.union(...members)` instead.\n   */\n  constructor({ isOptional, members }: { isOptional: IsOptional; members: T }) {\n    super({ isOptional });\n    this.members = members;\n  }\n  /** @internal */\n  get json(): ValidatorJSON {\n    return {\n      type: this.kind,\n      value: this.members.map((v) => v.json),\n    };\n  }\n  /** @internal */\n  asOptional() {\n    return new VUnion<Type | undefined, T, \"optional\">({\n      isOptional: \"optional\",\n      members: this.members,\n    });\n  }\n}\n\n// prettier-ignore\nexport type VOptional<T extends Validator<any, OptionalProperty, any>> =\n  T extends VId<infer Type, OptionalProperty> ? VId<Type | undefined, \"optional\">\n  : T extends VString<infer Type, OptionalProperty>\n    ? VString<Type | undefined, \"optional\">\n  : T extends VFloat64<infer Type, OptionalProperty>\n    ? VFloat64<Type | undefined, \"optional\">\n  : T extends VInt64<infer Type, OptionalProperty>\n    ? VInt64<Type | undefined, \"optional\">\n  : T extends VBoolean<infer Type, OptionalProperty>\n    ? VBoolean<Type | undefined, \"optional\">\n  : T extends VNull<infer Type, OptionalProperty>\n    ? VNull<Type | undefined, \"optional\">\n  : T extends VAny<infer Type, OptionalProperty>\n    ? VAny<Type | undefined, \"optional\">\n  : T extends VLiteral<infer Type, OptionalProperty>\n    ? VLiteral<Type | undefined, \"optional\">\n  : T extends VBytes<infer Type, OptionalProperty>\n    ? VBytes<Type | undefined, \"optional\">\n  : T extends VObject< infer Type, infer Fields, OptionalProperty, infer FieldPaths>\n    ? VObject<Type | undefined, Fields, \"optional\", FieldPaths>\n  : T extends VArray<infer Type, infer Element, OptionalProperty>\n    ? VArray<Type | undefined, Element, \"optional\">\n  : T extends VRecord< infer Type, infer Key, infer Value, OptionalProperty, infer FieldPaths>\n    ? VRecord<Type | undefined, Key, Value, \"optional\", FieldPaths>\n  : T extends VUnion<infer Type, infer Members, OptionalProperty, infer FieldPaths>\n    ? VUnion<Type | undefined, Members, \"optional\", FieldPaths>\n  : never\n\n/**\n * Type representing whether a property in an object is optional or required.\n *\n * @public\n */\nexport type OptionalProperty = \"optional\" | \"required\";\n\n/**\n * A validator for a Convex value.\n *\n * This should be constructed using the validator builder, {@link v}.\n *\n * A validator encapsulates:\n * - The TypeScript type of this value.\n * - Whether this field should be optional if it's included in an object.\n * - The TypeScript type for the set of index field paths that can be used to\n * build indexes on this value.\n * - A JSON representation of the validator.\n *\n * Specific types of validators contain additional information: for example\n * an `ArrayValidator` contains an `element` property with the validator\n * used to validate each element of the list. Use the shared 'kind' property\n * to identity the type of validator.\n *\n * More validators can be added in future releases so an exhaustive\n * switch statement on validator `kind` should be expected to break\n * in future releases of Convex.\n *\n * @public\n */\nexport type Validator<\n  Type,\n  IsOptional extends OptionalProperty = \"required\",\n  FieldPaths extends string = never,\n> =\n  | VId<Type, IsOptional>\n  | VString<Type, IsOptional>\n  | VFloat64<Type, IsOptional>\n  | VInt64<Type, IsOptional>\n  | VBoolean<Type, IsOptional>\n  | VNull<Type, IsOptional>\n  | VAny<Type, IsOptional>\n  | VLiteral<Type, IsOptional>\n  | VBytes<Type, IsOptional>\n  | VObject<\n      Type,\n      Record<string, Validator<any, OptionalProperty, any>>,\n      IsOptional,\n      FieldPaths\n    >\n  | VArray<Type, Validator<any, \"required\", any>, IsOptional>\n  | VRecord<\n      Type,\n      Validator<string, \"required\", any>,\n      Validator<any, \"required\", any>,\n      IsOptional,\n      FieldPaths\n    >\n  | VUnion<Type, Validator<any, \"required\", any>[], IsOptional, FieldPaths>;\n\n/**\n * Join together two index field paths.\n *\n * This is used within the validator builder, {@link v}.\n * @public\n */\nexport type JoinFieldPaths<\n  Start extends string,\n  End extends string,\n> = `${Start}.${End}`;\n\nexport type ObjectFieldType = { fieldType: ValidatorJSON; optional: boolean };\n\nexport type ValidatorJSON =\n  | { type: \"null\" }\n  | { type: \"number\" }\n  | { type: \"bigint\" }\n  | { type: \"boolean\" }\n  | { type: \"string\" }\n  | { type: \"bytes\" }\n  | { type: \"any\" }\n  | { type: \"literal\"; value: JSONValue }\n  | { type: \"id\"; tableName: string }\n  | { type: \"array\"; value: ValidatorJSON }\n  | {\n      type: \"record\";\n      keys: RecordKeyValidatorJSON;\n      values: RecordValueValidatorJSON;\n    }\n  | { type: \"object\"; value: Record<string, ObjectFieldType> }\n  | { type: \"union\"; value: ValidatorJSON[] };\n\nexport type RecordKeyValidatorJSON =\n  | { type: \"string\" }\n  | { type: \"id\"; tableName: string }\n  | { type: \"union\"; value: RecordKeyValidatorJSON[] };\n\nexport type RecordValueValidatorJSON = ObjectFieldType & { optional: false };\n", "import { Expand } from \"../type_utils.js\";\nimport { GenericId } from \"./index.js\";\nimport {\n  OptionalProperty,\n  VAny,\n  VArray,\n  VBoolean,\n  VBytes,\n  VFloat64,\n  VId,\n  VInt64,\n  VLiteral,\n  VNull,\n  VObject,\n  VOptional,\n  VRecord,\n  VString,\n  VUnion,\n  Validator,\n} from \"./validators.js\";\n\n/**\n * The type that all validators must extend.\n *\n * @public\n */\nexport type GenericValidator = Validator<any, any, any>;\n\nexport function isValidator(v: any): v is GenericValidator {\n  return !!v.isConvexValidator;\n}\n\n/**\n * Coerce an object with validators as properties to a validator.\n * If a validator is passed, return it.\n *\n * @public\n */\nexport function asObjectValidator<\n  V extends Validator<any, any, any> | PropertyValidators,\n>(\n  obj: V,\n): V extends Validator<any, any, any>\n  ? V\n  : V extends PropertyValidators\n    ? Validator<ObjectType<V>>\n    : never {\n  if (isValidator(obj)) {\n    return obj as any;\n  } else {\n    return v.object(obj as PropertyValidators) as any;\n  }\n}\n\n/**\n * Coerce an object with validators as properties to a validator.\n * If a validator is passed, return it.\n *\n * @public\n */\nexport type AsObjectValidator<\n  V extends Validator<any, any, any> | PropertyValidators,\n> =\n  V extends Validator<any, any, any>\n    ? V\n    : V extends PropertyValidators\n      ? Validator<ObjectType<V>>\n      : never;\n\n/**\n * The validator builder.\n *\n * This builder allows you to build validators for Convex values.\n *\n * Validators can be used in [schema definitions](https://docs.convex.dev/database/schemas)\n * and as input validators for Convex functions.\n *\n * @public\n */\nexport const v = {\n  /**\n   * Validates that the value corresponds to an ID of a document in given table.\n   * @param tableName The name of the table.\n   */\n  id: <TableName extends string>(tableName: TableName) => {\n    return new VId<GenericId<TableName>>({\n      isOptional: \"required\",\n      tableName,\n    });\n  },\n\n  /**\n   * Validates that the value is of type Null.\n   */\n  null: () => {\n    return new VNull({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Float64 (Number in JS).\n   *\n   * Alias for `v.float64()`\n   */\n  number: () => {\n    return new VFloat64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Float64 (Number in JS).\n   */\n  float64: () => {\n    return new VFloat64({ isOptional: \"required\" });\n  },\n\n  /**\n   * @deprecated Use `v.int64()` instead\n   */\n  bigint: () => {\n    return new VInt64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Int64 (BigInt in JS).\n   */\n  int64: () => {\n    return new VInt64({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of type Boolean.\n   */\n  boolean: () => {\n    return new VBoolean({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of type String.\n   */\n  string: () => {\n    return new VString({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is of Convex type Bytes (constructed in JS via `ArrayBuffer`).\n   */\n  bytes: () => {\n    return new VBytes({ isOptional: \"required\" });\n  },\n\n  /**\n   * Validates that the value is equal to the given literal value.\n   * @param literal The literal value to compare against.\n   */\n  literal: <T extends string | number | bigint | boolean>(literal: T) => {\n    return new VLiteral<T>({ isOptional: \"required\", value: literal });\n  },\n\n  /**\n   * Validates that the value is an Array of the given element type.\n   * @param element The validator for the elements of the array.\n   */\n  array: <T extends Validator<any, \"required\", any>>(element: T) => {\n    return new VArray<T[\"type\"][], T>({ isOptional: \"required\", element });\n  },\n\n  /**\n   * Validates that the value is an Object with the given properties.\n   * @param fields An object specifying the validator for each property.\n   */\n  object: <T extends PropertyValidators>(fields: T) => {\n    return new VObject<ObjectType<T>, T>({ isOptional: \"required\", fields });\n  },\n\n  /**\n   * Validates that the value is a Record with keys and values that match the given types.\n   * @param keys The validator for the keys of the record. This cannot contain string literals.\n   * @param values The validator for the values of the record.\n   */\n  record: <\n    Key extends Validator<string, \"required\", any>,\n    Value extends Validator<any, \"required\", any>,\n  >(\n    keys: Key,\n    values: Value,\n  ) => {\n    return new VRecord<Record<Infer<Key>, Value[\"type\"]>, Key, Value>({\n      isOptional: \"required\",\n      key: keys,\n      value: values,\n    });\n  },\n\n  /**\n   * Validates that the value matches one of the given validators.\n   * @param members The validators to match against.\n   */\n  union: <T extends Validator<any, \"required\", any>[]>(...members: T) => {\n    return new VUnion<T[number][\"type\"], T>({\n      isOptional: \"required\",\n      members,\n    });\n  },\n\n  /**\n   * Does not validate the value.\n   */\n  any: () => {\n    return new VAny({ isOptional: \"required\" });\n  },\n\n  /**\n   * Allows not specifying a value for a property in an Object.\n   * @param value The property value validator to make optional.\n   *\n   * ```typescript\n   * const objectWithOptionalFields = v.object({\n   *   requiredField: v.string(),\n   *   optionalField: v.optional(v.string()),\n   * });\n   * ```\n   */\n  optional: <T extends GenericValidator>(value: T) => {\n    return value.asOptional() as VOptional<T>;\n  },\n};\n\n/**\n * Validators for each property of an object.\n *\n * This is represented as an object mapping the property name to its\n * {@link Validator}.\n *\n * @public\n */\nexport type PropertyValidators = Record<\n  string,\n  Validator<any, OptionalProperty, any>\n>;\n\n/**\n * Compute the type of an object from {@link PropertyValidators}.\n *\n * @public\n */\nexport type ObjectType<Fields extends PropertyValidators> = Expand<\n  // Map each key to the corresponding property validator's type making\n  // the optional ones optional.\n  {\n    // This `Exclude<..., undefined>` does nothing unless\n    // the tsconfig.json option `\"exactOptionalPropertyTypes\": true,`\n    // is used. When it is it results in a more accurate type.\n    // When it is not the `Exclude` removes `undefined` but it is\n    // added again by the optional property.\n    [Property in OptionalKeys<Fields>]?: Exclude<\n      Infer<Fields[Property]>,\n      undefined\n    >;\n  } & {\n    [Property in RequiredKeys<Fields>]: Infer<Fields[Property]>;\n  }\n>;\n\ntype OptionalKeys<PropertyValidators extends Record<string, GenericValidator>> =\n  {\n    [Property in keyof PropertyValidators]: PropertyValidators[Property][\"isOptional\"] extends \"optional\"\n      ? Property\n      : never;\n  }[keyof PropertyValidators];\n\ntype RequiredKeys<PropertyValidators extends Record<string, GenericValidator>> =\n  Exclude<keyof PropertyValidators, OptionalKeys<PropertyValidators>>;\n\n/**\n * Extract a TypeScript type from a validator.\n *\n * Example usage:\n * ```ts\n * const objectSchema = v.object({\n *   property: v.string(),\n * });\n * type MyObject = Infer<typeof objectSchema>; // { property: string }\n * ```\n * @typeParam V - The type of a {@link Validator} constructed with {@link v}.\n *\n * @public\n */\nexport type Infer<T extends Validator<any, OptionalProperty, any>> = T[\"type\"];\n", "import { Value, stringifyValueForError } from \"./value.js\";\n\nconst IDENTIFYING_FIELD = Symbol.for(\"ConvexError\");\n\nexport class ConvexError<TData extends Value> extends Error {\n  name = \"ConvexError\";\n  data: TData;\n  [IDENTIFYING_FIELD] = true;\n\n  constructor(data: TData) {\n    super(typeof data === \"string\" ? data : stringifyValueForError(data));\n    this.data = data;\n  }\n}\n", "/**\n * Taken from https://github.com/rocicorp/compare-utf8/blob/main/LICENSE\n * (Apache Version 2.0, January 2004)\n */\n\n/**\n * This is copied here instead of added as a dependency to avoid bundling issues.\n */\n\n/**\n * Compares two JavaScript strings as if they were UTF-8 encoded byte arrays.\n * @param {string} a\n * @param {string} b\n * @returns {number}\n */\nexport function compareUTF8(a: string, b: string): number {\n  const aLength = a.length;\n  const bLength = b.length;\n  const length = Math.min(aLength, bLength);\n  for (let i = 0; i < length; ) {\n    const aCodePoint = a.codePointAt(i)!;\n    const bCodePoint = b.codePointAt(i)!;\n    if (aCodePoint !== bCodePoint) {\n      // Code points below 0x80 are represented the same way in UTF-8 as in\n      // UTF-16.\n      if (aCodePoint < 0x80 && bCodePoint < 0x80) {\n        return aCodePoint - bCodePoint;\n      }\n\n      // get the UTF-8 bytes for the code points\n      const aLength = utf8Bytes(aCodePoint, aBytes);\n      const bLength = utf8Bytes(bCodePoint, bBytes);\n      return compareArrays(aBytes, aLength, bBytes, bLength);\n    }\n\n    i += utf16LengthForCodePoint(aCodePoint);\n  }\n\n  return aLength - bLength;\n}\n\n/**\n * @param {number[]} a\n * @param {number} aLength\n * @param {number[]} b\n * @param {number} bLength\n * @returns {number}\n */\nfunction compareArrays(\n  a: number[],\n  aLength: number,\n  b: number[],\n  bLength: number,\n) {\n  const length = Math.min(aLength, bLength);\n  for (let i = 0; i < length; i++) {\n    const aValue = a[i];\n    const bValue = b[i];\n    if (aValue !== bValue) {\n      return aValue - bValue;\n    }\n  }\n  return aLength - bLength;\n}\n\n/**\n * @param {number} aCodePoint\n * @returns {number}\n */\nexport function utf16LengthForCodePoint(aCodePoint: number) {\n  return aCodePoint > 0xffff ? 2 : 1;\n}\n\n// 2 preallocated arrays for utf8Bytes.\nconst arr = () => Array.from({ length: 4 }, () => 0);\nconst aBytes = arr();\nconst bBytes = arr();\n\n/**\n * @param {number} codePoint\n * @param {number[]} bytes\n * @returns {number}\n */\nfunction utf8Bytes(codePoint: number, bytes: number[]) {\n  if (codePoint < 0x80) {\n    bytes[0] = codePoint;\n    return 1;\n  }\n\n  let count;\n  let offset;\n\n  if (codePoint <= 0x07ff) {\n    count = 1;\n    offset = 0xc0;\n  } else if (codePoint <= 0xffff) {\n    count = 2;\n    offset = 0xe0;\n  } else if (codePoint <= 0x10ffff) {\n    count = 3;\n    offset = 0xf0;\n  } else {\n    throw new Error(\"Invalid code point\");\n  }\n\n  bytes[0] = (codePoint >> (6 * count)) + offset;\n  let i = 1;\n  for (; count > 0; count--) {\n    const temp = codePoint >> (6 * (count - 1));\n    bytes[i++] = 0x80 | (temp & 0x3f);\n  }\n  return i;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function greaterThan(a: string, b: string) {\n  return compareUTF8(a, b) > 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function greaterThanEq(a: string, b: string) {\n  return compareUTF8(a, b) >= 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function lessThan(a: string, b: string) {\n  return compareUTF8(a, b) < 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function lessThanEq(a: string, b: string) {\n  return compareUTF8(a, b) <= 0;\n}\n", "import { Value } from \"./value.js\";\nimport { compareUTF8 } from \"./compare_utf8.js\";\n\nexport function compareValues(k1: Value | undefined, k2: Value | undefined) {\n  return compareAsTuples(makeComparable(k1), makeComparable(k2));\n}\n\nfunction compareAsTuples<T>(a: [number, T], b: [number, T]): number {\n  if (a[0] === b[0]) {\n    return compareSameTypeValues(a[1], b[1]);\n  } else if (a[0] < b[0]) {\n    return -1;\n  }\n  return 1;\n}\n\nfunction compareSameTypeValues<T>(v1: T, v2: T): number {\n  if (v1 === undefined || v1 === null) {\n    return 0;\n  }\n  if (typeof v1 === \"number\") {\n    if (typeof v2 !== \"number\") {\n      throw new Error(`Unexpected type ${v2 as any}`);\n    }\n    return compareNumbers(v1, v2);\n  }\n  if (typeof v1 === \"string\") {\n    if (typeof v2 !== \"string\") {\n      throw new Error(`Unexpected type ${v2 as any}`);\n    }\n    return compareUTF8(v1, v2);\n  }\n  if (\n    typeof v1 === \"bigint\" ||\n    typeof v1 === \"boolean\" ||\n    typeof v1 === \"string\"\n  ) {\n    return v1 < v2 ? -1 : v1 === v2 ? 0 : 1;\n  }\n  if (!Array.isArray(v1) || !Array.isArray(v2)) {\n    throw new Error(`Unexpected type ${v1 as any}`);\n  }\n  for (let i = 0; i < v1.length && i < v2.length; i++) {\n    const cmp = compareAsTuples(v1[i], v2[i]);\n    if (cmp !== 0) {\n      return cmp;\n    }\n  }\n  if (v1.length < v2.length) {\n    return -1;\n  }\n  if (v1.length > v2.length) {\n    return 1;\n  }\n  return 0;\n}\n\nfunction compareNumbers(v1: number, v2: number): number {\n  // Handle NaN values\n  if (isNaN(v1) || isNaN(v2)) {\n    // Create DataViews for bit-level comparison\n    const buffer1 = new ArrayBuffer(8);\n    const buffer2 = new ArrayBuffer(8);\n    new DataView(buffer1).setFloat64(0, v1, /* little-endian */ true);\n    new DataView(buffer2).setFloat64(0, v2, /* little-endian */ true);\n\n    // Read as BigInt to compare bits\n    const v1Bits = BigInt(\n      new DataView(buffer1).getBigInt64(0, /* little-endian */ true),\n    );\n    const v2Bits = BigInt(\n      new DataView(buffer2).getBigInt64(0, /* little-endian */ true),\n    );\n\n    // The sign bit is the most significant bit (bit 63)\n    const v1Sign = (v1Bits & 0x8000000000000000n) !== 0n;\n    const v2Sign = (v2Bits & 0x8000000000000000n) !== 0n;\n\n    // If one value is NaN and the other isn't, use sign bits first\n    if (isNaN(v1) !== isNaN(v2)) {\n      // If v1 is NaN, compare based on sign bits\n      if (isNaN(v1)) {\n        return v1Sign ? -1 : 1;\n      }\n      // If v2 is NaN, compare based on sign bits\n      return v2Sign ? 1 : -1;\n    }\n\n    // If both are NaN, compare their binary representations\n    if (v1Sign !== v2Sign) {\n      return v1Sign ? -1 : 1; // true means negative\n    }\n    return v1Bits < v2Bits ? -1 : v1Bits === v2Bits ? 0 : 1;\n  }\n\n  if (Object.is(v1, v2)) {\n    return 0;\n  }\n\n  if (Object.is(v1, -0)) {\n    return Object.is(v2, 0) ? -1 : -Math.sign(v2);\n  }\n  if (Object.is(v2, -0)) {\n    return Object.is(v1, 0) ? 1 : Math.sign(v1);\n  }\n\n  // Handle regular number comparison\n  return v1 < v2 ? -1 : 1;\n}\n\n// Returns an array which can be compared to other arrays as if they were tuples.\n// For example, [1, null] < [2, 1n] means null sorts before all bigints\n// And [3, 5] < [3, 6] means floats sort as expected\n// And [7, [[5, \"a\"]]] < [7, [[5, \"a\"], [5, \"b\"]]] means arrays sort as expected\nfunction makeComparable(v: Value | undefined): [number, any] {\n  if (v === undefined) {\n    return [0, undefined];\n  }\n  if (v === null) {\n    return [1, null];\n  }\n  if (typeof v === \"bigint\") {\n    return [2, v];\n  }\n  if (typeof v === \"number\") {\n    return [3, v];\n  }\n  if (typeof v === \"boolean\") {\n    return [4, v];\n  }\n  if (typeof v === \"string\") {\n    return [5, v];\n  }\n  if (v instanceof ArrayBuffer) {\n    return [6, Array.from(new Uint8Array(v)).map(makeComparable)];\n  }\n  if (Array.isArray(v)) {\n    return [7, v.map(makeComparable)];\n  }\n  // Otherwise, it's an POJO.\n  const keys = Object.keys(v).sort();\n  const pojo: Value[] = keys.map((k) => [k, v[k]!]);\n  return [8, pojo.map(makeComparable)];\n}\n", "export const version = \"1.25.0\";\n"], "mappings": ";;;;;AAGO,IAAM,eAAe,OAAO,IAAI,cAAc;;;ACD9C,IAAM,kBAAkB,OAAO,IAAI,iBAAiB;AAKpD,SAAS,iBAAoB,KAAQ,OAAe;AACxD,MAAY,eAAe,IAAI;AAClC;AAEO,SAAS,qBAAqB,WAA+B;AAClE,SAAO,UAAU,eAAe,KAAK;AACvC;AAEO,SAAS,iBAAiB,GAAoB;AACnD,SAAO,EAAE,WAAW,aAAa;AACnC;AAEO,SAAS,mBAAmB,mBAAwB;AAIzD,MAAI;AAGJ,MAAI,OAAO,sBAAsB,UAAU;AACzC,QAAI,iBAAiB,iBAAiB,GAAG;AACvC,wBAAkB,EAAE,gBAAgB,kBAAkB;IACxD,OAAO;AACL,wBAAkB,EAAE,MAAM,kBAAkB;IAC9C;EACF,WAGS,kBAAkB,YAAY,GAAG;AACxC,sBAAkB,EAAE,MAAM,kBAAkB,YAAY,EAAE;EAC5D,OAEK;AACH,UAAM,gBAAgB,qBAAqB,iBAAiB;AAC5D,QAAI,CAAC,eAAe;AAClB,YAAM,IAAI,MAAM,GAAG,iBAAiB,6BAA6B;IACnE;AACA,sBAAkB,EAAE,WAAW,cAAc;EAC/C;AACA,SAAO;AACT;;;AC8BO,SAAS,gBACd,mBACQ;AACR,QAAM,UAAU,mBAAmB,iBAAiB;AAEpD,MAAI,QAAQ,SAAS,QAAW;AAC9B,QAAI,QAAQ,mBAAmB,QAAW;AACxC,YAAM,IAAI;QACR,0GAA0G,QAAQ,cAAc;MAClI;IACF,WAAW,QAAQ,cAAc,QAAW;AAC1C,YAAM,IAAI;QACR,6HAA6H,QAAQ,SAAS;MAChJ;IACF;AACA,UAAM,IAAI;MACR,0FAA0F,KAAK,UAAU,OAAO,CAAC;IACnH;EACF;AAGA,MAAI,OAAO,sBAAsB,SAAU,QAAO;AAKlD,QAAM,OAAQ,kBAA0B,YAAY;AACpD,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,GAAG,iBAAwB,6BAA6B;EAC1E;AACA,SAAO;AACT;AAaO,SAAS,sBAId,MAA4D;AAC5D,SAAO,EAAE,CAAC,YAAY,GAAG,KAAK;AAMhC;AAYA,SAAS,UAAU,YAAsB,CAAC,GAAW;AACnD,QAAM,UAAgC;IACpC,IAAI,GAAG,MAAuB;AAC5B,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,WAAW,CAAC,GAAG,WAAW,IAAI;AACpC,eAAO,UAAU,QAAQ;MAC3B,WAAW,SAAS,cAAc;AAChC,YAAI,UAAU,SAAS,GAAG;AACxB,gBAAM,QAAQ,CAAC,OAAO,GAAG,SAAS,EAAE,KAAK,GAAG;AAC5C,gBAAM,IAAI;YACR,oFAAoF,KAAK;UAC3F;QACF;AACA,cAAM,OAAO,UAAU,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG;AAC5C,cAAM,aAAa,UAAU,UAAU,SAAS,CAAC;AACjD,YAAI,eAAe,WAAW;AAC5B,iBAAO;QACT,OAAO;AACL,iBAAO,OAAO,MAAM;QACtB;MACF,WAAW,SAAS,OAAO,aAAa;AACtC,eAAO;MACT,OAAO;AACL,eAAO;MACT;IACF;EACF;AAEA,SAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AA+HO,SAAS,UAA0B,KAAqC;AAC7E,SAAO;AACT;AA4HO,IAAM,SAAiB,UAAU;;;ACpajC,SAAS,UACd,MACuB;AACvB,MAAI,SAAS,QAAW;AACtB,WAAO,CAAC;EACV;AACA,MAAI,CAAC,eAAe,IAAI,GAAG;AACzB,UAAM,IAAI;MACR,mEACE,IACF;IACF;EACF;AACA,SAAO;AACT;AAEO,SAAS,sBAAsB,eAAuB;AAG3D,MAAI,OAAO,kBAAkB,aAAa;AACxC,UAAM,IAAI;MACR;IACF;EACF;AACA,MAAI,OAAO,kBAAkB,UAAU;AACrC,UAAM,IAAI;MACR,qCAAqC,aAAoB;IAC3D;EACF;AACA,MACE,EAAE,cAAc,WAAW,OAAO,KAAK,cAAc,WAAW,QAAQ,IACxE;AACA,UAAM,IAAI;MACR,+EAA+E,aAAa;IAC9F;EACF;AAKA,MAAI;AACF,QAAI,IAAI,aAAa;EACvB,QAAQ;AACN,UAAM,IAAI;MACR,gCAAgC,aAAa;IAC/C;EACF;AAGA,MAAI,cAAc,SAAS,cAAc,GAAG;AAC1C,UAAM,IAAI;MACR,gCAAgC,aAAa;IAC/C;EACF;AACF;AAKO,SAAS,eAAe,OAAgB;AAC7C,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,QAAM,WACJ,cAAc,QACd,cAAc,OAAO;;EAGrB,WAAW,aAAa,SAAS;AACnC,SAAO,YAAY;AACrB;;;;;;;;;AClEA,IAAI,SAAmB,CAAC;AACxB,IAAI,YAAsB,CAAC;AAC3B,IAAI,MAAM;AAEV,IAAI,OAAO;AACX,KAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC/C,SAAO,CAAC,IAAI,KAAK,CAAC;AAClB,YAAU,KAAK,WAAW,CAAC,CAAC,IAAI;AAClC;AAHS;AAAO;AAOhB,UAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAC/B,UAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAE/B,SAAS,QAAQ,KAAa;AAC5B,MAAI,MAAM,IAAI;AAEd,MAAI,MAAM,IAAI,GAAG;AACf,UAAM,IAAI,MAAM,gDAAgD;EAClE;AAIA,MAAI,WAAW,IAAI,QAAQ,GAAG;AAC9B,MAAI,aAAa,GAAI,YAAW;AAEhC,MAAI,kBAAkB,aAAa,MAAM,IAAI,IAAK,WAAW;AAE7D,SAAO,CAAC,UAAU,eAAe;AACnC;AAIO,SAAS,WAAW,KAAqB;AAC9C,MAAI,OAAO,QAAQ,GAAG;AACtB,MAAI,WAAW,KAAK,CAAC;AACrB,MAAI,kBAAkB,KAAK,CAAC;AAC5B,UAAS,WAAW,mBAAmB,IAAK,IAAI;AAClD;AAEA,SAAS,YAAY,MAAc,UAAkB,iBAAyB;AAC5E,UAAS,WAAW,mBAAmB,IAAK,IAAI;AAClD;AAGO,SAAS,YAAY,KAAyB;AACnD,MAAI;AACJ,MAAI,OAAO,QAAQ,GAAG;AACtB,MAAI,WAAW,KAAK,CAAC;AACrB,MAAI,kBAAkB,KAAK,CAAC;AAE5B,MAAIA,OAAM,IAAI,IAAI,YAAY,KAAK,UAAU,eAAe,CAAC;AAE7D,MAAI,UAAU;AAGd,MAAI,MAAM,kBAAkB,IAAI,WAAW,IAAI;AAE/C,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC3B,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,KACpC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,IACrC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC;AACjC,IAAAA,KAAI,SAAS,IAAK,OAAO,KAAM;AAC/B,IAAAA,KAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,IAAAA,KAAI,SAAS,IAAI,MAAM;EACzB;AAEA,MAAI,oBAAoB,GAAG;AACzB,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,IAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK;AACvC,IAAAA,KAAI,SAAS,IAAI,MAAM;EACzB;AAEA,MAAI,oBAAoB,GAAG;AACzB,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,IACpC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK;AACvC,IAAAA,KAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,IAAAA,KAAI,SAAS,IAAI,MAAM;EACzB;AAEA,SAAOA;AACT;AAEA,SAAS,gBAAgB,KAAa;AACpC,SACE,OAAQ,OAAO,KAAM,EAAI,IACzB,OAAQ,OAAO,KAAM,EAAI,IACzB,OAAQ,OAAO,IAAK,EAAI,IACxB,OAAO,MAAM,EAAI;AAErB;AAEA,SAAS,YAAY,OAAmB,OAAe,KAAa;AAClE,MAAI;AACJ,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,OAAO,IAAI,KAAK,KAAK,GAAG;AACnC,WACI,MAAM,CAAC,KAAK,KAAM,aAClB,MAAM,IAAI,CAAC,KAAK,IAAK,UACtB,MAAM,IAAI,CAAC,IAAI;AAClB,WAAO,KAAK,gBAAgB,GAAG,CAAC;EAClC;AACA,SAAO,OAAO,KAAK,EAAE;AACvB;AAGO,SAAS,cAAc,OAA2B;AACvD,MAAI;AACJ,MAAI,MAAM,MAAM;AAChB,MAAI,aAAa,MAAM;AACvB,MAAI,QAAQ,CAAC;AACb,MAAI,iBAAiB;AAGrB,WAAS,IAAI,GAAG,OAAO,MAAM,YAAY,IAAI,MAAM,KAAK,gBAAgB;AACtE,UAAM;MACJ;QACE;QACA;QACA,IAAI,iBAAiB,OAAO,OAAO,IAAI;MACzC;IACF;EACF;AAGA,MAAI,eAAe,GAAG;AACpB,UAAM,MAAM,MAAM,CAAC;AACnB,UAAM,KAAK,OAAO,OAAO,CAAC,IAAI,OAAQ,OAAO,IAAK,EAAI,IAAI,IAAI;EAChE,WAAW,eAAe,GAAG;AAC3B,WAAO,MAAM,MAAM,CAAC,KAAK,KAAK,MAAM,MAAM,CAAC;AAC3C,UAAM;MACJ,OAAO,OAAO,EAAE,IACd,OAAQ,OAAO,IAAK,EAAI,IACxB,OAAQ,OAAO,IAAK,EAAI,IACxB;IACJ;EACF;AAEA,SAAO,MAAM,KAAK,EAAE;AACtB;;;ACjJA,IAAM,gBAAgB;AAEtB,IAAM,YAAY,OAAO,sBAAsB;AAC/C,IAAM,YAAY,OAAO,qBAAqB;AAC9C,IAAM,OAAO,OAAO,GAAG;AACvB,IAAM,QAAQ,OAAO,GAAG;AACxB,IAAM,cAAc,OAAO,KAAK;AAkEhC,SAAS,UAAU,GAAW;AAC5B,SAAO,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,KAAK,OAAO,GAAG,GAAG,EAAE;AAClE;AAEO,SAAS,mBAAmB,OAAuB;AAExD,MAAI,QAAQ,MAAM;AAChB,aAAS,YAAY;EACvB;AACA,MAAI,MAAM,MAAM,SAAS,EAAE;AAC3B,MAAI,IAAI,SAAS,MAAM,EAAG,OAAM,MAAM;AAEtC,QAAM,QAAQ,IAAI,WAAW,IAAI,YAAY,CAAC,CAAC;AAC/C,MAAI,IAAI;AACR,aAAW,WAAW,IAAI,MAAM,OAAO,EAAG,QAAQ,GAAG;AACnD,UAAM,IAAI,CAAC,SAAS,SAAS,EAAE,CAAC,GAAG,GAAG;AACtC,cAAU;EACZ;AACA,SAAc,cAAc,KAAK;AACnC;AAEO,SAAS,mBAAmB,SAAyB;AAC1D,QAAM,eAAsB,YAAY,OAAO;AAC/C,MAAI,aAAa,eAAe,GAAG;AACjC,UAAM,IAAI;MACR,YAAY,aAAa,UAAU;IACrC;EACF;AACA,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,aAAW,QAAQ,cAAc;AAC/B,aAAS,OAAO,IAAI,IAAI,eAAe;AACvC;EACF;AACA,MAAI,QAAQ,WAAW;AACrB,aAAS,YAAY;EACvB;AACA,SAAO;AACT;AAEO,SAAS,qBAAqB,OAAuB;AAC1D,MAAI,QAAQ,aAAa,YAAY,OAAO;AAC1C,UAAM,IAAI;MACR,UAAU,KAAK;IACjB;EACF;AACA,QAAM,SAAS,IAAI,YAAY,CAAC;AAChC,MAAI,SAAS,MAAM,EAAE,YAAY,GAAG,OAAO,IAAI;AAC/C,SAAc,cAAc,IAAI,WAAW,MAAM,CAAC;AACpD;AAEO,SAAS,qBAAqB,SAAyB;AAC5D,QAAM,eAAsB,YAAY,OAAO;AAC/C,MAAI,aAAa,eAAe,GAAG;AACjC,UAAM,IAAI;MACR,YAAY,aAAa,UAAU;IACrC;EACF;AACA,QAAM,eAAe,IAAI,SAAS,aAAa,MAAM;AACrD,SAAO,aAAa,YAAY,GAAG,IAAI;AACzC;AAGO,IAAM,iBAAkB,SAAS,UAAkB,cACtD,uBACA;AACG,IAAM,iBAAkB,SAAS,UAAkB,cACtD,uBACA;AAEJ,IAAM,qBAAqB;AAE3B,SAAS,oBAAoB,GAAW;AACtC,MAAI,EAAE,SAAS,oBAAoB;AACjC,UAAM,IAAI;MACR,cAAc,CAAC,sCAAsC,kBAAkB;IACzE;EACF;AACA,MAAI,EAAE,WAAW,GAAG,GAAG;AACrB,UAAM,IAAI,MAAM,cAAc,CAAC,wCAAwC;EACzE;AACA,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,UAAM,WAAW,EAAE,WAAW,CAAC;AAE/B,QAAI,WAAW,MAAM,YAAY,KAAK;AACpC,YAAM,IAAI;QACR,cAAc,CAAC,2BAA2B,EAAE,CAAC,CAAC;MAChD;IACF;EACF;AACF;AAcO,SAAS,aAAa,OAAyB;AACpD,MAAI,UAAU,MAAM;AAClB,WAAO;EACT;AACA,MAAI,OAAO,UAAU,WAAW;AAC9B,WAAO;EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,IAAI,CAACC,WAAU,aAAaA,MAAK,CAAC;EACjD;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,MAAM,sBAAsB,KAAY,EAAE;EACtD;AACA,QAAM,UAAU,OAAO,QAAQ,KAAK;AACpC,MAAI,QAAQ,WAAW,GAAG;AACxB,UAAM,MAAM,QAAQ,CAAC,EAAE,CAAC;AACxB,QAAI,QAAQ,UAAU;AACpB,UAAI,OAAO,MAAM,WAAW,UAAU;AACpC,cAAM,IAAI,MAAM,6BAA6B,KAAY,EAAE;MAC7D;AACA,aAAc,YAAY,MAAM,MAAM,EAAE;IAC1C;AACA,QAAI,QAAQ,YAAY;AACtB,UAAI,OAAO,MAAM,aAAa,UAAU;AACtC,cAAM,IAAI,MAAM,+BAA+B,KAAY,EAAE;MAC/D;AACA,aAAO,eAAe,MAAM,QAAQ;IACtC;AACA,QAAI,QAAQ,UAAU;AACpB,UAAI,OAAO,MAAM,WAAW,UAAU;AACpC,cAAM,IAAI,MAAM,6BAA6B,KAAY,EAAE;MAC7D;AACA,YAAM,aAAoB,YAAY,MAAM,MAAM;AAClD,UAAI,WAAW,eAAe,GAAG;AAC/B,cAAM,IAAI;UACR,YAAY,WAAW,UAAU;QACnC;MACF;AACA,YAAM,iBAAiB,IAAI,SAAS,WAAW,MAAM;AACrD,YAAM,QAAQ,eAAe,WAAW,GAAG,aAAa;AACxD,UAAI,CAAC,UAAU,KAAK,GAAG;AACrB,cAAM,IAAI,MAAM,SAAS,KAAK,gCAAgC;MAChE;AACA,aAAO;IACT;AACA,QAAI,QAAQ,QAAQ;AAClB,YAAM,IAAI;QACR;MACF;IACF;AACA,QAAI,QAAQ,QAAQ;AAClB,YAAM,IAAI;QACR;MACF;IACF;EACF;AACA,QAAM,MAAgC,CAAC;AACvC,aAAW,CAAC,GAAGC,EAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,wBAAoB,CAAC;AACrB,QAAI,CAAC,IAAI,aAAaA,EAAC;EACzB;AACA,SAAO;AACT;AAEO,SAAS,uBAAuB,OAAY;AACjD,SAAO,KAAK,UAAU,OAAO,CAAC,MAAMD,WAAU;AAC5C,QAAIA,WAAU,QAAW;AAMvB,aAAO;IACT;AACA,QAAI,OAAOA,WAAU,UAAU;AAE7B,aAAO,GAAGA,OAAM,SAAS,CAAC;IAC5B;AACA,WAAOA;EACT,CAAC;AACH;AAEA,SAAS,qBACP,OACA,eACA,SACA,0BACW;AACX,MAAI,UAAU,QAAW;AACvB,UAAM,cACJ,WACA,qBAAqB,OAAO,uBAAuB;MACjD;IACF,CAAC;AACH,UAAM,IAAI;MACR,wCAAwC,WAAW;IACrD;EACF;AACA,MAAI,UAAU,MAAM;AAClB,WAAO;EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,QAAQ,aAAa,YAAY,OAAO;AAC1C,YAAM,IAAI;QACR,UAAU,KAAK;MACjB;IACF;AACA,WAAO,EAAE,UAAU,eAAe,KAAK,EAAE;EAC3C;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,UAAU,KAAK,GAAG;AACpB,YAAM,SAAS,IAAI,YAAY,CAAC;AAChC,UAAI,SAAS,MAAM,EAAE,WAAW,GAAG,OAAO,aAAa;AACvD,aAAO,EAAE,QAAe,cAAc,IAAI,WAAW,MAAM,CAAC,EAAE;IAChE,OAAO;AACL,aAAO;IACT;EACF;AACA,MAAI,OAAO,UAAU,WAAW;AAC9B,WAAO;EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AACA,MAAI,iBAAiB,aAAa;AAChC,WAAO,EAAE,QAAe,cAAc,IAAI,WAAW,KAAK,CAAC,EAAE;EAC/D;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM;MAAI,CAACA,QAAO,MACvB,qBAAqBA,QAAO,eAAe,UAAU,IAAI,CAAC,KAAK,KAAK;IACtE;EACF;AACA,MAAI,iBAAiB,KAAK;AACxB,UAAM,IAAI;MACR,+BAA+B,SAAS,OAAO,CAAC,GAAG,KAAK,GAAG,aAAa;IAC1E;EACF;AACA,MAAI,iBAAiB,KAAK;AACxB,UAAM,IAAI;MACR,+BAA+B,SAAS,OAAO,CAAC,GAAG,KAAK,GAAG,aAAa;IAC1E;EACF;AAEA,MAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,UAAM,UAAU,OAAO,aAAa;AACpC,UAAM,WAAW,UAAU,GAAG,OAAO,MAAM;AAC3C,UAAM,IAAI;MACR,+BAA+B,SAAS,UAAU,OAAO,aAAa;IACxE;EACF;AAEA,QAAM,MAAoC,CAAC;AAC3C,QAAM,UAAU,OAAO,QAAQ,KAAK;AACpC,UAAQ,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,MAAO,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK,CAAE;AACzE,aAAW,CAAC,GAAGC,EAAC,KAAK,SAAS;AAC5B,QAAIA,OAAM,QAAW;AACnB,0BAAoB,CAAC;AACrB,UAAI,CAAC,IAAI,qBAAqBA,IAAG,eAAe,UAAU,IAAI,CAAC,IAAI,KAAK;IAC1E,WAAW,0BAA0B;AACnC,0BAAoB,CAAC;AACrB,UAAI,CAAC,IAAI;QACPA;QACA;QACA,UAAU,IAAI,CAAC;MACjB;IACF;EACF;AACA,SAAO;AACT;AAEA,SAAS,+BACP,SACA,UACA,OACA,eACA;AACA,MAAI,SAAS;AACX,WAAO,GAAG,QAAQ,GAAG;MACnB;IACF,CAAC,oDAAoD,OAAO,uBAAuB;MACjF;IACF,CAAC;EACH,OAAO;AACL,WAAO,GAAG,QAAQ,GAAG;MACnB;IACF,CAAC;EACH;AACF;AAIA,SAAS,gCACP,OACA,eACA,SACW;AACX,MAAI,UAAU,QAAW;AACvB,WAAO,EAAE,YAAY,KAAK;EAC5B,OAAO;AACL,QAAI,kBAAkB,QAAW;AAE/B,YAAM,IAAI;QACR,uCAAuC;UACrC;QACF,CAAC;MACH;IACF;AACA,WAAO,qBAAqB,OAAO,eAAe,SAAS,KAAK;EAClE;AACF;AAcO,SAAS,aAAa,OAAyB;AACpD,SAAO,qBAAqB,OAAO,OAAO,IAAI,KAAK;AACrD;AAIO,SAAS,wBAAwB,OAAqC;AAC3E,SAAO,gCAAgC,OAAO,OAAO,EAAE;AACzD;AASO,SAAS,iBAAiB,OAAyB;AACxD,SAAO,qBAAqB,OAAO,OAAO,IAAI,IAAI;AACpD;;;;;;ACvaA,IAAe,gBAAf,MAIE;EAsBA,YAAY,EAAE,WAAW,GAA+B;AAjBxD,kBAAA,MAAS,MAAA;AAKT,kBAAA,MAAS,YAAA;AAKT,kBAAA,MAAS,YAAA;AAKT,kBAAA,MAAS,mBAAA;AAGP,SAAK,aAAa;AAClB,SAAK,oBAAoB;EAC3B;;EAEA,IAAI,WAAoB;AACtB,WAAO,KAAK,eAAe,aAAa,OAAO;EACjD;AAKF;AAKO,IAAM,MAAN,MAAM,aAGH,cAAgC;;;;EAcxC,YAAY;IACV;IACA;EACF,GAGG;AACD,UAAM,EAAE,WAAW,CAAC;AAjBtB,kBAAA,MAAS,WAAA;AAKT,kBAAA,MAAS,QAAO,IAAA;AAad,SAAK,YAAY;EACnB;;EAEA,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,MAAM,WAAW,KAAK,UAAU;EACjD;;EAEA,aAAa;AACX,WAAO,IAAI,KAAkC;MAC3C,YAAY;MACZ,WAAW,KAAK;IAClB,CAAC;EACH;AACF;AAKO,IAAM,WAAN,MAAM,kBAGH,cAAgC;EAHnC,cAAA;AAAA,UAAA,GAAA,SAAA;AAOL,kBAAA,MAAS,QAAO,SAAA;EAAA;;EAGhB,IAAI,OAAsB;AAExB,WAAO,EAAE,MAAM,SAAS;EAC1B;;EAEA,aAAa;AACX,WAAO,IAAI,UAAuC;MAChD,YAAY;IACd,CAAC;EACH;AACF;AAKO,IAAM,SAAN,MAAM,gBAGH,cAAgC;EAHnC,cAAA;AAAA,UAAA,GAAA,SAAA;AAOL,kBAAA,MAAS,QAAO,OAAA;EAAA;;EAGhB,IAAI,OAAsB;AAExB,WAAO,EAAE,MAAM,SAAS;EAC1B;;EAEA,aAAa;AACX,WAAO,IAAI,QAAqC,EAAE,YAAY,WAAW,CAAC;EAC5E;AACF;AAKO,IAAM,WAAN,MAAM,kBAGH,cAAgC;EAHnC,cAAA;AAAA,UAAA,GAAA,SAAA;AAOL,kBAAA,MAAS,QAAO,SAAA;EAAA;;EAGhB,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,KAAK,KAAK;EAC3B;;EAEA,aAAa;AACX,WAAO,IAAI,UAAuC;MAChD,YAAY;IACd,CAAC;EACH;AACF;AAKO,IAAM,SAAN,MAAM,gBAGH,cAAgC;EAHnC,cAAA;AAAA,UAAA,GAAA,SAAA;AAOL,kBAAA,MAAS,QAAO,OAAA;EAAA;;EAGhB,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,KAAK,KAAK;EAC3B;;EAEA,aAAa;AACX,WAAO,IAAI,QAAqC,EAAE,YAAY,WAAW,CAAC;EAC5E;AACF;AAKO,IAAM,UAAN,MAAM,iBAGH,cAAgC;EAHnC,cAAA;AAAA,UAAA,GAAA,SAAA;AAOL,kBAAA,MAAS,QAAO,QAAA;EAAA;;EAGhB,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,KAAK,KAAK;EAC3B;;EAEA,aAAa;AACX,WAAO,IAAI,SAAsC;MAC/C,YAAY;IACd,CAAC;EACH;AACF;AAKO,IAAM,QAAN,MAAM,eAGH,cAAgC;EAHnC,cAAA;AAAA,UAAA,GAAA,SAAA;AAOL,kBAAA,MAAS,QAAO,MAAA;EAAA;;EAGhB,IAAI,OAAsB;AACxB,WAAO,EAAE,MAAM,KAAK,KAAK;EAC3B;;EAEA,aAAa;AACX,WAAO,IAAI,OAAoC,EAAE,YAAY,WAAW,CAAC;EAC3E;AACF;AAKO,IAAM,OAAN,MAAM,cAIH,cAA4C;EAJ/C,cAAA;AAAA,UAAA,GAAA,SAAA;AAQL,kBAAA,MAAS,QAAO,KAAA;EAAA;;EAGhB,IAAI,OAAsB;AACxB,WAAO;MACL,MAAM,KAAK;IACb;EACF;;EAEA,aAAa;AACX,WAAO,IAAI,MAA+C;MACxD,YAAY;IACd,CAAC;EACH;AACF;AAKO,IAAM,UAAN,MAAM,iBAUH,cAA4C;;;;EAcpD,YAAY;IACV;IACA;EACF,GAGG;AACD,UAAM,EAAE,WAAW,CAAC;AAjBtB,kBAAA,MAAS,QAAA;AAKT,kBAAA,MAAS,QAAO,QAAA;AAad,SAAK,SAAS;EAChB;;EAEA,IAAI,OAAsB;AACxB,WAAO;MACL,MAAM,KAAK;MACX,OAAO,WAAW,OAAO;QACvB,WAAW,OAAO,QAAQ,KAAK,MAAM,EAAE,IAAI,CAAC,CAAC,GAAGC,EAAC,MAAM;UACrD;UACA;YACE,WAAWA,GAAE;YACb,UAAUA,GAAE,eAAe,aAAa,OAAO;UACjD;QACF,CAAC;MACH;IACF;EACF;;EAEA,aAAa;AACX,WAAO,IAAI,SAA0D;MACnE,YAAY;MACZ,QAAQ,KAAK;IACf,CAAC;EACH;AACF;AAKO,IAAM,WAAN,MAAM,kBAGH,cAAgC;;;;EAcxC,YAAY,EAAE,YAAY,MAAM,GAA4C;AAC1E,UAAM,EAAE,WAAW,CAAC;AAXtB,kBAAA,MAAS,OAAA;AAKT,kBAAA,MAAS,QAAO,SAAA;AAOd,SAAK,QAAQ;EACf;;EAEA,IAAI,OAAsB;AACxB,WAAO;MACL,MAAM,KAAK;MACX,OAAO,aAAa,KAAK,KAA2C;IACtE;EACF;;EAEA,aAAa;AACX,WAAO,IAAI,UAAuC;MAChD,YAAY;MACZ,OAAO,KAAK;IACd,CAAC;EACH;AACF;AAKO,IAAM,SAAN,MAAM,gBAIH,cAAgC;;;;EAcxC,YAAY;IACV;IACA;EACF,GAGG;AACD,UAAM,EAAE,WAAW,CAAC;AAjBtB,kBAAA,MAAS,SAAA;AAKT,kBAAA,MAAS,QAAO,OAAA;AAad,SAAK,UAAU;EACjB;;EAEA,IAAI,OAAsB;AACxB,WAAO;MACL,MAAM,KAAK;MACX,OAAO,KAAK,QAAQ;IACtB;EACF;;EAEA,aAAa;AACX,WAAO,IAAI,QAA8C;MACvD,YAAY;MACZ,SAAS,KAAK;IAChB,CAAC;EACH;AACF;AAKO,IAAM,UAAN,MAAM,iBAMH,cAA4C;;;;EAmBpD,YAAY;IACV;IACA;IACA;EACF,GAIG;AACD,UAAM,EAAE,WAAW,CAAC;AAxBtB,kBAAA,MAAS,KAAA;AAKT,kBAAA,MAAS,OAAA;AAKT,kBAAA,MAAS,QAAO,QAAA;AAed,QAAK,IAAI,eAAoC,YAAY;AACvD,YAAM,IAAI,MAAM,4CAA4C;IAC9D;AACA,QAAK,MAAM,eAAoC,YAAY;AACzD,YAAM,IAAI,MAAM,8CAA8C;IAChE;AACA,SAAK,MAAM;AACX,SAAK,QAAQ;EACf;;EAEA,IAAI,OAAsB;AACxB,WAAO;MACL,MAAM,KAAK;;MAEX,MAAM,KAAK,IAAI;MACf,QAAQ;QACN,WAAW,KAAK,MAAM;QACtB,UAAU;MACZ;IACF;EACF;;EAEA,aAAa;AACX,WAAO,IAAI,SAA8D;MACvE,YAAY;MACZ,KAAK,KAAK;MACV,OAAO,KAAK;IACd,CAAC;EACH;AACF;AAKO,IAAM,SAAN,MAAM,gBAKH,cAA4C;;;;EAcpD,YAAY,EAAE,YAAY,QAAQ,GAA2C;AAC3E,UAAM,EAAE,WAAW,CAAC;AAXtB,kBAAA,MAAS,SAAA;AAKT,kBAAA,MAAS,QAAO,OAAA;AAOd,SAAK,UAAU;EACjB;;EAEA,IAAI,OAAsB;AACxB,WAAO;MACL,MAAM,KAAK;MACX,OAAO,KAAK,QAAQ,IAAI,CAACA,OAAMA,GAAE,IAAI;IACvC;EACF;;EAEA,aAAa;AACX,WAAO,IAAI,QAAwC;MACjD,YAAY;MACZ,SAAS,KAAK;IAChB,CAAC;EACH;AACF;;;AC1eO,SAAS,YAAYC,IAA+B;AACzD,SAAO,CAAC,CAACA,GAAE;AACb;AAQO,SAAS,kBAGd,KAKU;AACV,MAAI,YAAY,GAAG,GAAG;AACpB,WAAO;EACT,OAAO;AACL,WAAO,EAAE,OAAO,GAAyB;EAC3C;AACF;AA2BO,IAAM,IAAI;;;;;EAKf,IAAI,CAA2B,cAAyB;AACtD,WAAO,IAAI,IAA0B;MACnC,YAAY;MACZ;IACF,CAAC;EACH;;;;EAKA,MAAM,MAAM;AACV,WAAO,IAAI,MAAM,EAAE,YAAY,WAAW,CAAC;EAC7C;;;;;;EAOA,QAAQ,MAAM;AACZ,WAAO,IAAI,SAAS,EAAE,YAAY,WAAW,CAAC;EAChD;;;;EAKA,SAAS,MAAM;AACb,WAAO,IAAI,SAAS,EAAE,YAAY,WAAW,CAAC;EAChD;;;;EAKA,QAAQ,MAAM;AACZ,WAAO,IAAI,OAAO,EAAE,YAAY,WAAW,CAAC;EAC9C;;;;EAKA,OAAO,MAAM;AACX,WAAO,IAAI,OAAO,EAAE,YAAY,WAAW,CAAC;EAC9C;;;;EAKA,SAAS,MAAM;AACb,WAAO,IAAI,SAAS,EAAE,YAAY,WAAW,CAAC;EAChD;;;;EAKA,QAAQ,MAAM;AACZ,WAAO,IAAI,QAAQ,EAAE,YAAY,WAAW,CAAC;EAC/C;;;;EAKA,OAAO,MAAM;AACX,WAAO,IAAI,OAAO,EAAE,YAAY,WAAW,CAAC;EAC9C;;;;;EAMA,SAAS,CAA+C,YAAe;AACrE,WAAO,IAAI,SAAY,EAAE,YAAY,YAAY,OAAO,QAAQ,CAAC;EACnE;;;;;EAMA,OAAO,CAA4C,YAAe;AAChE,WAAO,IAAI,OAAuB,EAAE,YAAY,YAAY,QAAQ,CAAC;EACvE;;;;;EAMA,QAAQ,CAA+B,WAAc;AACnD,WAAO,IAAI,QAA0B,EAAE,YAAY,YAAY,OAAO,CAAC;EACzE;;;;;;EAOA,QAAQ,CAIN,MACA,WACG;AACH,WAAO,IAAI,QAAuD;MAChE,YAAY;MACZ,KAAK;MACL,OAAO;IACT,CAAC;EACH;;;;;EAMA,OAAO,IAAiD,YAAe;AACrE,WAAO,IAAI,OAA6B;MACtC,YAAY;MACZ;IACF,CAAC;EACH;;;;EAKA,KAAK,MAAM;AACT,WAAO,IAAI,KAAK,EAAE,YAAY,WAAW,CAAC;EAC5C;;;;;;;;;;;;EAaA,UAAU,CAA6B,UAAa;AAClD,WAAO,MAAM,WAAW;EAC1B;AACF;;;;;;AChOA,IAAA;AAAA,IAAA;AAEA,IAAM,oBAAoB,OAAO,IAAI,aAAa;AAE3C,IAAM,cAAN,eAA+C,KAAA,OAGnD,KAAA,mBAHmD,IAAM;EAK1D,YAAY,MAAa;AACvB,UAAM,OAAO,SAAS,WAAW,OAAO,uBAAuB,IAAI,CAAC;AALtE,IAAAC,eAAA,MAAA,QAAO,aAAA;AACP,IAAAA,eAAA,MAAA,MAAA;AACA,IAAAA,eAAA,MAAC,IAAqB,IAAA;AAIpB,SAAK,OAAO;EACd;AACF;;;ACEO,SAAS,YAAY,GAAW,GAAmB;AACxD,QAAM,UAAU,EAAE;AAClB,QAAM,UAAU,EAAE;AAClB,QAAM,SAAS,KAAK,IAAI,SAAS,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,UAAU;AAC5B,UAAM,aAAa,EAAE,YAAY,CAAC;AAClC,UAAM,aAAa,EAAE,YAAY,CAAC;AAClC,QAAI,eAAe,YAAY;AAG7B,UAAI,aAAa,OAAQ,aAAa,KAAM;AAC1C,eAAO,aAAa;MACtB;AAGA,YAAMC,WAAU,UAAU,YAAY,MAAM;AAC5C,YAAMC,WAAU,UAAU,YAAY,MAAM;AAC5C,aAAO,cAAc,QAAQD,UAAS,QAAQC,QAAO;IACvD;AAEA,SAAK,wBAAwB,UAAU;EACzC;AAEA,SAAO,UAAU;AACnB;AASA,SAAS,cACP,GACA,SACA,GACA,SACA;AACA,QAAM,SAAS,KAAK,IAAI,SAAS,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,SAAS,EAAE,CAAC;AAClB,UAAM,SAAS,EAAE,CAAC;AAClB,QAAI,WAAW,QAAQ;AACrB,aAAO,SAAS;IAClB;EACF;AACA,SAAO,UAAU;AACnB;AAMO,SAAS,wBAAwB,YAAoB;AAC1D,SAAO,aAAa,QAAS,IAAI;AACnC;AAGA,IAAM,MAAM,MAAM,MAAM,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;AACnD,IAAM,SAAS,IAAI;AACnB,IAAM,SAAS,IAAI;AAOnB,SAAS,UAAU,WAAmB,OAAiB;AACrD,MAAI,YAAY,KAAM;AACpB,UAAM,CAAC,IAAI;AACX,WAAO;EACT;AAEA,MAAI;AACJ,MAAI;AAEJ,MAAI,aAAa,MAAQ;AACvB,YAAQ;AACR,aAAS;EACX,WAAW,aAAa,OAAQ;AAC9B,YAAQ;AACR,aAAS;EACX,WAAW,aAAa,SAAU;AAChC,YAAQ;AACR,aAAS;EACX,OAAO;AACL,UAAM,IAAI,MAAM,oBAAoB;EACtC;AAEA,QAAM,CAAC,KAAK,aAAc,IAAI,SAAU;AACxC,MAAI,IAAI;AACR,SAAO,QAAQ,GAAG,SAAS;AACzB,UAAM,OAAO,aAAc,KAAK,QAAQ;AACxC,UAAM,GAAG,IAAI,MAAQ,OAAO;EAC9B;AACA,SAAO;AACT;;;AC7GO,SAAS,cAAc,IAAuB,IAAuB;AAC1E,SAAO,gBAAgB,eAAe,EAAE,GAAG,eAAe,EAAE,CAAC;AAC/D;AAEA,SAAS,gBAAmB,GAAgB,GAAwB;AAClE,MAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACjB,WAAO,sBAAsB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;EACzC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AACtB,WAAO;EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAyB,IAAO,IAAe;AACtD,MAAI,OAAO,UAAa,OAAO,MAAM;AACnC,WAAO;EACT;AACA,MAAI,OAAO,OAAO,UAAU;AAC1B,QAAI,OAAO,OAAO,UAAU;AAC1B,YAAM,IAAI,MAAM,mBAAmB,EAAS,EAAE;IAChD;AACA,WAAO,eAAe,IAAI,EAAE;EAC9B;AACA,MAAI,OAAO,OAAO,UAAU;AAC1B,QAAI,OAAO,OAAO,UAAU;AAC1B,YAAM,IAAI,MAAM,mBAAmB,EAAS,EAAE;IAChD;AACA,WAAO,YAAY,IAAI,EAAE;EAC3B;AACA,MACE,OAAO,OAAO,YACd,OAAO,OAAO,aACd,OAAO,OAAO,UACd;AACA,WAAO,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI;EACxC;AACA,MAAI,CAAC,MAAM,QAAQ,EAAE,KAAK,CAAC,MAAM,QAAQ,EAAE,GAAG;AAC5C,UAAM,IAAI,MAAM,mBAAmB,EAAS,EAAE;EAChD;AACA,WAAS,IAAI,GAAG,IAAI,GAAG,UAAU,IAAI,GAAG,QAAQ,KAAK;AACnD,UAAM,MAAM,gBAAgB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACxC,QAAI,QAAQ,GAAG;AACb,aAAO;IACT;EACF;AACA,MAAI,GAAG,SAAS,GAAG,QAAQ;AACzB,WAAO;EACT;AACA,MAAI,GAAG,SAAS,GAAG,QAAQ;AACzB,WAAO;EACT;AACA,SAAO;AACT;AAEA,SAAS,eAAe,IAAY,IAAoB;AAEtD,MAAI,MAAM,EAAE,KAAK,MAAM,EAAE,GAAG;AAE1B,UAAM,UAAU,IAAI,YAAY,CAAC;AACjC,UAAM,UAAU,IAAI,YAAY,CAAC;AACjC,QAAI,SAAS,OAAO,EAAE;MAAW;MAAG;;MAAwB;IAAI;AAChE,QAAI,SAAS,OAAO,EAAE;MAAW;MAAG;;MAAwB;IAAI;AAGhE,UAAM,SAAS;MACb,IAAI,SAAS,OAAO,EAAE;QAAY;;QAAuB;MAAI;IAC/D;AACA,UAAM,SAAS;MACb,IAAI,SAAS,OAAO,EAAE;QAAY;;QAAuB;MAAI;IAC/D;AAGA,UAAM,UAAU,SAAS,yBAAyB;AAClD,UAAM,UAAU,SAAS,yBAAyB;AAGlD,QAAI,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG;AAE3B,UAAI,MAAM,EAAE,GAAG;AACb,eAAO,SAAS,KAAK;MACvB;AAEA,aAAO,SAAS,IAAI;IACtB;AAGA,QAAI,WAAW,QAAQ;AACrB,aAAO,SAAS,KAAK;IACvB;AACA,WAAO,SAAS,SAAS,KAAK,WAAW,SAAS,IAAI;EACxD;AAEA,MAAI,OAAO,GAAG,IAAI,EAAE,GAAG;AACrB,WAAO;EACT;AAEA,MAAI,OAAO,GAAG,IAAI,EAAE,GAAG;AACrB,WAAO,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE;EAC9C;AACA,MAAI,OAAO,GAAG,IAAI,EAAE,GAAG;AACrB,WAAO,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,EAAE;EAC5C;AAGA,SAAO,KAAK,KAAK,KAAK;AACxB;AAMA,SAAS,eAAeC,IAAqC;AAC3D,MAAIA,OAAM,QAAW;AACnB,WAAO,CAAC,GAAG,MAAS;EACtB;AACA,MAAIA,OAAM,MAAM;AACd,WAAO,CAAC,GAAG,IAAI;EACjB;AACA,MAAI,OAAOA,OAAM,UAAU;AACzB,WAAO,CAAC,GAAGA,EAAC;EACd;AACA,MAAI,OAAOA,OAAM,UAAU;AACzB,WAAO,CAAC,GAAGA,EAAC;EACd;AACA,MAAI,OAAOA,OAAM,WAAW;AAC1B,WAAO,CAAC,GAAGA,EAAC;EACd;AACA,MAAI,OAAOA,OAAM,UAAU;AACzB,WAAO,CAAC,GAAGA,EAAC;EACd;AACA,MAAIA,cAAa,aAAa;AAC5B,WAAO,CAAC,GAAG,MAAM,KAAK,IAAI,WAAWA,EAAC,CAAC,EAAE,IAAI,cAAc,CAAC;EAC9D;AACA,MAAI,MAAM,QAAQA,EAAC,GAAG;AACpB,WAAO,CAAC,GAAGA,GAAE,IAAI,cAAc,CAAC;EAClC;AAEA,QAAM,OAAO,OAAO,KAAKA,EAAC,EAAE,KAAK;AACjC,QAAM,OAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,GAAGA,GAAE,CAAC,CAAE,CAAC;AAChD,SAAO,CAAC,GAAG,KAAK,IAAI,cAAc,CAAC;AACrC;;;AC/IO,IAAM,UAAU;", "names": ["arr", "value", "v", "v", "v", "__publicField", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "v"]}
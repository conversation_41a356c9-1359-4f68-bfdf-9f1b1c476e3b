/**
 * 🔄 JobbLogg – Change Log
 * ------------------------
 * 📄 File: src/App.tsx
 * 📆 Date: 2025-06-26
 * 👤 Agent: AI Assistant
 *
 * ✅ Summary:
 * - Main application component with Dashboard integration
 * - Replaced default Vite content with JobbLogg Dashboard
 *
 * 🧱 Components/Functions Added:
 * - App component with full-screen layout
 * - Dashboard component integration
 * - daisyUI base styling (bg-base-100)
 *
 * 🧪 Notes or TODOs:
 * - Will need routing when adding more pages
 * - Ready for Clerk authentication wrapper
 */

import Dashboard from './pages/Dashboard/Dashboard'

function App() {
  return (
    <div className="min-h-screen bg-base-100">
      <Dashboard />
    </div>
  )
}

export default App

import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { SignedIn, SignedOut } from '@clerk/clerk-react'
import Dashboard from './pages/Dashboard/Dashboard'
import CreateProject from './pages/CreateProject/CreateProject'
import ProjectLog from './pages/ProjectLog/ProjectLog'
import ProjectDetail from './pages/ProjectDetail/ProjectDetail'
import SignIn from './pages/SignIn/SignIn'
import SignUp from './pages/SignUp/SignUp'

function App() {
  return (
    <BrowserRouter>
      <div className="min-h-screen bg-base-100">
        <Routes>
          {/* Authentication Routes - Accessible to unauthenticated users */}
          <Route path="/sign-in" element={<SignIn />} />
          <Route path="/sign-up" element={<SignUp />} />

          {/* Protected Routes - Require authentication */}
          <Route path="/" element={
            <>
              <SignedIn>
                <Dashboard />
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/create" element={
            <>
              <SignedIn>
                <CreateProject />
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/project/:projectId/details" element={
            <>
              <SignedIn>
                <ProjectDetail />
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/project/:projectId" element={
            <>
              <SignedIn>
                <ProjectLog />
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
        </Routes>
      </div>
    </BrowserRouter>
  )
}

export default App

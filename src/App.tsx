import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { SignedIn, SignedOut, RedirectToSignIn } from '@clerk/clerk-react'
import Dashboard from './pages/Dashboard/Dashboard'
import CreateProject from './pages/CreateProject/CreateProject'
import ProjectLog from './pages/ProjectLog/ProjectLog'

function App() {
  return (
    <BrowserRouter>
      <div className="min-h-screen bg-base-100">
        <Routes>
          <Route path="/" element={
            <>
              <SignedIn>
                <Dashboard />
              </SignedIn>
              <SignedOut>
                <RedirectToSignIn />
              </SignedOut>
            </>
          } />
          <Route path="/create" element={
            <>
              <SignedIn>
                <CreateProject />
              </SignedIn>
              <SignedOut>
                <RedirectToSignIn />
              </SignedOut>
            </>
          } />
          <Route path="/project/:projectId" element={
            <>
              <SignedIn>
                <ProjectLog />
              </SignedIn>
              <SignedOut>
                <RedirectToSignIn />
              </SignedOut>
            </>
          } />
        </Routes>
      </div>
    </BrowserRouter>
  )
}

export default App

// Clerk Appearance Configuration for JobbLogg
// Dynamic theme-aware configuration

// Light theme colors
const lightTheme = {
  background: '#ffffff',
  text: '#1f2937',
  textSecondary: '#6b7280',
  border: '#e5e7eb',
  shimmer: '#f3f4f6',
};

// Dark theme colors
const darkTheme = {
  background: '#1f2937',
  text: '#f9fafb',
  textSecondary: '#d1d5db',
  border: '#374151',
  shimmer: '#4b5563',
};

// Function to create appearance config based on theme
export const createClerkAppearance = (isDark = false) => {
  const colors = isDark ? darkTheme : lightTheme;

  return {
    variables: {
      // JobbLogg Brand Colors
      colorPrimary: '#1D4ED8',           // Primary blue
      colorPrimaryFocus: '#1E40AF',      // Darker blue for hover
      colorSuccess: '#10B981',           // Success green
      colorWarning: '#F59E0B',           // Warning amber
      colorDanger: '#EF4444',            // Error red

      // Typography
      fontFamily: 'Inter, system-ui, sans-serif',
      fontSize: '16px',
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },

      // Layout & Spacing
      borderRadius: '0.75rem',           // Rounded-xl equivalent
      spacingUnit: '1rem',

      // Dynamic theme colors
      colorBackground: colors.background,
      colorInputBackground: colors.background,
      colorInputText: colors.text,
      colorText: colors.text,
      colorTextSecondary: colors.textSecondary,
      colorTextOnPrimaryBackground: '#ffffff',

      // Shadows and borders
      colorBorder: colors.border,
      colorShimmer: colors.shimmer,
    },
  
  elements: {
    // Main card container
    card: {
      backgroundColor: 'hsl(var(--b1) / 1)',
      borderRadius: '1rem',
      boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
      border: '1px solid hsl(var(--b2) / 1)',
      padding: '2rem',
      maxWidth: '28rem',
      width: '100%',
    },
    
    // Header styling
    headerTitle: {
      fontSize: '1.875rem',
      fontWeight: '700',
      color: 'hsl(var(--bc) / 1)',
      textAlign: 'center',
      marginBottom: '0.5rem',
      background: 'linear-gradient(135deg, #1D4ED8 0%, #10B981 100%)',
      backgroundClip: 'text',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
    },

    headerSubtitle: {
      fontSize: '1rem',
      color: 'hsl(var(--bc) / 0.7)',
      textAlign: 'center',
      marginBottom: '2rem',
    },
    
    // Primary action button
    formButtonPrimary: {
      backgroundColor: '#1D4ED8',
      color: '#ffffff',
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '600',
      border: 'none',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.25)',
      width: '100%',
      height: '3rem',
      
      '&:hover': {
        backgroundColor: '#1E40AF',
        transform: 'scale(1.02)',
        boxShadow: '0 8px 25px 0 rgb(29 78 216 / 0.35)',
      },
      
      '&:focus': {
        outline: '2px solid #1D4ED8',
        outlineOffset: '2px',
        transform: 'scale(1.02)',
      },
      
      '&:active': {
        transform: 'scale(0.98)',
      },
    },
    
    // Secondary/outline buttons
    formButtonSecondary: {
      backgroundColor: 'transparent',
      color: 'hsl(var(--bc) / 1)',
      border: '2px solid hsl(var(--b3) / 1)',
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      width: '100%',
      height: '3rem',

      '&:hover': {
        borderColor: '#1D4ED8',
        color: '#1D4ED8',
        transform: 'scale(1.02)',
        boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.15)',
      },
    },
    
    // Social buttons (Apple, Google)
    socialButtonsBlockButton: {
      backgroundColor: 'hsl(var(--b1) / 1)',
      color: 'hsl(var(--bc) / 1)',
      border: '2px solid hsl(var(--b3) / 1)',
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      width: '100%',
      height: '3rem',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '0.75rem',
      marginBottom: '0.75rem',

      '&:hover': {
        borderColor: '#1D4ED8',
        transform: 'scale(1.02)',
        boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.15)',
      },

      '&:focus': {
        outline: '2px solid #1D4ED8',
        outlineOffset: '2px',
      },
    },
    
    // Input fields
    formFieldInput: {
      backgroundColor: 'hsl(var(--b1) / 1)',
      color: 'hsl(var(--bc) / 1)',
      border: '2px solid hsl(var(--b3) / 1)',
      borderRadius: '0.75rem',
      padding: '0.75rem 1rem',
      fontSize: '1rem',
      width: '100%',
      height: '3rem',
      transition: 'all 0.2s ease',

      '&:focus': {
        borderColor: '#1D4ED8',
        outline: 'none',
        boxShadow: '0 0 0 3px rgb(29 78 216 / 0.1)',
      },

      '&::placeholder': {
        color: 'hsl(var(--bc) / 0.5)',
      },
    },

    // Form field labels
    formFieldLabel: {
      fontSize: '0.875rem',
      fontWeight: '600',
      color: 'hsl(var(--bc) / 1)',
      marginBottom: '0.5rem',
      display: 'block',
    },
    
    // Links
    formFieldAction: {
      color: '#1D4ED8',
      fontSize: '0.875rem',
      fontWeight: '500',
      textDecoration: 'none',
      transition: 'color 0.2s ease',
      
      '&:hover': {
        color: '#1E40AF',
        textDecoration: 'underline',
      },
    },
    
    // Footer links
    footerActionLink: {
      color: '#1D4ED8',
      fontSize: '0.875rem',
      fontWeight: '500',
      textDecoration: 'none',
      transition: 'color 0.2s ease',
      
      '&:hover': {
        color: '#1E40AF',
        textDecoration: 'underline',
      },
    },
    
    // Divider
    dividerLine: {
      backgroundColor: 'hsl(var(--b3) / 1)',
      height: '1px',
      margin: '1.5rem 0',
    },

    dividerText: {
      color: 'hsl(var(--bc) / 0.6)',
      fontSize: '0.875rem',
      fontWeight: '500',
    },
    
    // Loading spinner
    spinner: {
      color: '#1D4ED8',
      width: '1.5rem',
      height: '1.5rem',
    },
    
    // Error messages
    formFieldErrorText: {
      color: '#EF4444',
      fontSize: '0.875rem',
      fontWeight: '500',
      marginTop: '0.5rem',
    },
    
    // Success messages
    formFieldSuccessText: {
      color: '#10B981',
      fontSize: '0.875rem',
      fontWeight: '500',
      marginTop: '0.5rem',
    },
    
    // Alert/notification styling
    alert: {
      backgroundColor: 'hsl(var(--b2) / 1)',
      border: '1px solid hsl(var(--b3) / 1)',
      borderRadius: '0.75rem',
      padding: '1rem',
      marginBottom: '1rem',
    },

    alertText: {
      color: 'hsl(var(--bc) / 1)',
      fontSize: '0.875rem',
      lineHeight: '1.5',
    },
  },
};

// Default appearance (light theme)
export const clerkAppearance = createClerkAppearance(false);

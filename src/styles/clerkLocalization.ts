import { Localization } from '@clerk/types';

export const norwegianLocalization: Localization = {
  locale: 'nb-NO',
  
  signIn: {
    start: {
      title: 'Velkommen tilbake! 👋',
      subtitle: 'Logg inn på JobbLogg-kontoen din og fortsett dokumenteringen',
      actionText: 'Har du ikke konto ennå?',
      actionLink: 'Opprett konto',
    },
    
    emailCode: {
      title: 'Sjekk e-posten din',
      subtitle: 'Vi har sendt en kode til {{identifier}}',
      formTitle: 'Bekreftelseskode',
      formSubtitle: 'Skriv inn koden du mottok på e-post',
      resendButton: 'Send kode på nytt',
    },
    
    emailLink: {
      title: 'Sjekk e-posten din',
      subtitle: 'Vi har sendt en innloggingslenke til {{identifier}}',
      formTitle: 'Innloggingslenke',
      formSubtitle: 'Klikk på lenken i e-posten for å logge inn',
      resendButton: 'Send lenke på nytt',
    },
    
    forgotPasswordAlternativeMethods: {
      title: 'Glemt passordet?',
      label: 'Ingen problem! Vi hjelper deg å komme inn igjen.',
      blockButton__emailCode: 'Send kode til e-post',
      blockButton__emailLink: 'Send innloggingslenke til e-post',
    },
    
    alternativeMethods: {
      title: 'Andre innloggingsmåter',
      actionLink: 'Få hjelp',
      blockButton__emailCode: 'Bruk e-postkode',
      blockButton__emailLink: 'Bruk e-postlenke',
      blockButton__password: 'Bruk passord',
    },
  },
  
  signUp: {
    start: {
      title: 'Opprett din JobbLogg-konto 🚀',
      subtitle: 'Begynn å dokumentere arbeidet ditt profesjonelt',
      actionText: 'Har du allerede en konto?',
      actionLink: 'Logg inn',
    },
    
    emailCode: {
      title: 'Bekreft e-postadressen din',
      subtitle: 'Vi har sendt en bekreftelseskode til {{identifier}}',
      formTitle: 'Bekreftelseskode',
      formSubtitle: 'Skriv inn koden du mottok på e-post',
      resendButton: 'Send kode på nytt',
    },
    
    emailLink: {
      title: 'Bekreft e-postadressen din',
      subtitle: 'Vi har sendt en bekreftelseslenke til {{identifier}}',
      formTitle: 'Bekreftelseslenke',
      formSubtitle: 'Klikk på lenken i e-posten for å bekrefte kontoen',
      resendButton: 'Send lenke på nytt',
    },
    
    continue: {
      title: 'Fullfør registreringen',
      subtitle: 'Fyll ut de siste detaljene for å komme i gang',
      actionText: 'Har du allerede en konto?',
      actionLink: 'Logg inn',
    },
  },
  
  // Form fields and labels
  formFieldLabel__emailAddress: 'E-postadresse',
  formFieldLabel__emailAddress_username: 'E-postadresse eller brukernavn',
  formFieldLabel__username: 'Brukernavn',
  formFieldLabel__firstName: 'Fornavn',
  formFieldLabel__lastName: 'Etternavn',
  formFieldLabel__password: 'Passord',
  formFieldLabel__newPassword: 'Nytt passord',
  formFieldLabel__confirmPassword: 'Bekreft passord',
  formFieldLabel__currentPassword: 'Nåværende passord',
  formFieldLabel__signOutOfOtherSessions: 'Logg ut av andre enheter',
  
  // Form field placeholders
  formFieldInputPlaceholder__emailAddress: 'Skriv inn e-postadressen din',
  formFieldInputPlaceholder__emailAddress_username: 'E-postadresse eller brukernavn',
  formFieldInputPlaceholder__username: 'Skriv inn brukernavn',
  formFieldInputPlaceholder__firstName: 'Skriv inn fornavn',
  formFieldInputPlaceholder__lastName: 'Skriv inn etternavn',
  formFieldInputPlaceholder__password: 'Skriv inn passord',
  formFieldInputPlaceholder__newPassword: 'Skriv inn nytt passord',
  formFieldInputPlaceholder__confirmPassword: 'Bekreft passordet',
  formFieldInputPlaceholder__currentPassword: 'Skriv inn nåværende passord',
  
  // Action buttons
  formButtonPrimary: 'Fortsett',
  formButtonPrimary__signIn: 'Logg inn',
  formButtonPrimary__signUp: 'Opprett konto',
  formButtonPrimary__continue: 'Fortsett',
  formButtonPrimary__finish: 'Fullfør',
  
  // Social buttons
  socialButtonsBlockButton: 'Fortsett med {{provider|titleize}}',
  
  // Divider
  dividerText: 'eller',
  
  // Footer actions
  footerActionLink__useAnotherMethod: 'Bruk en annen metode',
  footerActionLink__signUp: 'Opprett konto',
  footerActionLink__signIn: 'Logg inn',
  
  // Error messages
  formFieldError__notProvided: 'Dette feltet er påkrevd',
  formFieldError__emailAddress_invalid: 'Ugyldig e-postadresse',
  formFieldError__password_pwned: 'Dette passordet er kompromittert. Velg et annet passord.',
  formFieldError__password_too_short: 'Passordet må være minst {{length}} tegn',
  formFieldError__password_weak: 'Passordet er for svakt. Bruk en kombinasjon av bokstaver, tall og symboler.',
  formFieldError__firstName_invalid: 'Ugyldig fornavn',
  formFieldError__lastName_invalid: 'Ugyldig etternavn',
  formFieldError__username_invalid: 'Ugyldig brukernavn',
  formFieldError__username_taken: 'Dette brukernavnet er allerede tatt',
  formFieldError__emailAddress_taken: 'Denne e-postadressen er allerede registrert',
  
  // Success messages
  formFieldSuccess__signUp: 'Kontoen din er opprettet!',
  formFieldSuccess__signIn: 'Du er nå logget inn!',
  
  // Loading states
  formFieldAction__loading: 'Laster...',
  
  // Help text
  formFieldHintText__optional: '(valgfritt)',
  formFieldHintText__slug: 'Kun bokstaver, tall og bindestreker',
  
  // Verification
  verificationLinkText: 'Bekreftelseslenke',
  verificationCodeText: 'Bekreftelseskode',
  
  // Resend
  resendButton: 'Send på nytt',
  
  // Back button
  backButton: 'Tilbake',
  
  // Close button
  modalCloseButton: 'Lukk',
  
  // Breadcrumbs
  breadcrumbsItem1: 'Logg inn',
  breadcrumbsItem2: 'Velg konto',
  breadcrumbsItem3: 'Bekreft',
  
  // Alerts
  alertText: 'Hvis du fortsetter, godtar du våre vilkår og betingelser.',
  
  // Badge text
  badge__primary: 'Primær',
  badge__thisDevice: 'Denne enheten',
  badge__userDevice: 'Brukerenhet',
  badge__otherImpersonatorDevice: 'Annen enhet',
  
  // Dates
  dates: {
    previous6Days: 'Siste {{count}} dager',
    lastDay: 'I går',
    sameDay: 'I dag',
    nextDay: 'I morgen',
    next6Days: 'Neste {{count}} dager',
    numeric: '{{date}}',
  },
};

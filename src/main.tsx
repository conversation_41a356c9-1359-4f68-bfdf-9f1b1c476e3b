/**
 * 🔄 JobbLogg – Change Log
 * ------------------------
 * 📄 File: src/main.tsx
 * 📆 Date: 2025-06-26
 * 👤 Agent: AI Assistant
 *
 * ✅ Summary:
 * - Standard React application entry point
 * - Renders App component with StrictMode
 *
 * 🧱 Components/Functions Added:
 * - React root rendering setup
 * - StrictMode wrapper for development checks
 *
 * 🧪 Notes or TODOs:
 * - Standard Vite React setup, no changes needed
 * - Ready for production builds
 */

import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)

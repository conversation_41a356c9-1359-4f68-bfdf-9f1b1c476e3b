import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ConvexProvider, ConvexReactClient } from 'convex/react'
import { Clerk<PERSON>rovider } from '@clerk/clerk-react'
import './index.css'
import App from './App.tsx'

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string)
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY as string

if (!clerkPubKey) {
  throw new Error("Missing Clerk Publishable Key")
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ClerkProvider
      publishableKey={clerkPubKey}
      appearance={{
        baseTheme: undefined,
        variables: {
          colorPrimary: '#1D4ED8', // jobblogg-primary
          colorBackground: '#F3F4F6', // jobblogg-neutral
          colorInputBackground: '#FFFFFF',
          colorInputText: '#1F2937',
          colorText: '#1F2937',
          colorTextSecondary: '#6B7280',
          colorSuccess: '#10B981', // jobblogg-accent
          colorDanger: '#EF4444',
          colorWarning: '#F59E0B',
          borderRadius: '0.75rem', // rounded-xl
          fontFamily: 'Inter, system-ui, sans-serif',
          fontSize: '0.875rem',
          fontWeight: {
            normal: '400',
            medium: '500',
            semibold: '600',
            bold: '700'
          }
        },
        elements: {
          // Main containers
          card: {
            backgroundColor: '#FFFFFF',
            borderRadius: '0.75rem',
            boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
            border: 'none',
            padding: '2rem'
          },
          headerTitle: {
            fontSize: '1.5rem',
            fontWeight: '700',
            color: '#1F2937',
            marginBottom: '0.5rem'
          },
          headerSubtitle: {
            fontSize: '0.875rem',
            color: '#6B7280',
            marginBottom: '1.5rem'
          },
          // Form elements
          formButtonPrimary: {
            backgroundColor: '#1D4ED8',
            borderRadius: '0.5rem',
            fontSize: '0.875rem',
            fontWeight: '600',
            padding: '0.75rem 1.5rem',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: '#1E40AF',
              transform: 'scale(1.02)',
              boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1)'
            },
            '&:focus': {
              outline: '2px solid #1D4ED8',
              outlineOffset: '2px'
            }
          },
          formFieldInput: {
            borderRadius: '0.5rem',
            border: '1px solid #D1D5DB',
            padding: '0.75rem',
            fontSize: '0.875rem',
            transition: 'all 0.2s ease',
            '&:focus': {
              borderColor: '#1D4ED8',
              outline: '2px solid #1D4ED8',
              outlineOffset: '2px'
            }
          },
          formFieldLabel: {
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#374151',
            marginBottom: '0.5rem'
          },
          // Links and buttons
          footerActionLink: {
            color: '#1D4ED8',
            fontWeight: '500',
            textDecoration: 'none',
            '&:hover': {
              textDecoration: 'underline'
            }
          },
          dividerLine: {
            backgroundColor: '#E5E7EB',
            height: '1px',
            margin: '1.5rem 0'
          },
          dividerText: {
            color: '#6B7280',
            fontSize: '0.75rem',
            fontWeight: '500'
          },
          // Social buttons
          socialButtonsBlockButton: {
            borderRadius: '0.5rem',
            border: '1px solid #D1D5DB',
            padding: '0.75rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: '#F9FAFB',
              borderColor: '#9CA3AF',
              transform: 'scale(1.02)'
            }
          },
          // Error states
          formFieldErrorText: {
            color: '#EF4444',
            fontSize: '0.75rem',
            marginTop: '0.25rem'
          },
          // Loading states
          spinner: {
            color: '#1D4ED8'
          }
        },
        layout: {
          socialButtonsPlacement: 'bottom',
          socialButtonsVariant: 'blockButton',
          showOptionalFields: true
        }
      }}
    >
      <ConvexProvider client={convex}>
        <App />
      </ConvexProvider>
    </ClerkProvider>
  </StrictMode>,
)

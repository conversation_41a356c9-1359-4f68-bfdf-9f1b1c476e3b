@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Modern button styles */
  .btn-modern {
    @apply transition-all duration-200 transform hover:scale-105 focus:scale-105 focus:ring-2 focus:ring-primary focus:ring-opacity-50;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-xl hover:-translate-y-1;
  }

  /* Input focus styles */
  .input-modern {
    @apply transition-all duration-200 focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:border-primary;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-base-300 rounded;
  }

  /* Smooth transitions for theme switching */
  * {
    @apply transition-colors duration-200;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

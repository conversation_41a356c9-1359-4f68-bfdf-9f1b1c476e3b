import React from 'react';
import { SignUp as ClerkSignUp } from '@clerk/clerk-react';
import { Link } from 'react-router-dom';
import ThemeToggle from '../../components/ThemeToggle/ThemeToggle';

const SignUp: React.FC = () => {
  return (
    <div className="min-h-screen bg-base-200/30 animate-fade-in">
      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-screen">
        <div className="w-full max-w-md">
          {/* Theme Toggle */}
          <div className="flex justify-end mb-8">
            <ThemeToggle />
          </div>

          {/* Modern Header */}
          <div className="text-center mb-12 animate-slide-up">
            <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-accent/10 to-primary/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-base-content mb-3 bg-gradient-to-r from-accent to-primary bg-clip-text text-transparent">
              Kom i gang! 🚀
            </h1>
            <p className="text-base-content/60 text-lg">
              Opprett din JobbLogg-konto og start dokumenteringen i dag
            </p>
          </div>

          {/* Modern Clerk SignUp Component */}
          <div className="bg-base-100 rounded-xl shadow-lg p-8 animate-scale-in" style={{ animationDelay: '200ms' }}>
            <ClerkSignUp
              appearance={{
                elements: {
                  rootBox: "w-full",
                  card: "shadow-none border-none bg-transparent p-0",
                  headerTitle: "hidden",
                  headerSubtitle: "hidden",
                  socialButtonsBlockButton: "btn btn-outline btn-block mb-3 normal-case btn-modern hover:bg-accent/5 hover:border-accent/20",
                  socialButtonsBlockButtonText: "text-base-content font-medium",
                  socialButtonsBlockButtonArrow: "text-base-content/60",
                  dividerLine: "bg-base-300 h-px",
                  dividerText: "text-base-content/50 text-sm font-medium px-4 bg-base-100",
                  formButtonPrimary: "btn btn-primary btn-block normal-case btn-modern font-semibold text-base shadow-lg hover:shadow-xl",
                  formFieldInput: "input input-bordered w-full input-modern focus:border-primary focus:ring-2 focus:ring-primary/20",
                  formFieldLabel: "label-text text-base-content font-semibold mb-2 text-sm",
                  identityPreviewText: "text-base-content",
                  identityPreviewEditButton: "link link-primary font-medium hover:underline",
                  footerActionText: "text-base-content/60 text-sm",
                  footerActionLink: "link link-primary font-semibold hover:underline",
                  formResendCodeLink: "link link-primary font-medium hover:underline",
                  otpCodeFieldInput: "input input-bordered text-center input-modern focus:border-primary focus:ring-2 focus:ring-primary/20",
                  formFieldErrorText: "text-error text-sm mt-2 font-medium",
                  alertText: "text-error font-medium",
                  formFieldSuccessText: "text-success text-sm mt-2 font-medium",
                  formFieldAction: "link link-primary text-sm font-medium hover:underline",
                  formFieldHintText: "text-base-content/50 text-xs mt-1",
                  formHeaderTitle: "text-xl font-bold text-base-content mb-2",
                  formHeaderSubtitle: "text-base-content/60 text-sm mb-6"
                },
                layout: {
                  socialButtonsPlacement: "top",
                  socialButtonsVariant: "blockButton",
                  showOptionalFields: false
                }
              }}
              fallbackRedirectUrl="/"
              signInUrl="/sign-in"
            />
          </div>

          {/* Modern Sign In Link */}
          <div className="text-center mt-8 animate-slide-up" style={{ animationDelay: '400ms' }}>
            <div className="bg-base-200/30 rounded-lg p-4">
              <p className="text-base-content/70 mb-2">
                Har du allerede en konto? 👋
              </p>
              <Link
                to="/sign-in"
                className="btn btn-outline btn-modern font-semibold"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                Logg inn
              </Link>
            </div>
          </div>

          {/* Modern Footer */}
          <div className="text-center mt-8 animate-slide-up" style={{ animationDelay: '600ms' }}>
            <div className="flex items-center justify-center gap-2 text-base-content/40 text-sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>JobbLogg - Dokumenter arbeidet ditt enkelt</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;

import React, { useState, useRef } from 'react';
import { Link, useParams } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import ThemeToggle from '../../components/ThemeToggle/ThemeToggle';

const ProjectLog: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { user } = useUser();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string>('');
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch project details (for now we'll use a hardcoded approach since we don't have project fetching by ID yet)
  const projects = useQuery(api.projects.getByUser, { userId: user?.id || "" });
  const project = projects?.find(p => p._id === projectId);

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id ? { projectId: projectId as any, userId: user.id } : "skip"
  );

  // Convex mutations
  const generateUploadUrl = useMutation(api.logEntries.generateUploadUrl);
  const createLogEntry = useMutation(api.logEntries.create);

  const validateAndSetImage = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('Kun .jpg, .jpeg, .png og .webp filer er tillatt');
      return false;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Bildet kan ikke være større enn 10MB');
      return false;
    }

    setSelectedImage(file);

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setImagePreview(previewUrl);
    return true;
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      validateAndSetImage(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      validateAndSetImage(files[0]);
    }
  };

  const removeImage = () => {
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
    }
    setSelectedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setUploadProgress('');

    try {
      const formData = new FormData(e.currentTarget);
      const description = formData.get('description') as string;

      if (!user?.id || !projectId) {
        throw new Error('User not authenticated or project ID missing');
      }

      let imageId: string | undefined = undefined;

      // Upload image if one is selected
      if (selectedImage) {
        setUploadProgress('Laster opp bilde...');

        // Get upload URL from Convex
        const uploadUrl = await generateUploadUrl();

        // Upload the file to Convex storage
        const result = await fetch(uploadUrl, {
          method: 'POST',
          headers: { 'Content-Type': selectedImage.type },
          body: selectedImage,
        });

        if (!result.ok) {
          throw new Error('Failed to upload image');
        }

        const { storageId } = await result.json();
        imageId = storageId;
        setUploadProgress('Lagrer logg...');
      } else {
        setUploadProgress('Lagrer logg...');
      }

      // Create the log entry in the database
      await createLogEntry({
        projectId: projectId as any, // Type assertion needed for Convex ID
        userId: user.id,
        description,
        imageId: imageId as any // Type assertion needed for Convex ID
      });

      // Reset form
      e.currentTarget.reset();
      setSelectedImage(null);
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
      setImagePreview(null);
      setUploadProgress('');

      // Show success message
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);

    } catch (error) {
      console.error('Error creating log entry:', error);
      setUploadProgress('Feil ved lagring. Prøv igjen.');
      setTimeout(() => setUploadProgress(''), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-base-200/30">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          {/* Header Skeleton */}
          <div className="flex items-center gap-4 mb-8">
            <div className="skeleton h-10 w-10 rounded-full"></div>
            <div className="skeleton h-8 w-64"></div>
          </div>

          {/* Form Skeleton */}
          <div className="bg-base-100 rounded-xl p-8 shadow-lg">
            <div className="skeleton h-48 w-full rounded-xl mb-6"></div>
            <div className="skeleton h-20 w-full rounded-lg mb-6"></div>
            <div className="skeleton h-12 w-32 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  // Project not found with modern error state
  if (!project) {
    return (
      <div className="min-h-screen bg-base-200/30 animate-fade-in">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="flex items-center gap-4 mb-8">
            <Link
              to="/"
              className="btn btn-ghost btn-circle btn-modern hover:bg-primary/10"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <ThemeToggle />
          </div>

          <div className="bg-error/10 border border-error/20 rounded-xl p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-error/20 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-error mb-2">Prosjekt ikke funnet</h2>
            <p className="text-error/80 mb-6">Dette prosjektet eksisterer ikke eller du har ikke tilgang til det.</p>
            <Link to="/" className="btn btn-primary btn-modern">
              Tilbake til oversikt
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-200/30 animate-fade-in">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Modern Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
          <div className="flex items-center gap-4">
            <Link
              to="/"
              className="btn btn-ghost btn-circle btn-modern hover:bg-primary/10"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div className="animate-slide-up">
              <h1 className="text-3xl lg:text-4xl font-bold text-base-content bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                {project.name}
              </h1>
              <p className="text-base-content/60 mt-1 text-lg">
                📸 Legg til nytt bilde og beskrivelse
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Link
              to={`/project/${projectId}/details`}
              className="btn btn-outline btn-modern"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              Se detaljer
            </Link>
            <ThemeToggle />
          </div>
        </div>
        {/* Modern Success Alert */}
        {showSuccess && (
          <div className="bg-success/10 border border-success/20 rounded-xl p-6 mb-8 animate-scale-in">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-success/20 rounded-full flex-shrink-0">
                <svg
                  className="w-6 h-6 text-success"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-success mb-2">🎉 Logg lagret!</h3>
                <p className="text-success/80 text-sm mb-4">Bildet og beskrivelsen er lagret i prosjektet ditt.</p>
                <div className="flex flex-wrap gap-2">
                  <Link
                    to={`/project/${projectId}/details`}
                    className="btn btn-sm btn-success btn-modern"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Se prosjektdetaljer
                  </Link>
                  <Link
                    to="/"
                    className="btn btn-sm btn-outline btn-modern"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                    </svg>
                    Tilbake til oversikt
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modern Upload Progress Alert */}
        {uploadProgress && (
          <div className="bg-info/10 border border-info/20 rounded-xl p-6 mb-8 animate-scale-in">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-info/20 rounded-full">
                <svg
                  className="w-6 h-6 text-info animate-spin"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-info">Arbeider...</h3>
                <p className="text-info/80 text-sm">{uploadProgress}</p>
              </div>
            </div>
          </div>
        )}

        {/* Modern Project Log Form */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-base-100 rounded-xl shadow-lg p-8 animate-slide-up">
            <div className="mb-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-base-content mb-2">📝 Ny loggføring</h2>
              <p className="text-base-content/60">Dokumenter fremgangen med bilder og beskrivelser</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Modern Drag & Drop Image Upload */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold text-base-content">Bilde</span>
                  <span className="label-text-alt text-base-content/60">(valgfritt)</span>
                </label>

                {!imagePreview ? (
                  <div
                    className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer ${
                      isDragOver
                        ? 'border-primary bg-primary/5 scale-105'
                        : 'border-base-300 hover:border-primary/50 hover:bg-base-200/50'
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-base-content mb-2">
                      {isDragOver ? '📁 Slipp bildet her!' : '📸 Last opp bilde'}
                    </h3>
                    <p className="text-base-content/60 mb-4">
                      Dra og slipp et bilde her, eller klikk for å velge
                    </p>
                    <div className="flex items-center justify-center gap-4 text-sm text-base-content/50">
                      <span>JPG</span>
                      <span>•</span>
                      <span>PNG</span>
                      <span>•</span>
                      <span>WebP</span>
                      <span>•</span>
                      <span>Maks 10MB</span>
                    </div>
                  </div>
                ) : (
                  <div className="relative rounded-xl overflow-hidden bg-base-200 animate-scale-in">
                    <img
                      src={imagePreview}
                      alt="Forhåndsvisning"
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <button
                        type="button"
                        onClick={removeImage}
                        className="btn btn-error btn-circle btn-modern shadow-lg"
                        aria-label="Fjern bilde"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                      <p className="text-white text-sm font-medium">
                        ✅ {selectedImage?.name}
                      </p>
                      <p className="text-white/80 text-xs">
                        {selectedImage && (selectedImage.size / 1024 / 1024).toFixed(1)} MB
                      </p>
                    </div>
                  </div>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".jpg,.jpeg,.png,.webp"
                  onChange={handleImageChange}
                  className="hidden"
                />
              </div>

              {/* Description Field */}
              <div className="form-control">
                <label htmlFor="description" className="label">
                  <span className="label-text font-semibold text-base-content">Beskrivelse *</span>
                </label>
                <textarea
                  id="description"
                  name="description"
                  required
                  rows={4}
                  className="textarea textarea-bordered input-modern w-full resize-none text-base"
                  placeholder="Beskriv hva som ble gjort i dag, utfordringer, fremgang, eller andre viktige detaljer..."
                />
                <label className="label">
                  <span className="label-text-alt text-base-content/50">
                    💡 Tips: Detaljerte beskrivelser hjelper deg å følge fremgangen over tid
                  </span>
                </label>
              </div>



              {/* Modern Form Actions */}
              <div className="form-control mt-12">
                <button
                  type="submit"
                  className="btn btn-primary btn-lg btn-modern w-full shadow-lg hover:shadow-xl group mb-4"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      {uploadProgress || 'Lagrer logg...'}
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Lagre logg
                    </>
                  )}
                </button>

                <div className="flex justify-center">
                  <Link
                    to={`/project/${projectId}/details`}
                    className="btn btn-ghost btn-modern text-base-content/70 hover:text-base-content"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Se alle logger i prosjektet
                  </Link>
                </div>
              </div>
          </form>
        </div>
      </div>

        {/* Modern Existing Log Entries */}
        {logEntries && logEntries.length > 0 && (
          <div className="mt-12 max-w-2xl mx-auto">
            <div className="bg-base-100 rounded-xl shadow-lg p-8">
              <div className="flex items-center gap-3 mb-8">
                <div className="w-10 h-10 bg-gradient-to-br from-secondary/10 to-accent/10 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-base-content">📋 Tidligere logger</h2>
                  <p className="text-base-content/60 text-sm">{logEntries.length} oppføringer</p>
                </div>
              </div>

              <div className="space-y-6">
                {logEntries.map((entry, index) => (
                  <div
                    key={entry._id}
                    className="bg-base-200/30 rounded-xl p-6 card-hover animate-slide-up"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex flex-col gap-4">
                      <div className="flex justify-between items-start gap-4">
                        <p className="text-base-content leading-relaxed flex-1">{entry.description}</p>
                        <div className="flex items-center gap-2 text-sm text-base-content/60 flex-shrink-0">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span>
                            {new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
                      </div>
                      {entry.imageUrl && (
                        <div className="mt-4">
                          <img
                            src={entry.imageUrl}
                            alt="Prosjektbilde"
                            className="rounded-xl w-full h-auto max-h-96 object-cover shadow-md hover:shadow-lg transition-shadow duration-200"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Empty State for No Logs */}
        {logEntries && logEntries.length === 0 && (
          <div className="mt-12 max-w-2xl mx-auto">
            <div className="bg-base-200/30 rounded-xl p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-base-300/50 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-base-content/70 mb-2">Ingen logger ennå</h3>
              <p className="text-base-content/50">
                Dette prosjektet har ingen logger. Legg til den første loggen ovenfor! 📝
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectLog;

import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';

const ProjectLog: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { user } = useUser();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string>('');

  // Fetch project details (for now we'll use a hardcoded approach since we don't have project fetching by ID yet)
  const projects = useQuery(api.projects.getByUser, { userId: user?.id || "" });
  const project = projects?.find(p => p._id === projectId);

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id ? { projectId: projectId as any, userId: user.id } : "skip"
  );

  // Convex mutations
  const generateUploadUrl = useMutation(api.logEntries.generateUploadUrl);
  const createLogEntry = useMutation(api.logEntries.create);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        alert('Kun .jpg, .jpeg, .png og .webp filer er tillatt');
        return;
      }

      setSelectedImage(file);
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setUploadProgress('');

    try {
      const formData = new FormData(e.currentTarget);
      const description = formData.get('description') as string;

      if (!user?.id || !projectId) {
        throw new Error('User not authenticated or project ID missing');
      }

      let imageId: string | undefined = undefined;

      // Upload image if one is selected
      if (selectedImage) {
        setUploadProgress('Laster opp bilde...');

        // Get upload URL from Convex
        const uploadUrl = await generateUploadUrl();

        // Upload the file to Convex storage
        const result = await fetch(uploadUrl, {
          method: 'POST',
          headers: { 'Content-Type': selectedImage.type },
          body: selectedImage,
        });

        if (!result.ok) {
          throw new Error('Failed to upload image');
        }

        const { storageId } = await result.json();
        imageId = storageId;
        setUploadProgress('Lagrer logg...');
      } else {
        setUploadProgress('Lagrer logg...');
      }

      // Create the log entry in the database
      await createLogEntry({
        projectId: projectId as any, // Type assertion needed for Convex ID
        userId: user.id,
        description,
        imageId: imageId as any // Type assertion needed for Convex ID
      });

      // Reset form
      e.currentTarget.reset();
      setSelectedImage(null);
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
      setImagePreview(null);
      setUploadProgress('');

      // Show success message
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);

    } catch (error) {
      console.error('Error creating log entry:', error);
      setUploadProgress('Feil ved lagring. Prøv igjen.');
      setTimeout(() => setUploadProgress(''), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state
  if (projects === undefined) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex justify-center items-center min-h-[50vh]">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </div>
    );
  }

  // Project not found
  if (!project) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="mb-6">
          <Link to="/" className="btn btn-outline">
            ← Tilbake til oversikt
          </Link>
        </div>
        <div className="alert alert-error">
          <svg className="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <span>Prosjekt ikke funnet</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      {/* Back Button */}
      <div className="mb-6">
        <Link to="/" className="btn btn-outline">
          ← Tilbake til oversikt
        </Link>
      </div>

      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-base-content">Legg til logg</h1>
        <p className="text-base-content/70 mt-2">
          Prosjekt: <span className="font-semibold">{project.name}</span>
        </p>
      </div>

      {/* Success Alert */}
      {showSuccess && (
        <div className="alert alert-success mb-6">
          <svg
            className="w-6 h-6 shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full">
            <span className="font-semibold">✅ Logg lagret!</span>
            <div className="flex gap-2 mt-2 sm:mt-0">
              <Link to={`/project/${projectId}/details`} className="btn btn-sm btn-outline">
                Se prosjektdetaljer
              </Link>
              <Link to="/" className="btn btn-sm btn-outline">
                Tilbake til oversikt
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Upload Progress Alert */}
      {uploadProgress && (
        <div className="alert alert-info mb-6">
          <svg
            className="w-6 h-6 shrink-0 animate-spin"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          <span>{uploadProgress}</span>
        </div>
      )}

      {/* Project Log Form */}
      <div className="card bg-base-100 shadow-xl max-w-2xl">
        <div className="card-body">
          <div className="mb-6">
            <h2 className="card-title text-2xl mb-2">📝 Ny loggføring</h2>
            <p className="text-base-content/70">
              Legg til en beskrivelse av arbeidet som ble utført og last opp et bilde hvis ønskelig.
            </p>
          </div>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Description Field */}
            <div className="form-control">
              <label htmlFor="description" className="label">
                <span className="label-text font-semibold">Beskrivelse *</span>
              </label>
              <textarea
                id="description"
                name="description"
                required
                rows={4}
                className="textarea textarea-bordered w-full"
                placeholder="Beskriv hva som ble gjort i dag..."
              />
            </div>

            {/* Image Upload Field */}
            <div className="form-control">
              <label htmlFor="image" className="label">
                <span className="label-text font-semibold">Bilde</span>
                <span className="label-text-alt text-base-content/60">(valgfritt)</span>
              </label>
              <input
                type="file"
                id="image"
                name="image"
                accept=".jpg,.jpeg,.png,.webp"
                onChange={handleImageChange}
                className="file-input file-input-bordered w-full"
              />
              <div className="label">
                <span className="label-text-alt text-base-content/60">
                  Støttede formater: JPG, PNG, WebP
                </span>
              </div>
            </div>

            {/* Image Preview */}
            {imagePreview ? (
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold">Forhåndsvisning</span>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedImage(null);
                      if (imagePreview) {
                        URL.revokeObjectURL(imagePreview);
                      }
                      setImagePreview(null);
                      // Reset file input
                      const fileInput = document.getElementById('image') as HTMLInputElement;
                      if (fileInput) fileInput.value = '';
                    }}
                    className="btn btn-sm btn-ghost text-error"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Fjern
                  </button>
                </label>
                <div className="flex justify-center">
                  <img
                    src={imagePreview}
                    alt="Forhåndsvisning"
                    className="rounded-lg w-full max-w-xs object-cover shadow-lg"
                  />
                </div>
              </div>
            ) : (
              <div className="form-control">
                <div className="alert alert-info">
                  <svg className="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>📷 Du kan legge til et bilde for å dokumentere arbeidet visuelt (valgfritt)</span>
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="form-control mt-8">
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  type="submit"
                  className="btn btn-primary btn-lg flex-1"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Lagrer...
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Lagre logg
                    </>
                  )}
                </button>
                <Link
                  to={`/project/${projectId}/details`}
                  className="btn btn-outline btn-lg"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Se prosjektdetaljer
                </Link>
              </div>
              <div className="text-center mt-4">
                <p className="text-sm text-base-content/60">
                  💡 Tips: Du kan legge til flere bilder etter at du har lagret denne loggen
                </p>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Existing Log Entries */}
      {logEntries && logEntries.length > 0 && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title text-2xl mb-6">Tidligere logger</h2>
            <div className="space-y-6">
              {logEntries.map((entry) => (
                <div key={entry._id} className="border-b border-base-200 pb-6 last:border-b-0">
                  <div className="flex flex-col gap-4">
                    <div className="flex justify-between items-start">
                      <p className="text-base-content">{entry.description}</p>
                      <span className="text-sm text-base-content/60">
                        {new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                    {entry.imageUrl && (
                      <div className="mt-2">
                        <img
                          src={entry.imageUrl}
                          alt="Prosjektbilde"
                          className="rounded-lg max-w-full h-auto max-h-96 object-contain"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectLog;

import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const CreateProject: React.FC = () => {
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    const formData = new FormData(e.currentTarget);
    const projectData = {
      projectName: formData.get('projectName') as string,
      description: formData.get('description') as string,
    };
    
    console.log('Project data:', projectData);
    
    // Reset form
    e.currentTarget.reset();
    
    // Show success message
    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
    }, 3000);
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      {/* Back Button */}
      <div className="mb-6">
        <Link to="/" className="btn btn-outline">
          ← Tilbake til oversikt
        </Link>
      </div>

      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-base-content">Opprett nytt prosjekt</h1>
        <p className="text-base-content/70 mt-2">
          Fyll ut informasjonen nedenfor for å starte et nytt prosjekt
        </p>
      </div>

      {/* Success Alert */}
      {showSuccess && (
        <div className="alert alert-success mb-6">
          <svg 
            className="w-6 h-6 shrink-0" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
            />
          </svg>
          <span>Prosjekt opprettet!</span>
        </div>
      )}

      {/* Create Project Form */}
      <div className="card bg-base-100 shadow-xl max-w-2xl">
        <div className="card-body">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Project Name Field */}
            <div className="form-control">
              <label htmlFor="projectName" className="label">
                <span className="label-text font-semibold">Prosjektnavn *</span>
              </label>
              <input
                type="text"
                id="projectName"
                name="projectName"
                required
                className="input input-bordered w-full"
                placeholder="Skriv inn prosjektnavn..."
              />
            </div>

            {/* Description Field */}
            <div className="form-control">
              <label htmlFor="description" className="label">
                <span className="label-text font-semibold">Beskrivelse</span>
                <span className="label-text-alt text-base-content/60">(valgfritt)</span>
              </label>
              <textarea
                id="description"
                name="description"
                rows={4}
                className="textarea textarea-bordered w-full"
                placeholder="Beskriv prosjektet (valgfritt)..."
              />
            </div>

            {/* Form Actions */}
            <div className="form-control mt-8">
              <button type="submit" className="btn btn-primary">
                Opprett prosjekt
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Additional Info */}
      <div className="mt-8 text-sm text-base-content/60">
        <p>
          * Obligatoriske felt må fylles ut. Du kan alltid redigere prosjektinformasjonen senere.
        </p>
      </div>
    </div>
  );
};

export default CreateProject;

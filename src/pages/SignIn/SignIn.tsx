import React from 'react';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { Link } from 'react-router-dom';
import ThemeToggle from '../../components/ThemeToggle/ThemeToggle';

const SignIn: React.FC = () => {
  return (
    <div className="min-h-screen bg-base-200/30 animate-fade-in">
      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-screen">
        <div className="w-full max-w-md">
          {/* Theme Toggle */}
          <div className="flex justify-end mb-8">
            <ThemeToggle />
          </div>

          {/* Modern Header */}
          <div className="text-center mb-12 animate-slide-up">
            <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-base-content mb-3 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Velkommen tilbake! 👋
            </h1>
            <p className="text-base-content/60 text-lg">
              Logg inn på JobbLogg-kontoen din og fortsett dokumenteringen
            </p>
          </div>

          {/* Modern Clerk SignIn Component */}
          <div className="bg-base-100 rounded-xl shadow-lg p-8 animate-scale-in" style={{ animationDelay: '200ms' }}>
            <ClerkSignIn
              appearance={{
                elements: {
                  rootBox: "w-full",
                  card: "shadow-none border-none bg-transparent p-0",
                  headerTitle: "hidden",
                  headerSubtitle: "hidden",
                  socialButtonsBlockButton: "btn btn-outline btn-block mb-3 normal-case btn-modern hover:bg-primary/5 hover:border-primary/20",
                  socialButtonsBlockButtonText: "text-base-content font-medium",
                  socialButtonsBlockButtonArrow: "text-base-content/60",
                  dividerLine: "bg-base-300 h-px",
                  dividerText: "text-base-content/50 text-sm font-medium px-4 bg-base-100",
                  formButtonPrimary: "btn btn-primary btn-block normal-case btn-modern font-semibold text-base shadow-lg hover:shadow-xl",
                  formFieldInput: "input input-bordered w-full input-modern focus:border-primary focus:ring-2 focus:ring-primary/20",
                  formFieldLabel: "label-text text-base-content font-semibold mb-2 text-sm",
                  identityPreviewText: "text-base-content",
                  identityPreviewEditButton: "link link-primary font-medium hover:underline",
                  footerActionText: "text-base-content/60 text-sm",
                  footerActionLink: "link link-primary font-semibold hover:underline",
                  formResendCodeLink: "link link-primary font-medium hover:underline",
                  otpCodeFieldInput: "input input-bordered text-center input-modern focus:border-primary focus:ring-2 focus:ring-primary/20",
                  formFieldErrorText: "text-error text-sm mt-2 font-medium",
                  alertText: "text-error font-medium",
                  formFieldSuccessText: "text-success text-sm mt-2 font-medium",
                  formFieldAction: "link link-primary text-sm font-medium hover:underline",
                  formFieldHintText: "text-base-content/50 text-xs mt-1",
                  formHeaderTitle: "text-xl font-bold text-base-content mb-2",
                  formHeaderSubtitle: "text-base-content/60 text-sm mb-6"
                },
                layout: {
                  socialButtonsPlacement: "top",
                  socialButtonsVariant: "blockButton",
                  showOptionalFields: false
                }
              }}
              fallbackRedirectUrl="/"
              signUpUrl="/sign-up"
            />
          </div>

          {/* Modern Sign Up Link */}
          <div className="text-center mt-8 animate-slide-up" style={{ animationDelay: '400ms' }}>
            <div className="bg-base-200/30 rounded-lg p-4">
              <p className="text-base-content/70 mb-2">
                Har du ikke konto ennå? 🚀
              </p>
              <Link
                to="/sign-up"
                className="btn btn-outline btn-modern font-semibold"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
                Opprett konto
              </Link>
            </div>
          </div>

          {/* Modern Footer */}
          <div className="text-center mt-8 animate-slide-up" style={{ animationDelay: '600ms' }}>
            <div className="flex items-center justify-center gap-2 text-base-content/40 text-sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>JobbLogg - Dokumenter arbeidet ditt enkelt</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignIn;

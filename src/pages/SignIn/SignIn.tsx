import React from 'react';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { Link } from 'react-router-dom';

const SignIn: React.FC = () => {
  return (
    <div className="min-h-screen bg-base-100 flex items-center justify-center px-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-base-content mb-2">
            Velkommen tilbake
          </h1>
          <p className="text-base-content/70">
            Logg inn på JobbLogg-kontoen din
          </p>
        </div>

        {/* Clerk SignIn Component */}
        <div className="card bg-base-100 shadow-xl border border-base-200">
          <div className="card-body p-0">
            <ClerkSignIn
              appearance={{
                elements: {
                  rootBox: "w-full",
                  card: "shadow-none border-none bg-transparent",
                  headerTitle: "hidden",
                  headerSubtitle: "hidden",
                  socialButtonsBlockButton: "btn btn-outline btn-block mb-2 normal-case",
                  socialButtonsBlockButtonText: "text-base-content",
                  dividerLine: "bg-base-300",
                  dividerText: "text-base-content/60 text-sm",
                  formButtonPrimary: "btn btn-primary btn-block normal-case",
                  formFieldInput: "input input-bordered w-full",
                  formFieldLabel: "label-text text-base-content font-medium",
                  identityPreviewText: "text-base-content",
                  identityPreviewEditButton: "link link-primary",
                  footerActionText: "text-base-content/70",
                  footerActionLink: "link link-primary",
                  formResendCodeLink: "link link-primary",
                  otpCodeFieldInput: "input input-bordered text-center",
                  formFieldErrorText: "text-error text-sm mt-1",
                  alertText: "text-error",
                  formFieldSuccessText: "text-success text-sm mt-1"
                },
                layout: {
                  socialButtonsPlacement: "top",
                  showOptionalFields: false
                }
              }}
              redirectUrl="/"
              signUpUrl="/sign-up"
            />
          </div>
        </div>

        {/* Sign Up Link */}
        <div className="text-center mt-6">
          <p className="text-base-content/70">
            Har du ikke konto ennå?{' '}
            <Link to="/sign-up" className="link link-primary font-medium">
              Opprett konto
            </Link>
          </p>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-base-content/50 text-sm">
            JobbLogg - Dokumenter arbeidet ditt enkelt
          </p>
        </div>
      </div>
    </div>
  );
};

export default SignIn;

import React from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';

const ProjectDetail: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { user } = useUser();

  // Fetch project details
  const project = useQuery(
    api.projects.getById,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Loading state
  if (project === undefined || logEntries === undefined) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex justify-center items-center min-h-[50vh]">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </div>
    );
  }

  // Project not found or unauthorized
  if (!project || project.userId !== user?.id) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-error mb-4">Prosjekt ikke funnet</h1>
          <p className="text-base-content/70 mb-6">
            Dette prosjektet eksisterer ikke eller du har ikke tilgang til det.
          </p>
          <Link to="/" className="btn btn-primary">
            Tilbake til oversikt
          </Link>
        </div>
      </div>
    );
  }

  // Sort log entries by creation date (newest first)
  const sortedLogEntries = logEntries.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      {/* Header with Back Button */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div className="flex items-center gap-4">
          <Link to="/" className="btn btn-outline btn-sm">
            ← Tilbake
          </Link>
          <h1 className="text-3xl font-bold text-base-content">{project.name}</h1>
        </div>
        <div className="flex gap-2">
          <Link 
            to={`/project/${projectId}`} 
            className="btn btn-primary btn-sm"
          >
            + Legg til bilde
          </Link>
        </div>
      </div>

      {/* Project Information Card */}
      <div className="card bg-base-100 shadow-xl mb-8">
        <div className="card-body">
          <h2 className="card-title text-xl mb-4">Prosjektinformasjon</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Project Details */}
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-base-content mb-2">Beskrivelse</h3>
                <p className="text-base-content/70">
                  {project.description || 'Ingen beskrivelse tilgjengelig'}
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold text-base-content mb-2">Opprettet</h3>
                <p className="text-base-content/70">
                  {new Date(project.createdAt).toLocaleDateString('nb-NO', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>

            {/* Project Statistics */}
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-base-content mb-2">Statistikk</h3>
                <div className="stats stats-vertical shadow">
                  <div className="stat">
                    <div className="stat-title">Totalt bilder</div>
                    <div className="stat-value text-primary">{sortedLogEntries.length}</div>
                    <div className="stat-desc">Dokumenterte oppføringer</div>
                  </div>
                  
                  <div className="stat">
                    <div className="stat-title">Siste aktivitet</div>
                    <div className="stat-value text-sm">
                      {sortedLogEntries.length > 0 
                        ? new Date(sortedLogEntries[0].createdAt).toLocaleDateString('nb-NO', { 
                            day: 'numeric', 
                            month: 'short' 
                          })
                        : 'Ingen aktivitet'
                      }
                    </div>
                    <div className="stat-desc">Sist oppdatert</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Log Entries Section */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-base-content">Prosjektlogg</h2>
          {sortedLogEntries.length > 0 && (
            <span className="badge badge-primary">
              {sortedLogEntries.length} oppføring{sortedLogEntries.length !== 1 ? 'er' : ''}
            </span>
          )}
        </div>

        {sortedLogEntries.length === 0 ? (
          /* Empty State */
          <div className="card bg-base-100 shadow-xl border-2 border-dashed border-base-300">
            <div className="card-body items-center text-center py-12">
              <svg
                className="w-16 h-16 text-base-300 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <h3 className="text-xl font-semibold text-base-content/70 mb-2">
                Ingen bilder lagt til ennå
              </h3>
              <p className="text-base-content/50 mb-6">
                Start dokumentering av prosjektet ved å legge til ditt første bilde
              </p>
              <Link 
                to={`/project/${projectId}`} 
                className="btn btn-primary"
              >
                + Legg til første bilde
              </Link>
            </div>
          </div>
        ) : (
          /* Log Entries Grid */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedLogEntries.map((entry) => (
              <div key={entry._id} className="card bg-base-100 shadow-xl">
                <figure className="px-4 pt-4">
                  {entry.imageUrl ? (
                    <img
                      src={entry.imageUrl}
                      alt={entry.description || 'Prosjektbilde'}
                      className="rounded-lg w-full h-48 object-cover"
                    />
                  ) : (
                    <div className="w-full h-48 bg-base-200 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-12 h-12 text-base-300"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                  )}
                </figure>
                <div className="card-body p-4">
                  <p className="text-base-content/80 text-sm">
                    {entry.description || 'Ingen beskrivelse'}
                  </p>
                  <div className="flex justify-between items-center mt-3">
                    <span className="text-xs text-base-content/60">
                      {new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                        day: 'numeric',
                        month: 'short',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Link 
          to={`/project/${projectId}`} 
          className="btn btn-primary"
        >
          + Legg til nytt bilde
        </Link>
        <Link to="/" className="btn btn-outline">
          Tilbake til oversikt
        </Link>
      </div>
    </div>
  );
};

export default ProjectDetail;

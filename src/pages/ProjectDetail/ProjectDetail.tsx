import React from 'react';
import { Link, useParams } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import ThemeToggle from '../../components/ThemeToggle/ThemeToggle';

const ProjectDetail: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { user } = useUser();

  // Fetch project details
  const project = useQuery(
    api.projects.getById,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id ? {
      projectId: projectId as any,
      userId: user.id
    } : "skip"
  );

  // Modern loading state with skeletons
  if (project === undefined || logEntries === undefined) {
    return (
      <div className="min-h-screen bg-base-200/30">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          {/* Header Skeleton */}
          <div className="flex items-center gap-4 mb-8">
            <div className="skeleton h-10 w-10 rounded-full"></div>
            <div className="skeleton h-8 w-64"></div>
            <div className="ml-auto skeleton h-10 w-32 rounded-lg"></div>
          </div>

          {/* Project Info Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="bg-base-100 rounded-xl p-8 shadow-lg">
                <div className="skeleton h-8 w-48 mb-4"></div>
                <div className="skeleton h-20 w-full mb-6"></div>
                <div className="flex gap-4">
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                </div>
              </div>
            </div>
            <div>
              <div className="bg-base-100 rounded-xl p-6 shadow-lg">
                <div className="skeleton h-6 w-24 mb-4"></div>
                <div className="space-y-3">
                  <div className="skeleton h-4 w-full"></div>
                  <div className="skeleton h-4 w-3/4"></div>
                  <div className="skeleton h-4 w-1/2"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Gallery Skeleton */}
          <div className="bg-base-100 rounded-xl p-8 shadow-lg">
            <div className="skeleton h-8 w-32 mb-6"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="skeleton h-48 w-full rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Modern error state for unauthorized/not found
  if (!project || project.userId !== user?.id) {
    return (
      <div className="min-h-screen bg-base-200/30 animate-fade-in">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="flex items-center gap-4 mb-8">
            <Link
              to="/"
              className="btn btn-ghost btn-circle btn-modern hover:bg-primary/10"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <ThemeToggle />
          </div>

          <div className="bg-error/10 border border-error/20 rounded-xl p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-error/20 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-error mb-2">Prosjekt ikke funnet</h1>
            <p className="text-error/80 mb-6">
              Dette prosjektet eksisterer ikke eller du har ikke tilgang til det.
            </p>
            <Link to="/" className="btn btn-primary btn-modern">
              Tilbake til oversikt
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Sort log entries by creation date (newest first)
  const sortedLogEntries = logEntries.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <div className="min-h-screen bg-base-200/30 animate-fade-in">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Modern Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
          <div className="flex items-center gap-4">
            <Link
              to="/"
              className="btn btn-ghost btn-circle btn-modern hover:bg-primary/10"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div className="animate-slide-up">
              <h1 className="text-3xl lg:text-4xl font-bold text-base-content bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                {project.name}
              </h1>
              <p className="text-base-content/60 mt-1 text-lg">
                📊 Prosjektdetaljer og bildesamling
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Link
              to={`/project/${projectId}`}
              className="btn btn-primary btn-modern shadow-lg hover:shadow-xl"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Legg til bilde
            </Link>
            <ThemeToggle />
          </div>
        </div>

        {/* Modern Project Information Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Main Project Info */}
          <div className="lg:col-span-2">
            <div className="bg-base-100 rounded-xl shadow-lg p-8 animate-slide-up">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-base-content">Prosjektinformasjon</h2>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold text-base-content mb-3 flex items-center gap-2">
                    <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                    </svg>
                    Beskrivelse
                  </h3>
                  <div className="bg-base-200/30 rounded-lg p-4">
                    <p className="text-base-content leading-relaxed">
                      {project.description || (
                        <span className="text-base-content/50 italic">
                          Ingen beskrivelse tilgjengelig. Du kan legge til en beskrivelse ved å redigere prosjektet.
                        </span>
                      )}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-base-content mb-3 flex items-center gap-2">
                    <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Opprettet
                  </h3>
                  <div className="bg-base-200/30 rounded-lg p-4">
                    <p className="text-base-content">
                      {new Date(project.createdAt).toLocaleDateString('nb-NO', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 mt-8 pt-6 border-t border-base-200">
                <Link
                  to={`/project/${projectId}`}
                  className="btn btn-primary btn-modern flex-1 sm:flex-none"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Legg til bilde
                </Link>
                <button className="btn btn-outline btn-modern flex-1 sm:flex-none">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Rediger prosjekt
                </button>
              </div>
            </div>
          </div>

          {/* Statistics Sidebar */}
          <div>
            <div className="bg-base-100 rounded-xl shadow-lg p-6 animate-slide-up" style={{ animationDelay: '100ms' }}>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-br from-accent/10 to-success/10 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="font-bold text-base-content">Statistikk</h3>
              </div>

              <div className="space-y-4">
                <div className="bg-primary/5 rounded-lg p-4 text-center">
                  <div className="text-3xl font-bold text-primary mb-1">
                    {sortedLogEntries.length}
                  </div>
                  <div className="text-sm text-base-content/60">Totalt bilder</div>
                </div>

                <div className="bg-secondary/5 rounded-lg p-4 text-center">
                  <div className="text-lg font-bold text-secondary mb-1">
                    {sortedLogEntries.length > 0
                      ? new Date(sortedLogEntries[0].createdAt).toLocaleDateString('nb-NO', {
                          day: 'numeric',
                          month: 'short'
                        })
                      : 'Ingen aktivitet'
                    }
                  </div>
                  <div className="text-sm text-base-content/60">Siste aktivitet</div>
                </div>

                <div className="bg-accent/5 rounded-lg p-4 text-center">
                  <div className="text-lg font-bold text-accent mb-1">
                    {Math.ceil((Date.now() - project.createdAt) / (1000 * 60 * 60 * 24))}
                  </div>
                  <div className="text-sm text-base-content/60">Dager siden oppstart</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Log Entries Section */}
        <div className="bg-base-100 rounded-xl shadow-lg p-8 animate-slide-up" style={{ animationDelay: '200ms' }}>
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-info/10 to-primary/10 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-base-content">📸 Bildesamling</h2>
                <p className="text-base-content/60 text-sm">Kronologisk oversikt over prosjektets fremgang</p>
              </div>
            </div>
            {sortedLogEntries.length > 0 && (
              <div className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium">
                {sortedLogEntries.length} bilde{sortedLogEntries.length !== 1 ? 'r' : ''}
              </div>
            )}
          </div>

          {sortedLogEntries.length === 0 ? (
            /* Modern Empty State */
            <div className="text-center py-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-base-200/50 rounded-full flex items-center justify-center">
                <svg
                  className="w-10 h-10 text-base-content/30"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-base-content/70 mb-3">
                Ingen bilder lagt til ennå
              </h3>
              <p className="text-base-content/50 mb-8 max-w-md mx-auto">
                Start dokumentering av prosjektet ved å legge til ditt første bilde.
                Bilder hjelper deg å følge fremgangen og dele resultater! 📷
              </p>
              <Link
                to={`/project/${projectId}`}
                className="btn btn-primary btn-lg btn-modern shadow-lg hover:shadow-xl"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Legg til første bilde
              </Link>
            </div>
          ) : (
            /* Modern Log Entries Grid */
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedLogEntries.map((entry, index) => (
                <div
                  key={entry._id}
                  className="bg-base-200/30 rounded-xl overflow-hidden card-hover animate-scale-in group"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="aspect-video relative overflow-hidden">
                    {entry.imageUrl ? (
                      <img
                        src={entry.imageUrl}
                        alt={entry.description || 'Prosjektbilde'}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full bg-base-300/50 flex items-center justify-center">
                        <svg
                          className="w-12 h-12 text-base-content/30"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    )}
                    <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs">
                      #{sortedLogEntries.length - index}
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-base-content leading-relaxed text-sm mb-3">
                      {entry.description || (
                        <span className="text-base-content/50 italic">Ingen beskrivelse</span>
                      )}
                    </p>
                    <div className="flex items-center justify-between text-xs text-base-content/60">
                      <div className="flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span>
                          {new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                            day: 'numeric',
                            month: 'short',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                      {entry.imageUrl && (
                        <div className="flex items-center gap-1 text-success">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span>Med bilde</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Modern Action Section */}
        {sortedLogEntries.length > 0 && (
          <div className="mt-12 text-center">
            <div className="bg-base-200/30 rounded-xl p-8">
              <h3 className="text-lg font-semibold text-base-content mb-3">
                Fortsett dokumenteringen! 🚀
              </h3>
              <p className="text-base-content/60 mb-6">
                Legg til flere bilder for å følge prosjektets fremgang over tid
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to={`/project/${projectId}`}
                  className="btn btn-primary btn-lg btn-modern shadow-lg hover:shadow-xl"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Legg til nytt bilde
                </Link>
                <Link
                  to="/"
                  className="btn btn-outline btn-lg btn-modern"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                  </svg>
                  Tilbake til oversikt
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectDetail;

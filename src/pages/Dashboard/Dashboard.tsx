import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser, UserButton } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import ThemeToggle from '../../components/ThemeToggle/ThemeToggle';

const Dashboard: React.FC = () => {
  const { user } = useUser();
  const projects = useQuery(api.projects.getByUser, { userId: user?.id || "" });

  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-base-200/30">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          {/* Header Skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
            <div className="flex items-center gap-6">
              <div className="skeleton h-12 w-64"></div>
              <div className="skeleton h-10 w-10 rounded-full"></div>
            </div>
            <div className="skeleton h-12 w-40 rounded-xl"></div>
          </div>

          {/* Stats Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-base-100 rounded-xl p-6 shadow-lg">
                <div className="skeleton h-4 w-20 mb-3"></div>
                <div className="skeleton h-8 w-16 mb-2"></div>
                <div className="skeleton h-3 w-24"></div>
              </div>
            ))}
          </div>

          {/* Projects Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-base-100 rounded-xl shadow-lg p-6">
                <div className="skeleton h-48 w-full rounded-lg mb-4"></div>
                <div className="skeleton h-6 w-3/4 mb-2"></div>
                <div className="skeleton h-4 w-full mb-4"></div>
                <div className="flex gap-2">
                  <div className="skeleton h-8 w-20"></div>
                  <div className="skeleton h-8 w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Sort projects by creation date (newest first)
  const sortedProjects = projects.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <div className="min-h-screen bg-base-200/30 animate-fade-in">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Modern Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
          <div className="flex items-center gap-6">
            <div className="animate-slide-up">
              <h1 className="text-4xl lg:text-5xl font-bold text-base-content bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Dine prosjekter
              </h1>
              <p className="text-base-content/60 mt-2 text-lg">
                Velkommen tilbake, {user?.firstName || 'Bruker'}! 👋
              </p>
            </div>
            <div className="flex items-center gap-3">
              <ThemeToggle />
              <UserButton
                afterSignOutUrl="/"
                appearance={{
                  elements: {
                    avatarBox: "w-10 h-10 rounded-full ring-2 ring-primary/20 hover:ring-primary/40 transition-all duration-200"
                  }
                }}
              />
            </div>
          </div>
          <Link
            to="/create"
            className="btn btn-primary btn-lg btn-modern shadow-lg hover:shadow-xl group"
          >
            <svg className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Nytt prosjekt
          </Link>
        </div>

        {/* Stats Section - Moved to top */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="bg-base-100 rounded-xl p-6 shadow-lg card-hover animate-scale-in">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-primary/10 rounded-xl">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">{sortedProjects.length}</div>
                <div className="text-sm text-base-content/60">Totale prosjekter</div>
              </div>
            </div>
          </div>

          <div className="bg-base-100 rounded-xl p-6 shadow-lg card-hover animate-scale-in" style={{animationDelay: '0.1s'}}>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-success/10 rounded-xl">
                <svg className="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <div className="text-2xl font-bold text-success">
                  {sortedProjects.filter(p => {
                    const projectDate = new Date(p.createdAt);
                    const now = new Date();
                    return projectDate.getMonth() === now.getMonth() &&
                           projectDate.getFullYear() === now.getFullYear();
                  }).length}
                </div>
                <div className="text-sm text-base-content/60">Denne måneden</div>
              </div>
            </div>
          </div>

          <div className="bg-base-100 rounded-xl p-6 shadow-lg card-hover animate-scale-in" style={{animationDelay: '0.2s'}}>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-info/10 rounded-xl">
                <svg className="w-6 h-6 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <div className="text-2xl font-bold text-info">
                  {sortedProjects.length > 0 ?
                    new Date(sortedProjects[0].createdAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' }) :
                    '-'
                  }
                </div>
                <div className="text-sm text-base-content/60">Siste prosjekt</div>
              </div>
            </div>
          </div>

          <div className="bg-base-100 rounded-xl p-6 shadow-lg card-hover animate-scale-in" style={{animationDelay: '0.3s'}}>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-warning/10 rounded-xl">
                <svg className="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <div className="text-2xl font-bold text-warning">0</div>
                <div className="text-sm text-base-content/60">Totalt bilder</div>
              </div>
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-base-content mb-6 flex items-center gap-3">
            <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            Prosjektoversikt
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Real Project Cards */}
            {sortedProjects.map((project, index) => (
              <div
                key={project._id}
                className="bg-base-100 rounded-xl shadow-lg card-hover animate-slide-up group"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <figure className="px-6 pt-6">
                  <div className="w-full h-48 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300">
                    <svg
                      className="w-16 h-16 text-primary/60 group-hover:text-primary transition-colors duration-300"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                </figure>
                <div className="card-body p-6">
                  <h2 className="card-title text-xl font-bold text-base-content group-hover:text-primary transition-colors duration-200">
                    {project.name}
                  </h2>
                  <p className="text-base-content/70 text-sm leading-relaxed">
                    {project.description || 'Ingen beskrivelse tilgjengelig'}
                  </p>
                  <div className="flex items-center gap-2 mt-4">
                    <div className="badge badge-primary badge-sm font-medium">Nytt</div>
                    <span className="text-xs text-base-content/60">
                      Opprettet {new Date(project.createdAt).toLocaleDateString('nb-NO')}
                    </span>
                  </div>
                  <div className="card-actions justify-end mt-6 gap-3">
                    <Link
                      to={`/project/${project._id}/details`}
                      className="btn btn-sm btn-outline btn-modern hover:btn-primary"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      Se detaljer
                    </Link>
                    <Link
                      to={`/project/${project._id}`}
                      className="btn btn-sm btn-primary btn-modern"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Legg til bilde
                    </Link>
                  </div>
                </div>
              </div>
            ))}

            {/* Modern Empty State - Only show when no projects exist */}
            {sortedProjects.length === 0 && (
              <div className="col-span-full">
                <div className="bg-base-100 rounded-xl shadow-lg border-2 border-dashed border-primary/20 p-12 text-center animate-fade-in">
                  <div className="max-w-md mx-auto">
                    <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full flex items-center justify-center">
                      <svg
                        className="w-12 h-12 text-primary"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-base-content mb-3">
                      🚀 Kom i gang med ditt første prosjekt!
                    </h3>
                    <p className="text-base-content/70 mb-6 leading-relaxed">
                      JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater.
                      Opprett ditt første prosjekt for å komme i gang.
                    </p>
                    <Link
                      to="/create"
                      className="btn btn-primary btn-lg btn-modern shadow-lg hover:shadow-xl group"
                    >
                      <svg className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Opprett ditt første prosjekt
                    </Link>
                    <div className="mt-6 flex items-center justify-center gap-6 text-sm text-base-content/50">
                      <div className="flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Last opp bilder
                      </div>
                      <div className="flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Legg til notater
                      </div>
                      <div className="flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Følg fremgang
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

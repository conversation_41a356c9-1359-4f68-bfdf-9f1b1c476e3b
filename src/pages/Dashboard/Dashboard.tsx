import React from 'react';

const Dashboard: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <h1 className="text-3xl font-bold text-base-content">Din<PERSON> prosjekter</h1>
        <button className="btn btn-primary">
          + Nytt prosjekt
        </button>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Example Project Card */}
        <div className="card bg-base-100 shadow-xl">
          <figure className="px-6 pt-6">
            <div className="w-full h-48 bg-base-200 rounded-lg flex items-center justify-center">
              <svg 
                className="w-16 h-16 text-base-300" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
                />
              </svg>
            </div>
          </figure>
          <div className="card-body">
            <h2 className="card-title text-lg">Kjøkkenrenovering - Hansen</h2>
            <p className="text-base-content/70 text-sm">
              Komplett renovering av kjøkken med nye skap, benkeplate og fliser.
            </p>
            <div className="flex items-center gap-2 mt-2">
              <div className="badge badge-primary badge-sm">Pågående</div>
              <span className="text-xs text-base-content/60">Startet 15. juni</span>
            </div>
            <div className="card-actions justify-end mt-4">
              <button className="btn btn-sm btn-outline">Se detaljer</button>
              <button className="btn btn-sm btn-primary">Legg til bilde</button>
            </div>
          </div>
        </div>

        {/* Empty State Card for New Projects */}
        <div className="card bg-base-100 shadow-xl border-2 border-dashed border-base-300">
          <div className="card-body items-center text-center">
            <svg 
              className="w-12 h-12 text-base-300 mb-4" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 6v6m0 0v6m0-6h6m-6 0H6" 
              />
            </svg>
            <h3 className="text-lg font-semibold text-base-content/70 mb-2">
              Opprett nytt prosjekt
            </h3>
            <p className="text-sm text-base-content/50 mb-4">
              Start dokumentering av ditt neste prosjekt
            </p>
            <button className="btn btn-primary btn-sm">
              + Nytt prosjekt
            </button>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="mt-12">
        <h2 className="text-xl font-semibold text-base-content mb-6">Oversikt</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-title">Aktive prosjekter</div>
            <div className="stat-value text-primary">1</div>
            <div className="stat-desc">Pågående arbeider</div>
          </div>
          
          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-title">Fullførte prosjekter</div>
            <div className="stat-value text-success">0</div>
            <div className="stat-desc">Avsluttede arbeider</div>
          </div>
          
          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-title">Totalt bilder</div>
            <div className="stat-value">0</div>
            <div className="stat-desc">Dokumenterte fremgang</div>
          </div>
          
          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-title">Denne måneden</div>
            <div className="stat-value text-info">1</div>
            <div className="stat-desc">Nye prosjekter</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

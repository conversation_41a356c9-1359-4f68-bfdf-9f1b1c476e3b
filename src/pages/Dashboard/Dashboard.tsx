import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

const Dashboard: React.FC = () => {
  const projects = useQuery(api.projects.getByUser, { userId: "user-1" });

  // Loading state
  if (projects === undefined) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex justify-center items-center min-h-[50vh]">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </div>
    );
  }

  // Sort projects by creation date (newest first)
  const sortedProjects = projects.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <h1 className="text-3xl font-bold text-base-content">Dine prosjekter</h1>
        <Link to="/create" className="btn btn-primary">
          + Nytt prosjekt
        </Link>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Real Project Cards */}
        {sortedProjects.map((project) => (
          <div key={project._id} className="card bg-base-100 shadow-xl">
            <figure className="px-6 pt-6">
              <div className="w-full h-48 bg-base-200 rounded-lg flex items-center justify-center">
                <svg
                  className="w-16 h-16 text-base-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
            </figure>
            <div className="card-body">
              <h2 className="card-title text-lg">{project.name}</h2>
              <p className="text-base-content/70 text-sm">
                {project.description || 'Ingen beskrivelse tilgjengelig'}
              </p>
              <div className="flex items-center gap-2 mt-2">
                <div className="badge badge-primary badge-sm">Nytt</div>
                <span className="text-xs text-base-content/60">
                  Opprettet {new Date(project.createdAt).toLocaleDateString('nb-NO')}
                </span>
              </div>
              <div className="card-actions justify-end mt-4">
                <button className="btn btn-sm btn-outline">Se detaljer</button>
                <button className="btn btn-sm btn-primary">Legg til bilde</button>
              </div>
            </div>
          </div>
        ))}

        {/* Empty State Card - Only show when no projects exist */}
        {sortedProjects.length === 0 && (
          <div className="card bg-base-100 shadow-xl border-2 border-dashed border-base-300">
            <div className="card-body items-center text-center">
              <svg
                className="w-12 h-12 text-base-300 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              <h3 className="text-lg font-semibold text-base-content/70 mb-2">
                Opprett ditt første prosjekt
              </h3>
              <p className="text-sm text-base-content/50 mb-4">
                Start dokumentering av ditt første prosjekt
              </p>
              <Link to="/create" className="btn btn-primary btn-sm">
                + Nytt prosjekt
              </Link>
            </div>
          </div>
        )}
      </div>

      {/* Stats Section */}
      <div className="mt-12">
        <h2 className="text-xl font-semibold text-base-content mb-6">Oversikt</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-title">Totale prosjekter</div>
            <div className="stat-value text-primary">{sortedProjects.length}</div>
            <div className="stat-desc">Alle prosjekter</div>
          </div>

          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-title">Denne måneden</div>
            <div className="stat-value text-success">
              {sortedProjects.filter(p => {
                const projectDate = new Date(p.createdAt);
                const now = new Date();
                return projectDate.getMonth() === now.getMonth() &&
                       projectDate.getFullYear() === now.getFullYear();
              }).length}
            </div>
            <div className="stat-desc">Nye prosjekter</div>
          </div>

          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-title">Siste prosjekt</div>
            <div className="stat-value text-info">
              {sortedProjects.length > 0 ?
                new Date(sortedProjects[0].createdAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' }) :
                '-'
              }
            </div>
            <div className="stat-desc">Opprettet</div>
          </div>

          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-title">Totalt bilder</div>
            <div className="stat-value">0</div>
            <div className="stat-desc">Kommer snart</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
